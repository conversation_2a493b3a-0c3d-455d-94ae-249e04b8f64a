{"name": "nivoda-qa", "version": "1.0.0", "description": "Automation Of Nivoda Website", "main": "index.js", "scripts": {"lint": "eslint --fix --quiet \"./cypress/**/*.{cy.js,jsx}\"", "lint:check": "eslint --quiet \"./cypress/**/*.{cy.js,jsx}\"", "format": "prettier --write \"./cypress/**/*.{cy.js,jsx,html,json,scss,css}\"", "format:check": "prettier -c \"./cypress/**/*.{cy.js,jsx,html,json,scss,css}\"", "e2e:record:parallel": "cypress run --record --parallel ", "cy:verify": "cypress verify", "cy:run": "cypress run", "cy:open": "cypress open", "cy:db:run": "cypress run --env grepTags=@DBALL", "cy:db:checkoutrun": "cypress run --env grepTags='@DBExpress @DBCreditNote @DBCheckout'", "e2e:checkout:record:parallel": "cypress run --spec \"cypress/e2e/{Checkout,Invoices,CreditNotes,Express}/**/*\" --record --parallel", "e2e:dashboard:record:parallel": "cypress run --spec cypress/e2e/Dashboard --record --parallel", "e2e:diamondrequest:record:parallel": "cypress run --spec cypress/e2e/DiamondRequest --record --parallel", "e2e:filter:record:parallel": "cypress run --spec cypress/e2e/Filter --record --parallel", "e2e:invoices:record:parallel": "cypress run --spec cypress/e2e/Invoices --record --parallel", "e2e:login:record:parallel": "cypress run --spec cypress/e2e/Login --record --parallel", "cy:db:Ordersrun": "cypress run --env grepTags=@DBOrders", "e2e:orders:record:parallel": "cypress run --spec cypress/e2e/Orders --record --parallel", "cy:db:productsrun": "cypress run --env grepTags=@DBProduct", "e2e:products:record:parallel": "cypress run --spec \"cypress/e2e/{Product,Shortlist}/**/*\" --record --parallel", "e2e:search:record:parallel": "cypress run --spec cypress/e2e/Search --record --parallel", "e2e:settings:record:parallel": "cypress run --spec cypress/e2e/Settings --record --parallel", "e2e:signup:record:parallel": "cypress run --spec cypress/e2e/SignUp --record --parallel", "cy:db:adminaccessrun": "cypress run --env grepTags=@DBProduction", "e2e:adminaccess:record:parallel": "cypress run --env grepTags=@Production,grepFilterSpecs=true --record --parallel", "e2e:visitnavbar:record:parallel": "cypress run --spec cypress/e2e/VisitNavBar --record --parallel", "cy:db:remainingrun": "cypress run --env grepTags=@DBRemainingCases", "e2e:remaining:record:parallel": "cypress run --spec \"cypress/e2e/{Dashboard,DiamondRequest,Filter,Login,Search,Settings,SignUp,VisitNavBar}/**/*\" --record --parallel", "e2e:smoke:record:parallel": "cypress run --env grepTags=@Smoke,grepFilterSpecs=true --record --parallel", "e2e:withOTP:record:parallel": "cypress run --env grepTags=@OTP,grepFilterSpecs=true --record --parallel", "e2e:withoutOTP:record:parallel": "cypress run --env grepTags=-@OTP+-@Production+-@DBALL+-@Skip,grepFilterSpecs=true --record --parallel"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"], "*.{js,jsx,ts,tsx,json}": ["prettier --write"]}, "author": "<PERSON>", "license": "ISC", "dependencies": {"cypress-aiotests-reporter": "^1.4.0", "cypress-downloadfile": "^1.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "faker-js": "^1.0.0", "lint-staged": "^15.2.2", "moment-timezone": "^0.5.46", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "pg": "^8.11.5", "prettier": "^3.2.5", "unique-string-generator": "^1.1.1"}, "devDependencies": {"@bahmutov/cy-grep": "^1.11.3", "@cypress/grep": "^4.1.0", "@faker-js/faker": "^8.4.1", "cypress": "^14.3.3", "cypress-file-upload": "^5.0.8", "cypress-plugin-api": "^2.11.2", "cypress-plugin-last-failed": "^1.2.0", "cypress-real-events": "^1.14.0", "dotenv": "^16.4.7", "eslint": "^9.2.0", "exceljs": "^4.4.0", "unique-names-generator": "^4.7.1", "xlsx": "^0.18.5"}}