name: Automation of Nivoda Website
on:
  push:
jobs:
  cypress-push:
    runs-on: [ubuntu-latest]
    name: <PERSON>press Run On Nivoda QA
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install OpenVPN
        run: |
          sudo apt update
          sudo apt install -y openvpn openvpn-systemd-resolved
      - name: Connect to VPN
        uses: "kota65535/github-openvpn-connect-action@v2"
        with:
          config_file: .github/workflows/client.ovpn
          username: ${{ secrets.OVPN_USERNAME }}
          password: ${{ secrets.OVPN_PASSWORD }}
         
      - name: Install npm
        uses: actions/setup-node@v4
        with: 
          node-version: '18.4.0'

      - name: Cypress run
        uses: cypress-io/github-action@v6

      - name: Upload Videos
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-videos
          path: cypress/videos
          retention-days: 10

      - name: Upload Screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          retention-days: 10
    
      