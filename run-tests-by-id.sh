#!/bin/bash

#run commands in terminal to run the file
# chmod +x run-tests-by-id.sh
# ./run-tests-by-id.sh

# List of test case IDs you want to match in describe() or it() blocks

TEST_IDS=("NE-TC-3347"
"NE-TC-3250"
"NE-TC-3210"
"NE-TC-2789"
"NE-TC-2478"
"NE-TC-2388"
"NE-TC-2140"
"NE-TC-2139"
"NE-TC-2040"
"NE-TC-2038"
"NE-TC-2037"
"NE-TC-2033"
"NE-TC-2015"
"NE-TC-2013"
"NE-TC-1550"
"NE-TC-1547"
"NE-TC-1542"
"NE-TC-688"
"NE-TC-651"



)


# Base path for Cypress specs
BASE_FOLDER="cypress/e2e"

# Array to hold matching spec file paths
MATCHING_SPECS=()

# Loop through all files and check if any contains the test case ID
for FILE in $(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \)); do
  for ID in "${TEST_IDS[@]}"; do
    if grep -q "$ID" "$FILE"; then
      MATCHING_SPECS+=("$FILE")
      break
    fi
  done
done

# Remove duplicates
UNIQUE_SPECS=($(printf "%s\n" "${MATCHING_SPECS[@]}" | sort -u))

# Convert to comma-separated list
SPEC_STRING=$(IFS=, ; echo "${UNIQUE_SPECS[*]}")

# Run Cypress with the matched specs
if [[ -n "$SPEC_STRING" ]]; then
  echo "✅ Running specs:"
  echo "$SPEC_STRING"
  npx cypress run --spec "$SPEC_STRING" --headed
else
  echo "⚠️ No matching specs found with the given test case IDs."
fi
