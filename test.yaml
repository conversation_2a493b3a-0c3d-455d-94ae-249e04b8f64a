apiVersion: batch/v1
kind: Job
metadata:
  name: cypress-parallel-tests-machine-1
  namespace: automation
spec:
  completions: NO_OF_MACHINES
  parallelism: NO_OF_MACHINES
  template:
    spec:
      restartPolicy: Never
      containers:
        - name: machine-1-cases
          image: IMAGE_REPO:latest
          resources:
            requests:
              memory: '12Gi'
              cpu: '3000m' # 3 CPU
            limits:
              memory: '12Gi'
              cpu: '3000m'
          env:
            - name: CYCLE_KEY
              value: CYCLE_KEY_VALUE
          command: ['/bin/sh', '-c']
          args:
            - |
              echo "Executing tests on Machine 1 - OTP Cases"
              CYPRESS_RECORD_KEY=5b6adac1-9a6e-483e-ab47-7d386666ff92  npx cypress run --env grepTags=@TAG_COMPONENT grepFilterSpecs=true --record --parallel --group TAG_COMPONENT --browser electron --ci-build-id CI_BUILD_ID_PLACEHOLDER --tag TAG_COMPONENT

  backoffLimit: 4
# ---
# apiVersion: batch/v1
# kind: Job
# metadata:
#   name: cypress-parallel-tests-machine-2
#   namespace: automation
# spec:
#   completions: MACHINE_COUNT_WITHOUT_OTP_CASES
#   parallelism: MACHINE_COUNT_WITHOUT_OTP_CASES
#   template:
#     spec:
#       restartPolicy: Never
#       containers:
#         - name: machine-2-without-otp-cases
#           image: 633477234672.dkr.ecr.eu-west-2.amazonaws.com/qa-automation:latest
#           resources:
#             requests:
#               memory: '8Gi'
#               cpu: '3000m'
#             limits:
#               memory: '16Gi'
#               cpu: '3500m'
#           env:
#             - name: CYCLE_KEY
#               value: 'NE-CY-58'
#           command: ['/bin/sh', '-c']
#           args:
#             - |
#               echo "Executing tests on Machine 2 - Without Otp Cases"
#               CYPRESS_RECORD_KEY=5b6adac1-9a6e-483e-ab47-7d386666ff92 npm run e2e:withoutOTP:record:parallel -- --group 'Without-OTP-Cases' --browser electron --headless --ci-build-id CI_BUILD_ID_PLACEHOLDER
#   backoffLimit: 4

# ---
# apiVersion: batch/v1
# kind: Job
# metadata:
#   name: cypress-parallel-tests-machine-3
#   namespace: automation
# spec:
#   completions: MACHINE_COUNT_ADMIN_ACCESS
#   parallelism: MACHINE_COUNT_ADMIN_ACCESS
#   template:
#     spec:
#       restartPolicy: Never
#       containers:
#         - name: machine-3-admin-access
#           image: 633477234672.dkr.ecr.eu-west-2.amazonaws.com/qa-automation:latest
#           resources:
#             requests:
#               memory: '8Gi'
#               cpu: '3000m' # 2 CPU
#             limits:
#               memory: '16Gi'
#               cpu: '3000m'
#           env:
#             - name: CYCLE_KEY
#               value: 'NE-CY-58'
#           command: ['/bin/sh', '-c']
#           args:
#             - |
#               echo "Executing tests on Machine 3 - Admin Access"
#               CYPRESS_RECORD_KEY=5b6adac1-9a6e-483e-ab47-7d386666ff92 npm run e2e:adminaccess:record:parallel -- --group 'Admin Access' --browser electron  --ci-build-id CI_BUILD_ID_PLACEHOLDER
#   backoffLimit: 4
