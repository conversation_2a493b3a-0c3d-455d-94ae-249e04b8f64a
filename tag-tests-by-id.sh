#!/bin/bash

#run commands in terminal to run the file
# chmod +x tag-tests-by-id.sh
# ./tag-tests-by-id.sh

TEST_IDS=(
  "NE-TC-2016"
  "NE-TC-2032"
  "NE-TC-1242"
)

NEW_TAG="@Fintech"
BASE_FOLDER="cypress/e2e"
FOUND_IDS=()

for FILE in $(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \)); do
  MATCHED=false
  for ID in "${TEST_IDS[@]}"; do
    if grep -qE "\b${ID}\b" "$FILE"; then
      MATCHED=true
      FOUND_IDS+=("$ID")
    fi
  done

  if [ "$MATCHED" = true ]; then
    # Create a temporary file for processing
    TMPFILE=$(mktemp)

    # Process the file with a more robust approach
    python3 -c "
import re
import sys

# Read the file
with open('$FILE', 'r') as f:
    content = f.read()

# Pattern to match existing tags
# This handles: tags: '@Production', tags: \"@Production\", tags: ['@Production'], tags: [\"@Production\"]
pattern = r'tags:\s*([\'\"@][^,}]+[\'\"]*|\[[^\]]+\])'

def replace_tags(match):
    existing_tag = match.group(1)

    # If it's already an array, extract the existing tag
    if existing_tag.startswith('['):
        # Extract existing tags from array
        array_content = re.findall(r'[\'\"]([@][^\'\"]+)[\'\"]*', existing_tag)
        if array_content:
            existing_tag_clean = array_content[0]
        else:
            existing_tag_clean = '@Production'  # fallback
    else:
        # Single tag, clean it up
        existing_tag_clean = existing_tag.strip('\'\"')

    # Return new array format with both tags
    return f'tags: [\"$NEW_TAG\", \"{existing_tag_clean}\"]'

# Replace the tags
new_content = re.sub(pattern, replace_tags, content)

# Write back to file
with open('$FILE', 'w') as f:
    f.write(new_content)
" 2>/dev/null || {
    # Fallback to sed if Python is not available
    sed -i.bak -E 's/tags: *['\''\"]*@[^'\''\",}]*/tags: [\"'$NEW_TAG'\", \"@Production\"]/g' "$FILE"
    rm -f "$FILE.bak"
}

    echo "✅ Updated tags in: $FILE"
  fi
done

for ID in "${TEST_IDS[@]}"; do
  FOUND=false
  for FOUND_ID in "${FOUND_IDS[@]}"; do
    if [ "$ID" = "$FOUND_ID" ]; then
      FOUND=true
      break
    fi
  done
  if [ "$FOUND" = false ]; then
    echo "❌ Test case not found: $ID"
  fi
done

echo "🎯 Tagging complete."
