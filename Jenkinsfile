pipeline {
  agent {
    docker {
      image 'cypress/base:20.14.0'
      label 'dev-account'
      args '-u root:sudo'
    }
  }
  stages {
    stage('Setup') {
      steps {
        echo "Running setup for build ${env.BUILD_ID} on ${env.JENKINS_URL}"
        sh 'npm ci'
        sh 'npm run cy:verify'
      }
    }
    stage('Populating Data Before Tests') {
      steps {
        echo "Populating data for build before running tests"
        sh "npm run cy:db:run"
      }
    }
    stage('Run Cypress Tests In Parallel') {
      environment {
        CYPRESS_RECORD_KEY = credentials('nivoda-cypress-record-key')
        CYPRESS_trashAssetsBeforeRuns = 'true'
      }
      parallel {
        stage('Machine 1 - Checkout') {
          environment {
            CYPRESS_GROUP_NAME = 'Checkout'
            CYCLE_KEY = 'NE-CY-36'
          }
          steps {
            echo "Executing DB tests on Machine 1 - Checkout"
            sh "npm run cy:db:checkoutrun"
            echo "Executing tests on Machine 1 - Checkout"
            sh "npm run e2e:checkout:record:parallel -- --group '${env.CYPRESS_GROUP_NAME}' --headless"
          }
        }
        stage('Machine 2 - Orders') {
          environment {
            CYPRESS_GROUP_NAME = 'Orders'
            CYCLE_KEY = 'NE-CY-36'
          }
          steps {
            echo "Executing DB tests on Machine 2 - Orders"
            sh "npm run cy:db:Ordersrun"
            echo "Executing tests on Machine 2 - Orders"
            sh "npm run e2e:orders:record:parallel -- --group '${env.CYPRESS_GROUP_NAME}' --headless"
          }
        }
        stage('Machine 3 - Remaining Tests') {
          environment {
            CYPRESS_GROUP_NAME = 'Remaining Tests'
            CYCLE_KEY = 'NE-CY-36'
          }
          steps {
            echo "Executing DB tests on Machine 3 - Remaining Tests"
            sh "npm run cy:db:remainingrun"
            echo "Executing tests on Machine 3 - Remaining Tests"
            sh "npm run e2e:remaining:record:parallel -- --group '${env.CYPRESS_GROUP_NAME}' --headless"
          }
        }
        stage('Machine 4 - Products') {
          environment {
            CYPRESS_GROUP_NAME = 'Products'
            CYCLE_KEY = 'NE-CY-36'
          }
          steps {
            echo "Executing DB tests on Machine 4 - Products"
            sh "npm run cy:db:productsrun"
            echo "Executing tests on Machine 4 - Products"
            sh "npm run e2e:products:record:parallel -- --group '${env.CYPRESS_GROUP_NAME}' --headless"
          }
        }
        stage('Machine 5 - Admin Access') {
          environment {
            CYPRESS_GROUP_NAME = 'Admin Access'
            CYCLE_KEY = 'NE-CY-50'
          }
          steps {
            echo "Executing DB tests on Machine 5 - Admin Access"
            sh "npm run cy:db:adminaccessrun"
            echo "Executing tests on Machine 5 - Admin Access"
            sh "npm run e2e:adminaccess:record:parallel -- --group '${env.CYPRESS_GROUP_NAME}' --headless"
          }
        }
      }
    }
    stage('Cleanup') {
      steps {
        echo 'Performing cleanup after tests'
        sh 'npm cache clean --force'
      }
    }
  }
  post {
    always {
      script {
        try {
          echo 'Saving test results for rerun 📝'
          sh "cp ./test-results/last-run.json /tmp/last-run-${env.NODE_NAME}.json"

          echo 'Populating data before rerunning failed tests'
          sh "npm run cy:db:run"

          echo 'Rerunning failed tests'
          def groupName = 'Rerun'

          sh """
            npx cypress-plugin-last-failed run --record --parallel --key 5b6adac1-9a6e-483e-ab47-7d386666ff92 --group '${groupName}' &
            npx cypress-plugin-last-failed run --record --parallel --key 5b6adac1-9a6e-483e-ab47-7d386666ff92 --group '${groupName}' &
            npx cypress-plugin-last-failed run --record --parallel --key 5b6adac1-9a6e-483e-ab47-7d386666ff92 --group '${groupName}' &
            npx cypress-plugin-last-failed run --record --parallel --key 5b6adac1-9a6e-483e-ab47-7d386666ff92 --group '${groupName}' &
            npx cypress-plugin-last-failed run --record --parallel --key 5b6adac1-9a6e-483e-ab47-7d386666ff92 --group '${groupName}' &
            wait
          """
        } catch (Exception e) {
          echo "Error on ${env.NODE_NAME}: ${e.getMessage()}"
        }
      }
    }
    success {
      echo 'Pipeline succeeded'
    }
    failure {
      echo 'Pipeline failed'
    }
  }
}