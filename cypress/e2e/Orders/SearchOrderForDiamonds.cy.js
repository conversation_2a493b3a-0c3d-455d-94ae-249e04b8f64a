import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('SearchOrderForDiaomonds', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Search Order Number From Diamonds (NE-TC-644)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    cy.getSearchdiamond();
    login.loginUsingApi('Searchdiamond.json');
    menu.visitOrdersPage();
    orders.searchOrderNumber('cypress/fixtures/Searchdiamond.json');
  });
});
