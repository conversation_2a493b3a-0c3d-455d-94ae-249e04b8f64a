import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersPagePORequestPageForDiamonds', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit PO Request Page For Diamonds  (NE-TC-728)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderDiamondPoRequestPage();
  });
});
