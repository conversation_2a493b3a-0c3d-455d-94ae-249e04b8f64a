import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersActionRequiredPageForGemStones', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Action Required Page For GemStones (NE-TC-716)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderGemStonesActionRequiredPage();
  });
});
