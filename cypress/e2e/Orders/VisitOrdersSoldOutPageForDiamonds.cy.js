import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersSoldOutPageForDiamonds', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Sold Out Page For Diamonds (NE-TC-712)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderDiamondSoldOutPage();
  });
});
