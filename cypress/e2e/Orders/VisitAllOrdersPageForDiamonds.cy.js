import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersPage', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Orders Page (NE-TC-709)', { tags: '@Production' }, () => {
    const login = new Login();
    const menu = new Menu();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
  });
});
