import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersOnItsWayPageForGemStones', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit On Its Way Page For GemStones (NE-TC-717)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderGemStonesOnItsWayPage();
  });
});
