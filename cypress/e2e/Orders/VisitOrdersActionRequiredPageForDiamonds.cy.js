import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersActionRequiredPageForDiamonds', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Action Required Page For Diamonds (NE-TC-710)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderDiamondActionRequiredPage();
  });
});
