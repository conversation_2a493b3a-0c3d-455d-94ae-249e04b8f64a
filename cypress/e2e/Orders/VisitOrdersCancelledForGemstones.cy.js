import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersCancelledPageForGemstones', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Cancelled Page For Gemstones (NE-TC-720)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderGemStonesCancelledPage();
  });
});
