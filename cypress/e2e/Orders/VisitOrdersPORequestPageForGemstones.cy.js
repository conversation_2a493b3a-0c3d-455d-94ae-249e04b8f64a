import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersPagePORequestPageForGemStones', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit PO Request Page For GemStones  (NE-TC-715)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderGemStonesPoRequestPage();
  });
});
