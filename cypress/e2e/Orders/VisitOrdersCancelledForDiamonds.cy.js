import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitOrdersCancelledPageForDiamonds', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit Cancelled Page For Diamonds (NE-TC-713)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyOrderDiamondCancelledPage();
  });
});
