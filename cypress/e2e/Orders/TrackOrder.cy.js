import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('TrackOrder', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Verify User Can Track An Order (NE-TC-646)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    cy.getSearchdiamond();
    login.loginUsingApi('Searchdiamond.json');
    menu.visitOrdersPage();
    orders.trackOrder('Searchdiamond.json');
  });
});
