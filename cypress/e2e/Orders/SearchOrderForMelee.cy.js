import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('SearchOrderForMelee', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Search Order Number From Melee (NE-TC-737)', { tags: '@Smoke' }, () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    cy.getSearchMelee();
    login.loginUsingApi('SearchMelee.json');
    menu.visitOrdersPage();
    orders.VerifyAllOrdersMeleePage();
    orders.searchOrderNumber('cypress/fixtures/SearchMelee.json');
  });
});
