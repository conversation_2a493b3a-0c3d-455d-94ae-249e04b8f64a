import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('SearchOrderForGemstone', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Search Order Number From Gemstone (NE-TC-736)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    cy.getSearchGemstone();
    login.loginUsingApi('SearchGemstone.json');
    menu.visitOrdersPage();
    orders.VerifyAllOrdersGemStonesPage();
    orders.searchOrderNumber('cypress/fixtures/SearchGemstone.json');
  });
});
