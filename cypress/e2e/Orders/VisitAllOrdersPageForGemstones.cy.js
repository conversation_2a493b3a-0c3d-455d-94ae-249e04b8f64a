import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VisitAllOrdersPageForGemstones', { tags: ['@Orders-Page', '@Regression'] }, () => {
  it('Visit All Orders Page For Gemstones (NE-TC-714)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.VerifyAllOrdersGemStonesPage();
  });
});
