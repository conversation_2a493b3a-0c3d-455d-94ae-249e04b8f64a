import Login from '../../support/Login';
import Product from '../../support/Product';

describe('VerifyMorCustomerCertificateNo', { tags: ['@Regression', '@CX'] }, () => {
  it('Verify Certificate No Using Mor Customer', () => {
    const login = new Login();
    const product = new Product();

    cy.getcertnumber();
    login.loginUsingApi('morcustomer.json');
    product.VerifyMorCustomerCertificateNo('cert.json');
  });
});
