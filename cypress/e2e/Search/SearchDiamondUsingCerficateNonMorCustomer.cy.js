import Login from '../../support/Login';
import Product from '../../support/Product';

describe('VerifyNonMorCustomerCertificateNo', { tags: ['@Regression', '@CX'] }, () => {
  it('Verify Certificate No Using Non-Mor Customer', () => {
    const login = new Login();
    const product = new Product();

    cy.getcertnumber();
    login.loginUsingApi('nonmorcustomer.json');
    product.VerifyNonMorCustomerCertificateNo('cert.json');
  });
});
