import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('EnableAndDisableCreditNoteAutoApplication', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Enable And Disable Credit note auto application (NE-TC-2256)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitNivodaCreditSettings();
    company.enableDisableCreditNoteAutoApplication();
  });
});
