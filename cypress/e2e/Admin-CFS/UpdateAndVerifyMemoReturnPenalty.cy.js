import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('UpdateAndVerifyMemoReturnPenalty', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Update And Verify Memo return penalty (NE-TC-2258)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitPricingSettings();
    company.updateMemoReturnPenalty('100');
  });
});
