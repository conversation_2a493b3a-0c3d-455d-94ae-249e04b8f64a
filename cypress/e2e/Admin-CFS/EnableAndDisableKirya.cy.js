import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('EnableAndDisableKirya', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Enable AndDisable Kirya From Kriya Settings (NE-TC-2253)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitKriyaSettings();
    company.enableDisableKriya('15000');
  });
});
