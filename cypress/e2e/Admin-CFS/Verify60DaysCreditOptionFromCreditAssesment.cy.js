import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('Verify60DaysCreditOptionFromCreditAssesment', { tags: ['@Admin-CFS', '@Regression', '@CX'] }, () => {
  it('Verify 60 days credit option from default checkout options From Credi tAssesment Page (NE-TC-2247)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changeCheckoutType('60 days credit');
    company.approveAndVerifyChanges();
  });
});
