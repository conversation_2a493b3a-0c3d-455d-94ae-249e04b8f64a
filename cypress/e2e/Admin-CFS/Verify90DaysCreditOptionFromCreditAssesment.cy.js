import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('Verify90DaysCreditOptionFromCreditAssesment', { tags: ['@Admin-CFS', '@Regression', '@CX'] }, () => {
  it('Verify 90 days credit option from default checkout options From Credit Assesment Page (NE-TC-2248)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changeCheckoutType('90 days credit');
    company.approveAndVerifyChanges();
  });
});
