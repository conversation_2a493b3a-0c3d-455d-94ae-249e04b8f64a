import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('AddSaveAndVerifyCreditLimitFromCreditAssesment', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Add, save and verify credit limit From Credit Assesment Page (NE-TC-2243)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.updateAccountLimit();
    company.updateCreditLimit();
    company.approveAndVerifyChanges();
  });
});
