import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe(
  'VerifyIncentivePayOptionAsDefaultCheckoutFromCreditAssessmentPage',
  { tags: ['@Admin-CFS', '@Regression'] },
  () => {
    it('Verify incentive pay option from default checkout options From Credit Assesment Page (NE-TC-2245)', () => {
      const login = new Login();
      const company = new Company();

      cy.getCompanyCfs();

      login.loginUsingAdminApi('loginasadmin.json');
      company.visitCompanyPage('getCompanyData.json');
      company.accessCompanySetting();
      company.visitFinanceSettingsPage();
      company.changeCheckoutType('Incentive Pay Active');
      company.approveAndVerifyChanges();
    });
  }
);
