import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('VerifyIncentivePayOptionAsPaymentMethodFromCreditAssesment', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Verify Incentive payment option from Payment Methods From  Credit Assesment Page (NE-TC-2250)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changePaymentMethod('Incentive Payment');
    company.approveAndVerifyChanges();
  });
});
