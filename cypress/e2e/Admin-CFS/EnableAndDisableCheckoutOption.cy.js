import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('EnableAndDisableCheckoutOption', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Enable And Disable Checkout Option (NE-TC-2255)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitNivodaCreditSettings();
    company.enableDisableCheckout();
  });
});
