import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('Verify30DaysCreditOptionFromCreditAssesment', { tags: ['@Admin-CFS', '@Regression', '@CX'] }, () => {
  it('Verify 30 days credit option from default checkout options From Credit Assesment Page (NE-TC-2246)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changeCheckoutType('30 days credit');
    company.approveAndVerifyChanges();
  });
});
