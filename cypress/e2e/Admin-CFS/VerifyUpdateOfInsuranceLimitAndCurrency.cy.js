import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('VerifyUpdateOfInsuranceLimitAndCurrency', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Verify Update  Of Incurance limit and currency (NE-TC-2257)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitNivodaCreditSettings();
    company.updateInsuranceLimitAndCurrency('170000', 'GBP');
  });
});
