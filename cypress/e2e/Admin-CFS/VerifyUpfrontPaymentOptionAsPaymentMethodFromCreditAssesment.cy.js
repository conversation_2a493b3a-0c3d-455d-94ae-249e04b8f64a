import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('Set Payment Method As Upfront And then Credit Payment', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Verify upfront payment option from Payment Method From Credit Assesment Page (NE-TC-2251)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changeCheckoutType('Upfront Payment');
    company.changePaymentMethod('Advance Payment');
    company.rejectAssessment();
  });

  it('Verify credit payment option from Payment Method From Credit Assesment Page (NE-TC-2252)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitFinanceSettingsPage();
    company.changeCheckoutType('90 days credit');
    company.changePaymentMethod('Credit Default Payment');
    company.updateAccountLimit();
    company.updateCreditLimit();
    company.approveAndVerifyChanges();
  });
});
