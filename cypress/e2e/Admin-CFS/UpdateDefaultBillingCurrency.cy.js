import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('UpdateDefaultBillingCurrency', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Update default billing currency (NE-TC-2249)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitInvoiceSettings();
    company.updateCurrency('GBP');
  });
});
