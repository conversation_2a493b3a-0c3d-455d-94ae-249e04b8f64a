import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('EnableAndDisableAIP', { tags: ['@Admin-CFS', '@Regression'] }, () => {
  it('Enable And Disable AIP From Pricing Settings (NE-TC-2254)', () => {
    const login = new Login();
    const company = new Company();

    cy.getCompanyCfs();

    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('getCompanyData.json');
    company.accessCompanySetting();
    company.visitPricingSettings();
    company.enableDisableAIP();
  });
});
