import Login from '../../support/Login';
import Menu from '../../support/Menu';
import SupplierFeeds from '../../support/Supplier/SupplierFeeds';
describe('Supplier Feed share', () => {
  it('Supplier Can Grant Feed to Customers', () => {
    const login = new Login();
    const menu = new Menu();
    const supplierFeed = new SupplierFeeds();
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Feed center', 'feed-center');
    supplierFeed.verifyCustomerCount('active');
    supplierFeed.clickModifyButton();
    supplierFeed.verifyFeedShareSettingPopupIsDisplayed();
    supplierFeed.verifyCheckboxIsChecked('Share feed with everyone');
    supplierFeed.verifyCheckboxIsChecked('Share feed with EU customers');
    supplierFeed.verifyCheckboxIsChecked('Share feed with US customers');
    supplierFeed.verifyCheckboxIsChecked('Share feed with rest of the world');
    supplierFeed.verifyCheckboxIsChecked('Share feed with GB customers');
    supplierFeed.ensureCheckboxIsChecked('Share feed with no one');
    supplierFeed.saveChanges();
    supplierFeed.verifySuccessMessage();
    supplierFeed.verifyCustomerCount('revoked');
    supplierFeed.clickModifyButton();
    supplierFeed.verifyFeedShareSettingPopupIsDisplayed();
    supplierFeed.ensureCheckboxIsChecked('Share feed with everyone');
    supplierFeed.saveChanges();
    supplierFeed.verifySuccessMessage();
    supplierFeed.verifyCustomerCount('active');
  });
});
