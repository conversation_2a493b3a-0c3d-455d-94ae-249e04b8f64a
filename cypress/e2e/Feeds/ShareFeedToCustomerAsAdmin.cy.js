import Login from './../../support/Login';
import AccessAdminTabs from './../../support/Admin/AccessAdminTabs';
import AdminFeed from '../../support/Admin/AdminFeed';

describe('Admin Grant and Revoke Feed', () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const adminFeed = new AdminFeed();

  beforeEach(() => {
    cy.getCompanyWithManyFeeds();
    adminFeed.setCustomerFromFixture('companyWithFeeds.json');
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Feed Center', '', '', '/admin/feed-center');
  });

  it('Nivoda admin Can Grant/Revoke Natural Feed to Customers (NE-TC-2839),(NE-TC-2840)', () => {
    adminFeed.searchCustomerName(Cypress.env('customerName'));
    adminFeed.expandNaturalFeed();
    adminFeed.storeFeedCount('Natural Feed', 'Total Stones', 'naturalTotalStonesCountBefore');
    adminFeed.storeFeedCount('Natural Feed', 'Active', 'naturalActiveFeedCount');
    adminFeed.storeFeedCount('Natural Feed', 'Revoked', 'naturalRevokedFeedCount');

    adminFeed.searchSupplierFeed(Cypress.env('customerPassword'), Cypress.env('customerId'), 'Natural');
    adminFeed.clickEditFeedButton();
    adminFeed.changeFeedStatusAndCloseDialogue();
    adminFeed.expandCustomerFeeds();
    adminFeed.storeFeedCount('Natural Feed', 'Total Stones', 'naturalTotalStonesCountAfter');
    adminFeed.storeFeedCount('Natural Feed', 'Active', 'naturalActiveFeedCountAfter');
    adminFeed.storeFeedCount('Natural Feed', 'Revoked', 'naturalRevokedFeedCountAfter');
    adminFeed.assertCountChangedBasedOnCheckbox(
      'naturalTotalStonesCountBefore',
      'naturalTotalStonesCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertActiveCountChangedBasedOnCheckbox(
      'naturalActiveFeedCount',
      'naturalActiveFeedCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertRevokedCountChangedBasedOnCheckbox(
      'naturalRevokedFeedCount',
      'naturalRevokedFeedCountAfter',
      'wasCheckboxChecked'
    );
  });

  it('Nivoda admin Can Grant/Revoke Labgrown Feed to Customers (NE-TC-3246),(NE-TC-3247)', () => {
    adminFeed.searchCustomerName(Cypress.env('customerName'));
    adminFeed.expandLabgrownFeed();
    adminFeed.storeFeedCount('Labgrown Feed', 'Total Stones', 'labGrownTotalStonesCountBefore');
    adminFeed.storeFeedCount('Labgrown Feed', 'Active', 'labGrownActiveFeedCount');
    adminFeed.storeFeedCount('Labgrown Feed', 'Revoked', 'labGrownRevokedFeedCount');
    adminFeed.searchSupplierFeed(Cypress.env('customerPassword'), Cypress.env('customerId'), 'Labgrown');
    adminFeed.clickEditFeedButton();
    adminFeed.changeFeedStatusAndCloseDialogue();
    adminFeed.expandCustomerFeeds();
    adminFeed.storeFeedCount('Labgrown Feed', 'Total Stones', 'labGrownTotalStonesCountAfter');
    adminFeed.storeFeedCount('Labgrown Feed', 'Active', 'labGrownActiveFeedCountAfter');
    adminFeed.storeFeedCount('Labgrown Feed', 'Revoked', 'labGrownRevokedFeedCountAfter');
    adminFeed.assertCountChangedBasedOnCheckbox(
      'labGrownTotalStonesCountBefore',
      'labGrownTotalStonesCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertActiveCountChangedBasedOnCheckbox(
      'labGrownActiveFeedCount',
      'labGrownActiveFeedCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertRevokedCountChangedBasedOnCheckbox(
      'labGrownRevokedFeedCount',
      'labGrownRevokedFeedCountAfter',
      'wasCheckboxChecked'
    );
  });

  it('Nivoda admin Can Grant/Revoke Unkown Feed to Customers (NE-TC-3248),(NE-TC-3249)', () => {
    adminFeed.searchCustomerName(Cypress.env('customerName'));
    adminFeed.expandUnknownFeed();
    adminFeed.storeFeedCount('Unknown Feed', 'Total Stones', 'unKnownTotalStonesCountBefore');
    adminFeed.storeFeedCount('Unknown Feed', 'Active', 'unKnownActiveFeedCount');
    adminFeed.storeFeedCount('Unknown Feed', 'Revoked', 'unKnownRevokedFeedCount');
    adminFeed.searchSupplierFeed(Cypress.env('customerPassword'), Cypress.env('customerId'), 'Gemstone');
    adminFeed.clickEditFeedButton();
    adminFeed.changeFeedStatusAndCloseDialogue();
    adminFeed.expandCustomerFeeds();
    adminFeed.storeFeedCount('Unknown Feed', 'Total Stones', 'unKnownTotalStonesCountAfter');
    adminFeed.storeFeedCount('Unknown Feed', 'Active', 'unKnownActiveFeedCountAfter');
    adminFeed.storeFeedCount('Unknown Feed', 'Revoked', 'unKnownRevokedFeedCountAfter');
    adminFeed.assertCountChangedBasedOnCheckbox(
      'unKnownTotalStonesCountBefore',
      'unKnownTotalStonesCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertActiveCountChangedBasedOnCheckbox(
      'unKnownActiveFeedCount',
      'unKnownActiveFeedCountAfter',
      'wasCheckboxChecked'
    );
    adminFeed.assertRevokedCountChangedBasedOnCheckbox(
      'unKnownRevokedFeedCount',
      'unKnownRevokedFeedCountAfter',
      'wasCheckboxChecked'
    );
  });
});
