import Login from '../../support/Login';
import Product from '../../support/Product';
import Filter from '../../support/Filter';

describe('VerifyDeliveryTimelinesForUSCustomer', { tags: ['@Delivery-Timelines', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const filter = new Filter();

  beforeEach(() => {
    login.loginUsingApi('multipleaddressuser.json');
  });

  it('Verify delivery timelines for AU location and AU product (NE-TC-2219)', () => {
    filter.applyLocationFilter('Australia');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'AU', 'R');
  });

  it('Verify delivery timelines for US product (NE-TC-2219)', () => {
    product.searchStone('getUSstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'US', 'US');
  });

  it('Verify delivery timelines for GB product (NE-TC-2219)', () => {
    product.searchStone('getGBstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'GB', 'R');
  });

  it('Verify delivery timelines for IN product (NE-TC-2219)', () => {
    product.searchStone('getINstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'IN', 'US');
  });
});
