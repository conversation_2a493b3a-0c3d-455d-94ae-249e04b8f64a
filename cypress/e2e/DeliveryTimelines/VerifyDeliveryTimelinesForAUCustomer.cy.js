import Login from '../../support/Login';
import Product from '../../support/Product';
import Filter from '../../support/Filter';

describe('VerifyDeliveryTimelinesForAUCustomer (NE-TC-2221)', { tags: ['@Delivery-Timelines', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('auUser.json');
  });

  it('Verify Delivery timelines for US Stone', () => {
    product.searchStone('getUSstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'US', 'AU');
  });

  it('Verify Delivery timelines for GB Stone', () => {
    product.searchStone('getGBstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'GB', 'AU');
  });

  it('Verify Delivery timelines for IN Stone', () => {
    product.searchStone('getINstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'IN', 'AU');
  });

  it('Verify Delivery timelines for HK Stone', () => {
    product.searchStone('getHKstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'HK', 'AU');
  });
});
