import Login from '../../support/Login';
import Product from '../../support/Product';
import Filter from '../../support/Filter';

describe('VerifyDeliveryTimelinesForGBCustomer', { tags: ['@Delivery-Timelines', '@Regression'] }, () => {
  it('Verify Delivery timelines for GB Customer (NE-TC-2220)', () => {
    const login = new Login();
    const product = new Product();
    const filter = new Filter();

    login.loginUsingApi('gbUuser.json');

    product.searchStone('getUSstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'US', 'R');

    product.searchStone('getGBstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'GB', 'GB');

    product.searchStone('getINstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'IN', 'GB');

    product.searchStone('getHKstone.json');
    product.deliveryDaysAssertion('deliveryTimelineData.json', 'HK', 'R');
  });
});
