import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('ValidateStoneNotAvailableButton', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Stone Not Available Text In Nivoda Express (NE-TC-1401)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifyStoneNotAvailableText(
      'cypress/fixtures/notavailablesourcestonecert.json',
      'New York',
      'CA customers',
      'EXPRESS'
    );
  });
});
