import Login from '../../support/Login';
import Product from '../../support/Product';

describe(
  'ValidateOneToTwoDeliveryIconOnStoneCardForMemoAndExpressEnabledCustomer',
  { tags: ['@Regression', '@Express'] },
  () => {
    it('Verify One To Two Delivery Icon And Its Text For Memo And Nivoda Express Customer (NE-TC-2004)', () => {
      const login = new Login();
      const product = new Product();

      login.loginUsingApi('memoAndExpressUser.json');
      product.accessOneToTwoDeliveryButton();
    });
  }
);
