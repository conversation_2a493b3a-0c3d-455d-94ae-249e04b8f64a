import Login from '../../support/Login';
import Product from '../../support/Product';

describe('NivodaExpressNotVisibleForExpressDisabledCustomer', { tags: ['@Regression', '@Express'] }, () => {
  it('Verify Nivoda Express Disabled Customer Cannot See Express Button (NE-TC-1543)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('memoUser.json');
    product.nivodaExpressButtonNotVisibleAssertion();
  });
});
