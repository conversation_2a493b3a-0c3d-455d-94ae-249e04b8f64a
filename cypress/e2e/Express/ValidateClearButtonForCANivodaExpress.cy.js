import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('ValidateClearButtonInNivodaExpressForCACustomers', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Clear Button Clears all the selected and input data In Nivoda Express For CA Customers (NE-TC-2367)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifyClearButton('cypress/fixtures/searchOnlyCert.json', 'New York', 'CA customers', 'EXPRESS');
  });
});
