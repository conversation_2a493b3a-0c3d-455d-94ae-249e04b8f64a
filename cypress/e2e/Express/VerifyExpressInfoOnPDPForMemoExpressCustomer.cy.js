import Login from '../../support/Login';
import Product from '../../support/Product';

describe('VerifyExpressInfoOnPDPForMemoExpressCustomer', { tags: ['@Regression', '@Express'] }, () => {
  it('Verify Express Info On PDP Stone Page For Memo Express Customer (NE-TC-1551)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('memoAndExpressUser.json');
    product.accessOneToTwoDeliveryButton();
    product.searchStone('memoNotReturnableStone.json');
    cy.wait(5000);
    product.accessDiamondProductDetails();
    product.assertExpressInfoPDPForMemoExpressCustomer();
  });
});
