import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('ValidateNotFoundButton', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Not Found Button In Case Of Invalid Stone In Nivoda Express (NE-TC-1404)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.selectDestinationOffice(
      'cypress/fixtures/searchOnlyCert.json',
      'New York',
      'CA customers',
      'EXPRESS'
    );
    nivodaExpress.invalidStoneTextVerify();
  });
});
