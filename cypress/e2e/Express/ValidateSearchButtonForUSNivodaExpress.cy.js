import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('ValidateSearchButtonUSCustomers', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Search Button In Nivoda Express For US Customers (NE-TC-2372)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');

    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifSearchButton('cypress/fixtures/searchOnlyCert.json', 'New York', 'US customers', 'EXPRESS');
  });
});
