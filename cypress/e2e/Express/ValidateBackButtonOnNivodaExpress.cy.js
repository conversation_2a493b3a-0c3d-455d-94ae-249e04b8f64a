import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('ValidateSourceSelectedButton', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Source Selected Button Displays Correct Text In Nivoda Express For CA Customers (NE-TC-2366)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifSearchButton('cypress/fixtures/searchOnlyCert.json', 'New York', 'CA customers', 'EXPRESS');
    nivodaExpress.verifyCheckBox();
    nivodaExpress.beforeSourcingStoneTextVerify('New York');
    nivodaExpress.backButtonOnSourcingPage();
  });
});
