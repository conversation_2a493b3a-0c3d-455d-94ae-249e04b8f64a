import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe('Validate Clear Button In Nivoda Express For EU Customers', { tags: ['@Regression', '@Express'] }, () => {
  it('Validate Clear Button Clears all the selected and input data In Nivoda Express For EU Customers (NE-TC-2368)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifyClearButton('cypress/fixtures/searchOnlyCert.json', 'London', 'EU customers', 'EXPRESS');
  });
});
