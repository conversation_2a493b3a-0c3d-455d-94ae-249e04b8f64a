import Login from '../../support/Login';
import Product from '../../support/Product';

describe(
  'VerifyExpressStonesAreReturnableForNonMemoAndExpressEnabledCustomer',
  { tags: ['@Regression', '@Express'] },
  () => {
    it('Verify Express Stones Are Returnable For Non  Memo And Nivoda Express Customer (NE-TC-1547)', () => {
      const login = new Login();
      const product = new Product();

      login.loginUsingApi('nivodaexpressuser.json');
      product.accessNivodaExpress();
      product.stoneReturnableAssertion();
    });
  }
);
