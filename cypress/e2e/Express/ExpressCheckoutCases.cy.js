import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('Express Checkout Test Cases', { tags: ['@Smoke', '@Express'] }, () => {
  let login = new Login();
  let product = new Product();
  let checkout = new Checkout();
  let navbar = new Navbar();
  it('Validate User Can Add Express Stone To Cart (NE-TC-1534) (NE-TC-1538) (NE-TC-1539) (NE-TC-1540) (NE-TC-1541)', () => {
    login.loginUsingApi('memoAndExpressUser.json');
    navbar.visitLabgrownDiamonds();
    product.accessNivodaExpress();

    cy.getexpressCheckoutStone1();
    cy.getexpressCheckoutStone2();

    product.accessNivodaExpress();
    product.addMemoProductToCart('cypress/fixtures/expressCheckoutStone1.json');
    product.addMemoProductToCart('cypress/fixtures/expressCheckoutStone2.json');
    product.memoProceedToCheckout();
    product.orderTotalCalculations();
    product.cartAndNotesAssertions('cypress/fixtures/expressCheckoutStone2.json', 0, 1, 0);
    product.cartAndNotesAssertions('cypress/fixtures/expressCheckoutStone1.json', 1, 5, 4);
    checkout.proceedToDeliveryOptions();
    checkout.deliverToSingleAddress();
    cy.visit('/v2/live/checkout/delivery-options');
    checkout.deliverToMultipleAddressesForExpress();
    cy.visit('/v2/live/checkout/delivery-options');
    checkout.addDeliveryAddress();
  });
});
