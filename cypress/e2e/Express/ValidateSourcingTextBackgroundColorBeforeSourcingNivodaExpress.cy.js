import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';

describe(
  'ValidateSourcingTextBackgroundColorBeforeSourcingNivodaExpress',
  { tags: ['@Regression', '@Express'] },
  () => {
    it('Validate Sourcing Text Background Color Should Be Yellow Before Sourcing Nivoda Express (NE-TC-2374)', () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();
      const nivodaExpress = new NivodaExpress();

      login.loginUsingAdminApi('loginasadmin.json');
      login.loginAsAdminAssertion();
      accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
      nivodaExpress.verifSearchButton('cypress/fixtures/searchOnlyCert.json', 'New York', 'CA customers', 'EXPRESS');
      nivodaExpress.verifyCheckBox();
      nivodaExpress.beforeSourcingStoneTextVerify('New York');
      nivodaExpress.verifyBackgroundColorOfBeforeSourcingStoneText();
    });
  }
);
