import Login from '../../support/Login';
import Product from '../../support/Product';

describe('VerifyExpressInfoOnPDPForNonMemoExpressCustomer', { tags: ['@Regression', '@Express'] }, () => {
  it('Verify Express Info On PDP Stone Page For Non Memo Express Customer (NE-TC-1550)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('nivodaexpressuser.json');
    product.accessNivodaExpress();
    product.accessDiamondProductDetails();
    product.assertExpressInfoPDPForExpressCustomer();
  });
});
