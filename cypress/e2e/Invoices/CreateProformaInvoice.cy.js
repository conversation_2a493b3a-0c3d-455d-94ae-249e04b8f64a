import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';
import Checkout from '../../support/Checkout';
import Admin from '../../support/Admin';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('CreateProformaInvoiceandverifydetails', { tags: ['@Smoke', '@Invoices'] }, () => {
  const login = new Login();
  const product = new Product();
  const navbar = new Navbar();
  const checkout = new Checkout();
  const admin = new Admin();
  const menu = new Menu();
  const financeDashboard = new FinanceDashboard();

  it('Create Proforma Invoice and verify details (NE-TC-683)', () => {
    cy.getcreateinvoice();
    login.loginUsingApi('upfrontaipuser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/createinvoice.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });
  it('Create Proforma Invoice and verify details (NE-TC-683)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Gemstone',
      'cypress/fixtures/createinvoice.json',
      'cypress/fixtures/orderNumber.json',
      'Available + On hold (1)',
      'Status update successful for 1 item(s) and moved to Available + On hold tab'
    );
    admin.selectStone('Gemstone');
    admin.accessAwaitingPaymentTab();
    admin.orderStatuChangeFromAwaitingPayment('orderNumber.json', 'Purchase Order');
    admin.accessPOFromMenu();
    cy.wait(3000);
    admin.confirmRTC(
      'Gemstone',
      'cypress/fixtures/createinvoice.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'Gemstone',
      'cypress/fixtures/createinvoice.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.accessAccounting('To Invoice');
    admin.createInvoice('cypress/fixtures/upfrontaipuser.json');
    admin.accessInvoicesToSend();
    admin.sendInvoice('cypress/fixtures/upfrontaipuser.json');
  });
  it('Create Proforma Invoice and verify details (NE-TC-683)', { tags: ['@Smoke'] }, () => {
    cy.wait(6000);
    login.loginUsingApi('upfrontaipuser.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('Invoice.json');
  });
});
