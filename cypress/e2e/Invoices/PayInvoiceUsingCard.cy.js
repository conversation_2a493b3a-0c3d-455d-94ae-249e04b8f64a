import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';
import Invoices from '../../support/Invoices';

describe('PayInvoiceandVerifyStatus', { tags: ['@Invoices'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const financeDashboard = new FinanceDashboard();
  const invoices = new Invoices();

  //this test case is skipping currently due to stripe issue will check this later
  it.skip('Verify Invoices Status (NE-TC-685)', () => {
    login.loginUsingApi('PayInvoice1.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('PayInvoice1.json');
    invoices.payInvoiceViaStripe();
  });
});
