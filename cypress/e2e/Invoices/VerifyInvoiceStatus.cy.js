import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyInvoiceStatus', { tags: ['@Invoices', '@Regression'] }, () => {
  it('Verify status of the invoice (NE-TC-684)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('VerifyStatusOfInvoice.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('VerifyStatusOfInvoice.json');
    financeDashboard.invoiceStatusAssertion('Paid');
  });
});
