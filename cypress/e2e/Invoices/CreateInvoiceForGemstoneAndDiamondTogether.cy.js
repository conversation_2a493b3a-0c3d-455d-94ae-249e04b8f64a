import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Admin from '../../support/Admin';
import Navbar from '../../support/Navbar';

describe('CreateInvoiceForGemstone&DiamondTogether', { tags: [, '@OTP'] }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();

  const admin = new Admin();

  const navbar = new Navbar();
  it('Verify User Can Create Place Order For Gemstone & Diamond Together', () => {
    cy.getcreditNoteDiamond();
    cy.getcreditNoteGems();
    cy.getMultipleAddressUser();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/creditNoteDiamond.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/creditNoteGems.json');
    product.viewCart();
    checkout.proceedToDeliveryOptions();
    checkout.deliverToMultipleAddresses('multipleaddressuser.json');
    checkout.multipleOrderConfirmationText();
  });
  it('Verify User Can Create Invoice For Gemstone & Diamond Together (NE-TC-2003)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteDiamond.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/creditstoneorderNumber2.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteDiamond.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/creditstoneorderNumber2.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.selectStone('Gemstone');
    admin.markGemStoneQcPass('cypress/fixtures/creditstoneorderNumber2.json');
    admin.selectStone('Diamond');
    admin.markStoneQcPass('cypress/fixtures/creditstoneorderNumber1.json');
    admin.accessAccounting('To Invoice');
    admin.createInvoice('cypress/fixtures/multipleaddressuser.json');
  });
});
