import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Admin from '../../support/Admin';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('PayInvoiceandVerifyStatus', { tags: ['@Smoke', '@Invoices'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const admin = new Admin();
  const financeDashboard = new FinanceDashboard();
  it('Pay Invoice (NE-TC-685)', () => {
    cy.getPayInvoice1();

    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessAccounting('To Invoice');
    admin.accessInvoicesToSend();
    admin.sendInvoice('cypress/fixtures/PayInvoice1.json');
    admin.accessAwaitingPaymentTabAccounting();
    admin.searchInvoiceInAwaitingPaymentTab('cypress/fixtures/Invoice.json');

    admin.payInvoice('cypress/fixtures/Invoice.json');
  });
  it('Verify Invoices Status (NE-TC-685)', () => {
    login.loginUsingApi('PayInvoice1.json');
    menu.visitFinancesPage();
    financeDashboard.visitPaidTab();
    financeDashboard.searchInvoice('Invoice.json');
    financeDashboard.invoiceStatusAssertion('Paid');
  });
});
