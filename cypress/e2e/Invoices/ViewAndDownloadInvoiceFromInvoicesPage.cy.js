import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Invoices from '../../support/Invoices';

describe('ViewAndDownloadInvoicesFromInvoicesPage', { tags: '@Skip' }, () => {
  it.skip('Verify User Can View And Download Invoices From Invoices Page (NE-TC-686)', () => {
    const login = new Login();
    const menu = new Menu();
    const invoices = new Invoices();

    cy.getinvoicedata();
    login.loginUsingApi('invoicedata.json');
    menu.visitInvoicesPage();
    invoices.searchInvoice('cypress/fixtures/invoicedata.json');
    invoices.downloadAndViewInvoice('cypress/fixtures/invoicedata.json');
  });
});
