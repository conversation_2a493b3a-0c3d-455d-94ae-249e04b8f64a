import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Admin from '../../support/Admin';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('PayPartialInvoiceandVerifyStatus', { tags: ['@Invoices', '@Regression'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const admin = new Admin();
  const financeDashboard = new FinanceDashboard();
  it('Pay Partial Invoice (NE-TC-689)', () => {
    cy.getPayPartialInvoice();
    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessAccounting('To Invoice');
    admin.accessAwaitingPaymentTabAccounting();
    admin.searchInvoiceInAwaitingPaymentTab('cypress/fixtures/PayPartialInvoice.json');
    admin.partialPayInvoice('cypress/fixtures/PayPartialInvoice.json');
  });

  it('Verify Invoices Status (NE-TC-689)', () => {
    login.loginUsingApi('PayPartialInvoice.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('PayPartialInvoice.json');
    financeDashboard.invoiceStatusAssertion('Partially paid');
  });
});
