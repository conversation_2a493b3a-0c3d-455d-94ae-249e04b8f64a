import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';
import Product from '../../support/Product';
import Login from '../../support/Login';

describe('ShortListOptionDisabledOnNivodaCart', { tags: ['@Showroom'] }, () => {
  const login = new Login();
  const showroomLogin = new ShowroomLogin();
  const showroom = new Showroom();
  const product = new Product();
  it('Verify user is able to Add Natural Diamond To Basket', () => {
    cy.getnaturaldiamondcert();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductToNivodaCartAndPlaceOrder(
      'naturaldiamondcert.json',
      'Natural diamonds',
      'Natural diamonds (1)',
      '3',
      '3'
    );
  });
  it('Verify Preventing Shortlisting of Showroom Orders on Nivoda Platform (ST-TC-13)', () => {
    login.visitPage();
    login.loginUsingUi('getcheckouttonivodacartuser.json');
    login.loginAsCustomerAssertion();
    product.verifyShortlistOptionDisabledForShowroomItems('naturaldiamondcert.json');
  });
});
