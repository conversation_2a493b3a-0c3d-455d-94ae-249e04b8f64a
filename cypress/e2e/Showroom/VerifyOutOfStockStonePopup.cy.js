import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('VerifyViewButtonNavigatesToBasketPage', { tags: ['@Showroom'] }, () => {
  it('Verify ‘View’ CTA in Toaster Message Navigates to Basket Page on Showroom (ST-TC-42)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('checkouttoshowroomcartuser.json');

    showroom.accessDashboard();
    showroom.addProductToCart('OutOfStockShowroomStone.json', 'Natural diamonds', 'Natural diamonds (1)');
    showroom.verifyViewBasketButton();
    showroom.verifyOutOfStockStonePopup();
  });
});
