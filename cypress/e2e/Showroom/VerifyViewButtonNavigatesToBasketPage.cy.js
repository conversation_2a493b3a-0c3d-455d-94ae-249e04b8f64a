import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('VerifyViewButtonNavigatesToBasketPage', { tags: ['@Showroom'] }, () => {
  it('Verify ‘View’ CTA in Toaster Message Navigates to Basket Page on Showroom (ST-TC-42)', () => {
    cy.getnaturaldiamondcert();

    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('checkouttoshowroomcartuser.json');

    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductToCart('naturaldiamondcert.json', 'Natural diamonds', 'Natural diamonds (1)');
    showroom.verifyViewBasketButton();
  });
});
