import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';
import Product from '../../support/Product';
import Login from '../../support/Login';
import Checkout from '../../support/Checkout';

describe('AddToBasketShowroomNaturalDiamond', { tags: ['@Showroom'] }, () => {
  const login = new Login();
  const showroomLogin = new ShowroomLogin();
  const showroom = new Showroom();
  const product = new Product();
  const checkout = new Checkout();
  it('Verify user is able to Add Natural Diamond To Basket', () => {
    cy.getnaturaldiamondcert();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductToNivodaCartAndPlaceOrder(
      'naturaldiamondcert.json',
      'Natural diamonds',
      'Natural diamonds (1)',
      '3',
      '3'
    );
  });
  it('Verify Showroom Orders Are Marked as CFM When Placed on Nivoda Platform (ST-TC-19)(ST-TC-20)', () => {
    login.visitPage();
    login.loginUsingUi('getcheckouttonivodacartuser.json');
    login.loginAsCustomerAssertion();
    product.viewCheckoutShowroomOrder('getcheckouttonivodacartuser.json');
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
    cy.verifyOrderMethodIsCfm();
  });
});
