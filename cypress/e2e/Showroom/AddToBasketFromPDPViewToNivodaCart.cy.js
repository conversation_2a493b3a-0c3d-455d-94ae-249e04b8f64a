import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AddToCartPDPView', { tags: ['@Showroom'] }, () => {
  it('Verify Entry Point for ‘Add to Basket’ from PDP in Showroom (ST-TC-36)(ST-TC-39)(ST-TC-40)(ST-TC-41)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductFromPDPToNivodaCart(
      'naturaldiamondcert.json',
      'Natural diamonds',
      'Natural diamonds (1)',
      '0',
      '3'
    );
  });
});
