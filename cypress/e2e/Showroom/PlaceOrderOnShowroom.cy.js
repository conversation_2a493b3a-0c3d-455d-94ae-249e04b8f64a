import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AddToBasketShowroomNaturalDiamond', { tags: ['@Showroom'] }, () => {
  it('Verify user is able to Add Natural Diamond To Basket (ST-TC-3)(ST-TC-4)(ST-TC-43)(NE-TC-3202)', () => {
    cy.getnaturaldiamondcert();

    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('checkouttoshowroomcartuser.json');

    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductToShowRoomCart('naturaldiamondcert.json', 'Natural diamonds', 'Natural diamonds (1)');
    showroom.placeShowroomOrder();
  });
});
