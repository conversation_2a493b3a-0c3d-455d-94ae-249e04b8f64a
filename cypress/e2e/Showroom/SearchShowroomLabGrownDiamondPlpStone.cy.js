import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('SearchNaturalDiamondPlpStone', { tags: ['@Smoke', '@Showroom'] }, () => {
  it('Verify user is able to search Natural Diamond in plp page through stock id /cert id (NE-TC-3253)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.visitLabGrownDiamonds();
    showroom.searchStone('labdiamondcert.json');
    showroom.verifyDiamondText('Lab grown diamonds', 'Lab grown diamonds (1)');
  });
});
