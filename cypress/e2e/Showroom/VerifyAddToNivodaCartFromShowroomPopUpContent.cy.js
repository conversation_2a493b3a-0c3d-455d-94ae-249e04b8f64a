import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('SearchNaturalDiamondPlpStone', { tags: ['@Smoke', '@Showroom'] }, () => {
  it('Verify Diamond Preview Details in the ‘Add to Basket’ Popup on Showroom (ST-TC-38)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroom.accessDashboard();
    showroom.searchStone('naturaldiamondcert.json');
    showroom.verifyDiamondText('Natural diamonds', 'Natural diamonds (1)');
    showroom.verifyAddToNivodaCartPopUpContents();
  });
});
