import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('SearchNaturalDiamondPlpStone', { tags: ['@Smoke', '@Showroom'] }, () => {
  it('Verify user is able to search Natural Diamond in plp page through stock id /cert id (NE-TC-3185)(NE-TC-3167)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.searchStone('naturaldiamondcert.json');
    showroom.verifyDiamondText('Natural diamonds', 'Natural diamonds (1)');
  });
});
