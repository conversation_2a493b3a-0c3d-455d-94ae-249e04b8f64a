import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AddToCartFromShortListView', { tags: ['@Showroom'] }, () => {
  it('Verify Entry Point for ‘Add to Basket’ from Shortlists Page in Showroom (ST-TC-37)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductFromShortListToNivodaCart(
      'naturaldiamondcert.json',
      'Natural diamonds',
      'Natural diamonds (1)',
      '1',
      '2'
    );
  });
});
