import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AddNaturalDiamondToShortListFromPLPView', { tags: ['@Showroom'] }, () => {
  it('Verify user is able to add stone to shortlist from PLP page (NE-TC-3201)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.verifyAddToShortList('naturaldiamondcert.json', 'Natural diamonds', 'Natural diamonds (1)', '1', '2');
  });
});
