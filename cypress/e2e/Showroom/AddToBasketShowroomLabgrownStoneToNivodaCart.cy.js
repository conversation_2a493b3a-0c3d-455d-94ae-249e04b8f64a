import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AddToBasketShowroomNaturalDiamond', { tags: ['@Showroom'] }, () => {
  it('Verify user is able to Add Natural Diamond To Basket (ST-TC-5) (ST-TC-7)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.visitLabGrownDiamonds();
    showroom.addProductToNivodaCartAndPlaceOrder(
      'labdiamondcert.json',
      'Lab grown diamonds',
      'Lab grown diamonds (1)',
      '3',
      '3'
    );
  });
});
