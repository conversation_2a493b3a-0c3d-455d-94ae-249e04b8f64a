import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';
import Product from '../../support/Product';
import Login from '../../support/Login';

describe('AddToBasketShowroomNaturalDiamond', { tags: ['@Showroom'] }, () => {
  const login = new Login();
  const showroomLogin = new ShowroomLogin();
  const showroom = new Showroom();
  const product = new Product();
  it('Verify user is able to Add Natural Diamond To Basket (ST-TC-1)(ST-TC-2)(ST-TC-4)(ST-TC-6)(ST-TC-8)(ST-TC-7)(ST-TC-35)', () => {
    cy.getnaturaldiamondcert();
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
    showroom.addProductToNivodaCartAndPlaceOrder(
      'naturaldiamondcert.json',
      'Natural diamonds',
      'Natural diamonds (1)',
      '3',
      '3'
    );
  });
  it('Verify Proceeding to Checkout Includes Showroom Orders on Nivoda Platform (ST-TC-11)(ST-TC-12)(ST-TC-16)(ST-TC-17)(ST-TC-18)', () => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
    login.loginAsCustomerAssertion();
    product.viewCheckoutShowroomOrder('morcustomer.json');
  });
  it('Verify Warning Popup on Removing Diamonds from PLP/PDP/Shortlist on Nivoda Platform(ST-TC-14)', () => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
    login.loginAsCustomerAssertion();
    product.verifyRemoveShowroomItemFromCartPLPPage('naturaldiamondcert.json', 'morcustomer.json');
  });
  it('Verify Warning Popup When Attempting to Remove a Diamond from Nivoda Cart on Nivoda Platform (ST-TC-9)', () => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
    login.loginAsCustomerAssertion();
    product.removeFromCartShowroomPopup('morcustomer.json');
  });
  it('Verify Removing a Diamond from the Nivoda Cart on Nivoda Platform Affects All Company Users (ST-TC-10)', () => {
    cy.fetchUsersByCompanyIdExceptSameEmail('morcustomer.json', 'sameComapnyUser.json');

    login.visitPage();
    login.loginUsingUi('sameComapnyUser.json');
    login.loginAsCustomerAssertion();
    product.searchStone('naturaldiamondcert.json');
  });
  it('Verify Searchability of Removed Diamonds on Nivoda Platform if Still Available (ST-TC-15)(ST-TC-20)(ST-TC-22)', () => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
    login.loginAsCustomerAssertion();
    product.addProductToCart('naturaldiamondcert.json');
  });
});
