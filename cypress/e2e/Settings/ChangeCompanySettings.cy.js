import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('ChangeCompanySettings', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Change Company Settings (NE-TC-144)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.updateCompanySetting();
  });
});
