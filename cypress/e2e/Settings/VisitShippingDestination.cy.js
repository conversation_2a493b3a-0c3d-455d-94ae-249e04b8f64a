import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('AccessShippingDestination', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Access Shipping Destination (NE-TC-153)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.visitShippingDestination();
  });
});
