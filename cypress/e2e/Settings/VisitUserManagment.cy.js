import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('AccessUserManagment', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Access User Managment (NE-TC-149)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openUserManagment();
  });
});
