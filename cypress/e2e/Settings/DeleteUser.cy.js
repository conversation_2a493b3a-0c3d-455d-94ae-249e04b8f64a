import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DeleteUser', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Delete User (NE-TC-152)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openUserManagment();
    settings.deleteUser('User deleted successfully');
  });
});
