import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DisableOrderIsPlacedEmailNotification', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Verify Order placed Notification is Disabled (NE-TC-1229)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications('Order is placed', 'disableEmail', 'Notification settings are saved');
  });
});
