import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnableSecurity', { tags: ['@Customer-Settings', '@OTP'] }, () => {
  it('Verify Security Is Enabled For The User (NE-TC-1239)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingUniqueUsersApi();
    menu.visitSettingsPage();
    settings.securityenabledisable('enableSecurity', 'Successfully updated your settings');
  });
});
