import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('ChangeLanguage', { tags: ['@Customer-Settings', '@Regression', '@CX'] }, () => {
  it('Change Language (NE-TC-145)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeLanguage();
  });
});
