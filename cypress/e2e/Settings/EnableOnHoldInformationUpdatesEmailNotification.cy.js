import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnableOnHold-InformationUpdatesEmailNotification', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Verify Order placed Notification is Enabled (NE-TC-1235)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications(
      'On hold - information updates',
      'enableEmail',
      'Notification settings are saved'
    );
  });
});
