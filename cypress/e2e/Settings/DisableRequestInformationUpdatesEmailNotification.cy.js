import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DisableRequest-InformationUpdatesEmailNotification', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Verify Request - Information Updates is Disabled (NE-TC-1226)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications(
      'Request - information updates',
      'disableEmail',
      'Notification settings are saved'
    );
  });
});
