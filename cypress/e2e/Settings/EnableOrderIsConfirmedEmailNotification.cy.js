import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnableOrderIsPlacedEmailNotification', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Verify Order placed Notification is Enabled (NE-TC-1212)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications('Order is confirmed', 'enableEmail', 'Notification settings are saved');
  });
});
