import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe(
  'DisableSoldOut/OnMemoUpdatesFromCartOrShortlistEmaiLNotification',
  { tags: ['@Customer-Settings', '@Regression'] },
  () => {
    it('Verify Sold out/on memo updates from cart or shortlist Notification is Disabled (NE-TC-1221)', () => {
      const login = new Login();
      const menu = new Menu();
      const settings = new Settings();

      login.loginUsingApi('companyowner.json');
      menu.visitSettingsPage();
      settings.changeEmailNotifications(
        'Sold out/on memo updates from cart or shortlist',
        'disableEmail',
        'Notification settings are saved'
      );
    });
  }
);
