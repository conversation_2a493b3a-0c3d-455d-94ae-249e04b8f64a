import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('AddNewUser', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Add New User (NE-TC-150)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openUserManagment();
    settings.addUser('User created successfully');
  });
});
