import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('Update Profile Picture', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Update Profile Picture (NE-TC-139)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.updateProfileImage('Successfully updated your profile image');
  });
});
