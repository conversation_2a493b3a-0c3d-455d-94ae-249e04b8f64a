import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DisableSecurity', { tags: ['@Customer-Settings', '@OTP'] }, () => {
  it('Verify Security Is Disabled For The User (NE-TC-148)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingUniqueUsersApi();
    menu.visitSettingsPage();
    settings.securityenabledisable('disableSecurity', 'Successfully updated your settings');
  });
});
