import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe(
  'InvoiceCommunicationsIsAlwaysEnabledEmaiLNotification',
  { tags: ['@Customer-Settings', '@Regression'] },
  () => {
    it('Verify Invoice communications (cannot be disabled) is always Enabled (NE-TC-1237)', () => {
      const login = new Login();
      const menu = new Menu();
      const settings = new Settings();

      login.loginUsingApi('companyowner.json');
      menu.visitSettingsPage();
      settings.changeEmailNotifications(
        'Invoice communications (cannot be disabled)',
        'enableEmail',
        'Notification settings are saved'
      );
      settings.invoiceCommunicationAlwaysenabled();
    });
  }
);
