import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EditLocation', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Edit Location (NE-TC-157)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.visitShippingDestination();
    settings.editLocation('Location updated successfully.Our team will contact you soon for verification.');
  });
});
