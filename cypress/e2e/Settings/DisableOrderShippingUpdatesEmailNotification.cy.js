import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DisableOrderShippingUpdatesEmaiLNotification', { tags: ['@Customer-Settings', '@Regression'] }, () => {
  it('Verify Order Shipping Updates Notification is Disabled (NE-TC-1230)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications('Order shipping updates', 'disableEmail', 'Notification settings are saved');
  });
});
