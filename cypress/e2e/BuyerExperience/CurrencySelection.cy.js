import Login from '../../support/Login';
import Settings from '../../support/Settings';

describe('Verify Currency dropdown (NE-TC-2471)', { tags: ['@Regression', '@CX'] }, () => {
  const login = new Login();
  const settings = new Settings();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('should update and verify currency: $ (NE-TC-3266)', () => {
    settings.updateCurrencyAndVerify(0, '$');
  });

  it('should update and verify currency: £ (NE-TC-3267)', () => {
    settings.updateCurrencyAndVerify(1, '£');
  });

  it('should update and verify currency: € (NE-TC-3268)', () => {
    settings.updateCurrencyAndVerify(2, '€');
  });

  it('should update and verify currency: AU$ (NE-TC-3269)', () => {
    settings.updateCurrencyAndVerify(3, 'AU$');
  });

  it('should update and verify currency: CA$ (NE-TC-3270)', () => {
    settings.updateCurrencyAndVerify(4, 'CA$');
  });

  it('should update and verify currency: AED (NE-TC-3271)', () => {
    settings.updateCurrencyAndVerify(5, 'AED');
  });

  it('should update and verify currency: HK$ (NE-TC-3272)', () => {
    settings.updateCurrencyAndVerify(6, 'HK$');
  });

  it('should update and verify currency: R (NE-TC-3273)', () => {
    settings.updateCurrencyAndVerify(7, 'R');
  });

  it('should update and verify currency: ₹ (NE-TC-3274)', () => {
    settings.updateCurrencyAndVerify(8, '₹');
  });

  it('should update and verify currency: ₣ (NE-TC-3275)', () => {
    settings.updateCurrencyAndVerify(9, '₣');
  });
});
