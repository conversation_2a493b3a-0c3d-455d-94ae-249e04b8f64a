import Login from '../../support/Login';
import Product from '../../support/Product';

describe('DefaultQCNotes', { tags: ['@Regression', '@CX'] }, () => {
  it('user should be able to view Default QC Requirements (NE-TC-2470)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    product.customerReferenceNotes();
    product.defaultQCNotes();
  });
});
