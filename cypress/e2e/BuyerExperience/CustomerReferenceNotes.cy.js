import Login from '../../support/Login';
import Product from '../../support/Product';

describe('CustomerReferenceNotes', { tags: ['@Regression', '@CX'] }, () => {
  it('user should be able to write notes when stone added to the cart (NE-TC-2472)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    product.customerReferenceNotes();
  });
});
