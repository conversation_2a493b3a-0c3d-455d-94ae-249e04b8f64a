import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('GemstoneAddToCart', { tags: ['@Regression', '@CX'] }, () => {
  it('Gemstone Add To Cart (NE-TC-2418)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getgemstonecert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.addProductToCart('gemstonecert.json');
  });
});
