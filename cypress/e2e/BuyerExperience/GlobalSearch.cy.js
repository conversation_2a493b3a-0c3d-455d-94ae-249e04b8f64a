import Login from '../../support/Login';
import Product from '../../support/Product';

describe('GlobalSearch', { tags: ['@Regression', '@CX'] }, () => {
  const login = new Login();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Natural Diamond Global Search NE-TC-2410', () => {
    product.globalStoneSearch('naturaldiamondcert.json');
  });

  it('Labgrown Diamond Global Search NE-TC-2411', () => {
    product.globalStoneSearch('labdiamondcert.json');
  });

  it('Gems Global Search NE-TC-3276', () => {
    product.globalStoneSearch('gemstonecert.json');
  });
});
