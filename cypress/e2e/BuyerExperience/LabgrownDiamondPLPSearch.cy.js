import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('LabgrownDiamondPLPSearch', { tags: ['@Regression', '@CX'] }, () => {
  it('Labgrown Diamond PLP Search (NE-TC-2390)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabdiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.verifyCertificateNumber('labdiamondcert.json');
  });
});
