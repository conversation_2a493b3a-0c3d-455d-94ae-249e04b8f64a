import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('LabgrownDiamondAddToCart', { tags: ['@Regression', '@CX'] }, () => {
  it('Labgrown Diamond Add To Cart.cy (NE-TC-3229)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabdiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.addProductToCart('labdiamondcert.json');
  });
});
