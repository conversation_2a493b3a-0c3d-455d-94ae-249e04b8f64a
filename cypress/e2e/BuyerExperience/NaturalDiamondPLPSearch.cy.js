import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('NaturalDiamondPLPSearch', { tags: ['@Regression'] }, () => {
  it('Natural Diamond PLP Search (NE-TC-2389)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getnaturaldiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalDiamonds();
    product.verifyCertificateNumber('naturaldiamondcert.json');
  });
});
