import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import Product from '../../support/Product';

describe('ValidateLabGrownDiamondSCSFilter', { tags: ['@Regression', '@scs', '@CX'] }, () => {
  it('Validate SCS Filters For LabGrown Diamond (NE-TC-2488)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    filter.applyScsFilter();
    product.verifyScsFilterAndContents();
  });
});
