import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';

describe('ValidateLabGrownMeleeColourFilter', { tags: ['@Regression', '@PLP-Filters', '@CX'] }, () => {
  it('Validate Color Filters For LabGrown Melee', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabGrownMelee();
    filter.verifyColourForMelee();
  });
});
