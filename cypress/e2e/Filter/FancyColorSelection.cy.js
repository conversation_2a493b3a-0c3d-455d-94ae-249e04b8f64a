import Login from '../../support/Login';
import Filter from '../../support/Filter';

describe('Verify Fancy Color Filter', { tags: ['@Regression', '@PLP-Filters', '@CX'] }, () => {
  it('should apply the Fancy Color filter correctly (NE-TC-2381)', () => {
    const login = new Login();
    const filter = new Filter();

    login.loginUsingApi('morcustomer.json');

    filter.selectAllFilters();
    filter.selectFancyColor();
    filter.applyDiamondFilters();
  });
});
