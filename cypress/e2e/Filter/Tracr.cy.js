import Login from '../../support/Login';
import Filter from '../../support/Filter';

describe('Verify Tracr Filter', { tags: ['@Regression', '@PLP-Filters', '@CX'] }, () => {
  it('Should apply tracr filter correctly for diamonds (NE-TC-2819)', () => {
    const login = new Login();
    const filter = new Filter();

    login.loginUsingApi('morcustomer.json');

    filter.selectAllFilters();
    filter.selectTracr();
    filter.applyDiamondFilters();
  });
});
