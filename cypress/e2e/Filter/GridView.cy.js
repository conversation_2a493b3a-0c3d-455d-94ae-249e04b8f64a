import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';

describe('VerifyGridView', { tags: ['@Regression', '@PLP-Filters', '@CX'] }, () => {
  const login = new Login();
  const filter = new Filter();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Grid view visibility for Natural Diamonds (NE-TC-2382)', () => {
    navBar.visitNaturalDiamonds();
    filter.gridViewClick();
  });

  it('Verify Grid view visibility for Lab Grown Diamonds (NE-TC-2383)', () => {
    navBar.visitLabgrownDiamonds();
    filter.gridViewClick();
  });

  it('Verify Grid view visibility For Gemstones (NE-TC-2384)', () => {
    navBar.visitGemStones();
    filter.gridViewClick();
  });

  it('Verify Grid view visibility for Natural Melee (NE-TC-2385)', () => {
    navBar.visitNaturalMelee();
    filter.gridViewClick();
  });

  it('Verify Grid view visibility for Lab Grown Melee (NE-TC-2386)', () => {
    navBar.visitLabGrownMelee();
    filter.gridViewClick();
  });
});
