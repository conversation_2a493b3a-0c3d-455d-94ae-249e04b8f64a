import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';

describe('ValidateMeleeLabGrownShapefilterInBothViews', { tags: ['@Regression', '@PLP-Filters'] }, () => {
  it('Verify Shape Filters For Lab Grown Melees In Both List View And Grid View', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabGrownMelee();
    filter.labgrownMeleeShapeFilterAssertionInBothViews();
  });
});
