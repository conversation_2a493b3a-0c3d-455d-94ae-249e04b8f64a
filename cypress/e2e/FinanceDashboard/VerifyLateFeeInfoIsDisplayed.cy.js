import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyLateFeeInfoIsDisplayed', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Late Fee Info Is Displayed After Hovering On Late Fees Section (NE-TC-2033)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('overdueFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.lateFeeAssertion();
  });
});
