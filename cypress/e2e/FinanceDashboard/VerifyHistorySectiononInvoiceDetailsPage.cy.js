import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyHistorySectionInInvoiceDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify The History Section For An Invoice In Invoice Details Page (NE-TC-2042)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.historySectionAssertion('AllTabFinancesData.json');
  });
});
