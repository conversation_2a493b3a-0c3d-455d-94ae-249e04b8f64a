import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyDueDateFieldIsGrayedOutInSortByForCreditNotesTab',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Due Date Field Is Greyed Out In Sort By Under Credit Notes Tab (NE-TC-2024)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      login.loginUsingApi('morcustomer.json');
      menu.visitFinancesPage();
      financeDashboard.visitCreditNoteHeader();
      financeDashboard.dueDateDisabledInSorting();
    });
  }
);
