import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyStatusFilterIsNotDisplayedForPaidTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Staus Filter Is Not Displayed For Paid Tab On Finance Dashboard (NE-TC-2023)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.visitPaidTab();
    financeDashboard.statusFilterNotVisibleAssertion();
  });
});
