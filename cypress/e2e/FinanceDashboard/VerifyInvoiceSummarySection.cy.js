import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyInvoiceSummarySection', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Invoice Summary Section For An Invoice (NE-TC-2043)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.invoiceSummaryAssertion('AllTabFinancesData.json');
    financeDashboard.downloadAndViewInvoice('AllTabFinancesData.json');
  });
});
