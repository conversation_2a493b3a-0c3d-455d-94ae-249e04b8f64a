import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyOrderItemSectionInCreditNoteInvoiceDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify The Order Item Section For A Credit Note Invoice In Invoice Details Page (NE-TC-2037)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.orderItemSectionAssertion('creditNoteFinances.json');
  });
});
