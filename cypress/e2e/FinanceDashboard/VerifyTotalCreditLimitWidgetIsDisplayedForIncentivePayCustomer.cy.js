import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyTotalCreditLimitWidgetIsVisibleForIncentivePayUser',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Total Credit Limit Widget Is Visible For An Incentive Pay Customer (NE-TC-2019)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      cy.getincentivepayaipuser();
      login.loginUsingApi('incentivepayaipuser.json');
      menu.visitFinancesPage();
      financeDashboard.totalUnpaidAndOverdueWidgetAssertion();
      financeDashboard.creditLimitWidgetVisibleAssertion('incentivepayaipuser.json');
    });
  }
);
