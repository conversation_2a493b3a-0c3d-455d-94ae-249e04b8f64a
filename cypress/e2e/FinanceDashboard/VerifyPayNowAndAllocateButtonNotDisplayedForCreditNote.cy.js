import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyPayNowAndAllocateButtonNotDisplayedForCreditNote',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Pay Now And Allocate Button Are Not Displayed For A Credit Note (NE-TC-2011)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      login.loginUsingApi('morcustomer.json');
      menu.visitFinancesPage();
      financeDashboard.visitCreditNoteHeader();
      financeDashboard.payNowButtonNotVisibleAssertion();
    });
  }
);
