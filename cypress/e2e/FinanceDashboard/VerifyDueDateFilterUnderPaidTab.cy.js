import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyDueDateFilterUnderPaidTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Due Date Filter Under Paid Tab (NE-TC-2027)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.visitPaidTab();
    financeDashboard.dueDateFilterAssertion();
  });
});
