import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyDueDateFilterUnderAllTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Due Date Filter Under All Tab (NE-TC-2025)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.dueDateFilterAssertion();
  });
});
