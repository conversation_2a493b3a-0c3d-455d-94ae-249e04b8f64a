import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifIssueDateFilterUnderCreditNotesTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Issue Date Filter Under Credit Notes Tab (NE-TC-2028)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.visitCreditNoteHeader();
    financeDashboard.issueDateFilterAssertion();
  });
});
