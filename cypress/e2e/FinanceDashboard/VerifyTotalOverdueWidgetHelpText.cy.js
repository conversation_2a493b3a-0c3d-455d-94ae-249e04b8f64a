import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyTotalOverdueWidgetHelpText', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Help Text Under Total Overdue Widget By Hovering On the ? Icon (NE-TC-2030)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    cy.getTotalOvderDueData();

    login.loginUsingApi('totalOverdue.json');
    menu.visitFinancesPage();
    financeDashboard.totalOverdueWidgetHelpTextAssertion();
  });
});
