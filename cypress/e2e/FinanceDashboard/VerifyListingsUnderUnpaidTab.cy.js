import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyListingsUnderUnpaidTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Listings Under Unpaid Tab (NE-TC-2014)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('UnpaidTabData.json');
    menu.visitFinancesPage();
    financeDashboard.visitUnPaidTab();
    financeDashboard.searchInvoice('UnpaidTabData.json');
  });
});
