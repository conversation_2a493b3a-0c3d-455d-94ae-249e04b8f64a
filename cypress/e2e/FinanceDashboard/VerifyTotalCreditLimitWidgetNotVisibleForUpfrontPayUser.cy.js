import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyTotalCreditLimitWidgetIsNotVisibleForUpfrontUser',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Total Credit Limit Widget Is Not Visible For An Upfront Pay Customer (NE-TC-2018)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      login.loginUsingApi('upfrontaipuser.json');
      menu.visitFinancesPage();
      financeDashboard.totalUnpaidAndOverdueWidgetAssertion();
      financeDashboard.creditLimitWidgetNotVisibleAssertion();
    });
  }
);
