import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyKriyaLimitIsDisplayeInTotalCreditLimitInLocalCurrency',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Kriya limit is displayed in Total credit limit in customer Local Currency (NE-TC-2021)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      cy.getKriyaCustomer();

      login.loginUsingApi('kriyaUser.json');
      menu.visitFinancesPage();
      financeDashboard.totalUnpaidAndOverdueWidgetAssertion();
      financeDashboard.creditLimitWidgetVisibleAssertion('kriyaUser.json');
    });
  }
);
