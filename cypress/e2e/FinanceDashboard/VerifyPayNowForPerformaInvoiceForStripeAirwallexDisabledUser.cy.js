import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyPayNowForPerformaInvoiceForAirWallexAndStripeDisabledUser',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Nivoda Bank Details Are Shown To Airwallex User After Clicking Pay Now For Performa Invoice (NE-TC-2034)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      login.loginUsingApi('airwallexStripDisabledUser.json');
      menu.visitFinancesPage();
      financeDashboard.searchInvoice('airwallexStripDisabledUser.json');
      financeDashboard.airwallexAndStripeDisabledPayInvoice();
    });
  }
);
