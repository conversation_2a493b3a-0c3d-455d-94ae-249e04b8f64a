import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyListingsUnderAllTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Listings Under All Tab (NE-TC-2012)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.verifyAllTabistingData('AllTabFinancesData.json');
  });
});
