import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyBulkDownloadForTableListings', { tags: ['@Smoke', '@FinanceDashboard'] }, () => {
  it('Verify User Can Download Invoices In Bulk (NE-TC-2016)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.bulkDownloadInvoices();
  });
});
