import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyManualBankTransferForStripeUserWith30DayInvoice',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Manual Bank Transfer For A Striper User With 30 days Invoice (NE-TC-2036)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      login.loginUsingApi('stripeAndAirwallexUserWith30DayInvoice.json');
      menu.visitFinancesPage();
      financeDashboard.searchInvoice('stripeAndAirwallexUserWith30DayInvoice.json');
      financeDashboard.payInvoice();
    });
  }
);
