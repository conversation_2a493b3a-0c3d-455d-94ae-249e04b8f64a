import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyTabsDisplayedOnInvoiceDashboard', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Tabs Displayed on Invoice Dashboard (NE-TC-2029)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.invoiceTabsAssertion();
  });
});
