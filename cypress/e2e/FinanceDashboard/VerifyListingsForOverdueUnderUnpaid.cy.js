import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyListingsForOverdue', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Listings For Overdue Invoices (NE-TC-2013)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('overdueFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('overdueFinancesData.json');
    financeDashboard.verifyOverdueInvoiceData('overdueFinancesData.json');
  });
});
