import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyOrderItemSectionInInvoiceDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify The Order Item Section For An Invoice In Invoice Details Page (NE-TC-2041)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.orderItemSectionAssertion('AllTabFinancesData.json');
  });
});
