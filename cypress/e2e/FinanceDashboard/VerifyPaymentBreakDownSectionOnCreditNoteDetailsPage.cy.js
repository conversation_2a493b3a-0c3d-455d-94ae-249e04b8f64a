import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyPaymentBreakDownSectionOnCreditNoteDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Payment Breakdown Section For An Invoice For A Credit Note (NE-TC-2040)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.paymentBreakdownAssertionForCreditNote('creditNoteFinances.json');
  });
});
