import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyInvoiceSummarySectionOnCreditNoteDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Invoice Summary Section For An Invoice On Credit Note Details Page (NE-TC-2039)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.invoiceSummaryAssertionForCreditNotes('creditNoteFinances.json');
    financeDashboard.downloadAndViewInvoice('creditNoteFinances.json');
  });
});
