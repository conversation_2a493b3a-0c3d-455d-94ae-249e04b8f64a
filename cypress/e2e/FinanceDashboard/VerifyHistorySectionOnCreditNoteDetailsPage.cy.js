import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyHistorySectionForCreditNotesDetailsPage', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify The History Section For A Credit Note Invoice In Invoice Details Page (NE-TC-2038)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.historySectionAssertion('creditNoteFinances.json');
  });
});
