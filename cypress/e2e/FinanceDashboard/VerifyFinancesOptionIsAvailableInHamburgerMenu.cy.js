import Login from '../../support/Login';
import Menu from '../../support/Menu';

describe('VerifyFiancesOptionIsVisibleInHamburgerMenu', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify User Can Visit Finance Dashboard (NE-TC-2017)', () => {
    const login = new Login();
    const menu = new Menu();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
  });
});
