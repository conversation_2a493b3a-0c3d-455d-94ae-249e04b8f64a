import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyAmountDisplayedInTotalOverdueWidget', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Amount Displayed In Total Overdue Widget Is Correct (NE-TC-2032)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    cy.getTotalOvderDueData();

    login.loginUsingApi('totalOverdue.json');
    menu.visitFinancesPage();
    financeDashboard.totalOverdueAssertion('totalOverdue.json');
  });
});
