import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyTotalCreditLimitWidgetIsVisibleForCreditPayUser',
  { tags: ['@FinanceDashboard', '@Regression'] },
  () => {
    it('Verify Total Credit Limit Widget Is Visible For Credit Pay Customer (NE-TC-2020)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      cy.getCreditUser();

      login.loginUsingApi('credituser.json');
      menu.visitFinancesPage();
      financeDashboard.totalUnpaidAndOverdueWidgetAssertion();
      financeDashboard.creditLimitWidgetVisibleAssertion('credituser.json');
    });
  }
);
