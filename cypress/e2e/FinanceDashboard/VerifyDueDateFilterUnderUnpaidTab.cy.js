import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyDueDateFilterUnderUnpaidTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Due Date Filter Under Unpaid Tab (NE-TC-2026)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
    financeDashboard.visitUnPaidTab();
    financeDashboard.dueDateFilterAssertion();
  });
});
