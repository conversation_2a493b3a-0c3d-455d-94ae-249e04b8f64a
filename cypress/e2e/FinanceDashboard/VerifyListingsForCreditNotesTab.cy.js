import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyListingsForCreditNotesTab', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  it('Verify Listings For The Credit Notes Tab (NE-TC-2015)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('creditNoteFinances.json');
    menu.visitFinancesPage();
    financeDashboard.visitCreditNoteHeader();
    financeDashboard.verifyCreditNoteListingData('creditNoteFinances.json');
  });
});
