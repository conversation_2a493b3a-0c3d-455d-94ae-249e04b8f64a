import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import JewellerySKU from '../../support/JewelleryDashboard';

describe('Edit Jewellery SKU', () => {
  it('Verify user can Edit SKU (NE-TC-2821)', () => {
    const login = new Login();
    const accessadmintabs = new AccessAdminTabs();
    const jewellerysku = new JewellerySKU();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();

    accessadmintabs.accessTabs('Jewellery Dashboard', 'Stock keeping unit', '', '/jewellery/sku');
    jewellerysku.editSKU('getJewellerySKU.json');
  });
});
