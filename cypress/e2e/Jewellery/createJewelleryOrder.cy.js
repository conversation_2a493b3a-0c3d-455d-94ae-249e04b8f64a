import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import JewelleryOrder from '../../support/JewelleryDashboard';

describe('Create Jewellery Order (NE-TC-2542)', { tags: ['@Regression'] }, () => {
  const login = new Login();
  const accessadmintabs = new AccessAdminTabs();
  const jewelleryorder = new JewelleryOrder();

  beforeEach(() => {
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessadmintabs.accessTabs(
      'Jewellery Dashboard',
      'Create jewellery order',
      '',
      '/jewellery/create-custom-jewellery-order'
    );
  });
  it('Verify the Creation of new Jewellery Order with all the information (NE-TC-2544)', () => {
    jewelleryorder.createOrder('getJewellerySKUexcludingJR.json');
  });
  it('Verify the Creation of new Jewellery Order without data (NE-TC-2543)', () => {
    jewelleryorder.createOrderwithoutdata('getJewellerySKUexcludingJR.json');
  });
});
