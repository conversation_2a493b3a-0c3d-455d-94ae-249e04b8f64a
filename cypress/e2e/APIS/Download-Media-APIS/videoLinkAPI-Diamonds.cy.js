describe('Get Video Link via API For Diamonds', { tags: '@API' }, () => {
  it('User Should Get Video Link via API For Diamonds', function () {
    const password = Cypress.env('customerPassword');

    cy.getNaturalDiamondData().then((certData) => {
      const { certId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({}) => {
        cy.diamondsDownloadLinkAPI(certId);
      });
    });
  });
});
