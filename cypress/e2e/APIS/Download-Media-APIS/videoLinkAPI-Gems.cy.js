describe('Get Video Link via API For Gems', { tags: '@API' }, () => {
  it('User Should Get Video Link via API For Gems', function () {
    const password = Cypress.env('customerPassword');

    cy.getGemstonesData().then((certData) => {
      const { certId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({}) => {
        cy.gemsDownloadLinkAPI(certId);
      });
    });
  });
});
