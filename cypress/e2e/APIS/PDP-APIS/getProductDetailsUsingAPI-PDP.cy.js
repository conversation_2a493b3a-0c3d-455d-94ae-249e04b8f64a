describe('Get Product Details via API For PDP', { tags: '@API' }, () => {
  it('User Should Get Product Details via API For PDP', function () {
    const password = Cypress.env('customerPassword');

    cy.getNaturalDiamondData().then((certData) => {
      const { diamondId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({}) => {
        cy.pdpDataAPI(diamondId);
      });
    });
  });
});
