describe('Melee SR Request Via API', { tags: '@API' }, () => {
  it('Authenticates a user, Generates a Melee SR Request', function () {
    const password = Cypress.env('customerPassword');

    cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userId }) => {
      cy.createMeleeRequest(
        'ROUND',
        'VS1',
        'D',
        'EX',
        userId,
        'PLATFORM',
        'NATURAL',
        '',
        null,
        [{ carats: 5, mm_size: { from: 5, to: 6 } }],
        null,
        null,
        'carats'
      );
    });
  });
});
