describe('Gemstone Order Request via API', { tags: '@API' }, () => {
  it('Authenticates a user, Places an Order Request For Gemstone', function () {
    const password = Cypress.env('customerPassword');

    cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
      cy.createGemsOrderRequest(
        'CERTIFIED',
        '',
        'NATURAL',
        'SAPPHIRE',
        'BLUE',
        'ROUND',
        1,
        4,
        ['HEATED'],
        [],
        [],
        [],
        [],
        'GBP'
      );
    });
  });
});
