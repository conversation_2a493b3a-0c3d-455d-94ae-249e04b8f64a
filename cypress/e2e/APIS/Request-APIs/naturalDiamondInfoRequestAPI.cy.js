describe('Natural Diamond Info GIA Request via API', { tags: '@API' }, () => {
  it('Authenticates a user, Places a GIA Diamond Request For Natural Diamond', function () {
    const password = Cypress.env('customerPassword');

    cy.getnaturaldiamondinforequest();

    cy.fixture('naturaldiamondinforequest.json').then(({ 0: { certNumber } }) => {
      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userId }) => {
        cy.createGIARequest(
          certNumber,
          userId,
          'Diamond Request',
          false,
          '',
          null,
          'GIA',
          false,
          ['EYECLEAN', 'SHADE'],
          'OPEN',
          'INFO'
        );
      });
    });
  });
});
