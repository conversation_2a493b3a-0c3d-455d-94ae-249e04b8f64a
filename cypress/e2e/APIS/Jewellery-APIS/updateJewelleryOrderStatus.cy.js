describe('CreateJewelleryOrder', { tags: '@API' }, () => {
  it('Create A Jewellery Order Using the API', function () {
    const password = Cypress.env('customerPassword');
    cy.getcertnumber();
    cy.getJewellerySku();
    cy.authenticateUser('loginasadmin.json', password, null).then(({ userId }) => {
      cy.fixture('certificatepayload.json').then((data) => {
        const certificateId = data[0].certNumber;
        cy.log(certificateId);
        cy.searchCertificateByQuery(certificateId);

        cy.searchCustomerCompany('diamond');
        cy.createJewelleryOrder();

        cy.fixture('jewellerySku.json').then((jewelleryData) => {
          const jewelleryId = jewelleryData[0].id;

          cy.updateJewelleryStatus(jewelleryId, 'ORDER_ACCEPTED').then((updated) => {
            expect(updated.status).to.eq('ORDER_ACCEPTED');

            cy.updateJewelleryStatus(jewelleryId, 'ORDER_IN_PROCESS').then((processingUpdated) => {
              expect(processingUpdated.status).to.eq('ORDER_IN_PROCESS');
            });

            cy.updateJewelleryStatus(jewelleryId, 'ORDER_IN_PROCESS').then((processingUpdated) => {
              expect(processingUpdated.status).to.eq('ORDER_IN_PROCESS');
            });
          });
        });
      });
    });
  });
});
