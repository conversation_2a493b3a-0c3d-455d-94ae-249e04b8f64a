describe('GetJewelleryOrderStatus', { tags: '@API' }, () => {
  const password = Cypress.env('customerPassword');

  before(() => {
    cy.authenticateUser('loginasadmin.json', password, null);
  });

  it('Search Order With Status PURCHASE ORDER via API', function () {
    cy.getJewelleryOrdersByStatus('PURCHASE_ORDER');
  });

  it('Search Sku With Status Processing via API', function () {
    cy.getJewelleryOrdersByStatus('PROCESSING');
  });

  it('Search Sku With Status DELIVERED via API', function () {
    cy.getJewelleryOrdersByStatus('DELIVERED');
  });

  it('Search Sku With Status Ready TO Ship via API', function () {
    cy.getJewelleryOrdersByStatus('READY_TO_SHIP');
  });
});
