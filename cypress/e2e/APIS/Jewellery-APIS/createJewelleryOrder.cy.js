describe('CreateJewelleryOrder', { tags: '@API' }, () => {
  it('Create A Jewellery Order Using the API', function () {
    const password = Cypress.env('customerPassword');
    cy.getcertnumber();
    cy.getJewellerySku();
    cy.authenticateUser('loginasadmin.json', password, null).then(({ userId }) => {
      cy.fixture('cert.json').then((data) => {
        const certNumber = data[0].certNumber;
        cy.searchCertificateByQuery(certNumber);
        cy.searchCustomerCompany('diamond');
        cy.createJewelleryOrder();
      });
    });
  });
});
