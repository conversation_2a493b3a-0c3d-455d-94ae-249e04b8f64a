describe('Apply Fancy Color Filter For Diamonds', { tags: '@API' }, () => {
  it('Should Apply Fancy Color Filter For Diamonds', function () {
    const password = Cypress.env('customerPassword');

    cy.authenticateUser('upfrontaipuser.json', password, null).then(({}) => {
      cy.searchDiamondsByFilter({
        color: ['FANCY'],
        fancyColor: ['Blue', 'Red', 'Green']
      });
    });
  });
});
