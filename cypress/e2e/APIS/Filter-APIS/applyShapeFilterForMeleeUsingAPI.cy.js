describe('Apply Filters For Melee', { tags: '@API' }, () => {
  const password = Cypress.env('customerPassword');

  before(() => {
    cy.authenticateUser('upfrontaipuser.json', password, null);
  });

  it('should search for round and pear shaped melee diamonds', () => {
    cy.searchMeleeByFilter({
      shapes: ['ROUND', 'PEAR'],
      type: 'NATURAL'
    });
  });

  it('should search with color and clarity filters', () => {
    cy.searchMeleeByFilter({
      clarity: ['VVS', 'VS'],
      color: {
        white: ['D_F']
      },
      type: 'NATURAL'
    });
  });
});
