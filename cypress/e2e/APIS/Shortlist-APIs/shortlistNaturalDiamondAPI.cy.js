describe('Search and Shortlist a Natural Diamond via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a natural diamond, and adds it to the shortlist', function () {
    const password = Cypress.env('customerPassword');

    cy.getNaturalDiamondData().then((certData) => {
      const { certNumber, diamondId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchStone(certNumber);
        cy.addToShortlist(diamondId);
      });
    });
  });
});
