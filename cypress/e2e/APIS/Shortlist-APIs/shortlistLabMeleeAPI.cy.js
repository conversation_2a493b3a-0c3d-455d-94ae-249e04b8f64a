describe('Search and Shortlist a Lab Melee Stone via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a lab melee stone, and adds it to the shortlist', function () {
    const password = Cypress.env('customerPassword');

    cy.getLabMeleeAPIData().then((certData) => {
      const { NivodaStockId: certificateNumber, id: meleeId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchmelee(certificateNumber, 'LABGROWN').then(({ meleeid, pieces, carats }) => {
          cy.addToMeleeShortlist(meleeId, meleeid, pieces, carats);
        });
      });
    });
  });
});
