describe(
  'Search Memo Stone, Add to Shortlist and verify Added Natural Diamond in Shortlist via API',
  { tags: '@API' },
  () => {
    it('Authenticates a user, searches for a natural diamond on Memo , and adds it to the shortlist and verify', function () {
      const password = Cypress.env('customerPassword');

      cy.getMemoStone().then((certData) => {
        const { certNumber, diamondId } = certData[0];

        cy.authenticateUser('LoginasMemoEnabledUser.json', password, null).then(() => {
          cy.searchStone(certNumber);
          cy.addToShortlist(diamondId, { is_memo: true });
          cy.getShortlistItems('DIAMOND/' + diamondId);
        });
      });
    });
  }
);
