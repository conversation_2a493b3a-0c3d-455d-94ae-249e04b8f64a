describe('Search Stone, Add to Shortlist and verify Added Gemstone in Shortlist via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a gemstone, and adds it to the shortlist verify Added Gemstone in Shortlist', function () {
    const password = Cypress.env('customerPassword');

    cy.getGemstonesData().then((gemstones) => {
      const certificateNumber = gemstones[0].certNumber;
      const gemsId = gemstones[0].id;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchGemStone(certificateNumber);
        cy.addToShortlist(gemsId);
        cy.getShortlistItems('GEMSTONE/' + gemsId);
      });
    });
  });
});
