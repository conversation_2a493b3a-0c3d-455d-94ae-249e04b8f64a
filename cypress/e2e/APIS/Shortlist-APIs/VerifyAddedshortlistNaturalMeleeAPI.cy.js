describe(
  'Search and Shortlist a Natural Melee Stone and Verify Its Availble In Shortlist via API',
  { tags: '@API' },
  () => {
    it('Authenticates a user, searches for a natural melee stone, and adds it to the shortlist', function () {
      const password = Cypress.env('customerPassword');

      cy.getMeleeAPIData().then((certData) => {
        const { NivodaStockId: certificateNumber, id: meleeId } = certData[0];

        cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
          cy.searchmelee(certificateNumber).then(({ meleeid, pieces, carats }) => {
            cy.addToMeleeShortlist(meleeId, meleeid, pieces, carats);
            cy.getShortlistItems('MELEE/' + meleeId);
          });
        });
      });
    });
  }
);
