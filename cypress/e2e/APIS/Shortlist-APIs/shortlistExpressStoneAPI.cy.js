describe('Search and Shortlist an Express Stone via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for an Express Stone, and adds it to the shortlist', function () {
    const password = Cypress.env('customerPassword');

    cy.getExpressStone().then((certData) => {
      const { certNumber, diamondId } = certData[0];

      cy.authenticateUser('nivodaexpressuser.json', password, null).then(() => {
        cy.searchStone(certNumber);
        cy.addToShortlist(diamondId);
      });
    });
  });
});
