describe('Search Stone, Add to Shortlist and verify Added lab diamond in Shortlist via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a lab diamond, and adds it to the shortlist and verify', function () {
    const password = Cypress.env('customerPassword');

    cy.getLabDiamondData().then((certData) => {
      const { certNumber, diamondId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchStone(certNumber);
        cy.addToShortlist(diamondId);
        cy.getShortlistItems('DIAMOND/' + diamondId);
      });
    });
  });
});
