describe('Search and Shortlist a Gemstone via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a gemstone, and adds it to the shortlist', function () {
    const password = Cypress.env('customerPassword');

    cy.getGemstonesData().then((gemstones) => {
      const certificateNumber = gemstones[0].certNumber;
      const gemsId = gemstones[0].id;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchGemStone(certificateNumber);
        cy.addToShortlist(gemsId);
      });
    });
  });
});
