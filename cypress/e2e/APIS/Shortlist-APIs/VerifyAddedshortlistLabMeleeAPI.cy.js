describe('Search Stone, Add to Shortlist and verify Added lab Melee in Shortlist via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a lab melee stone, and adds it to the shortlist abd verify', function () {
    const password = Cypress.env('customerPassword');

    cy.getLabMeleeAPIData().then((certData) => {
      const { NivodaStockId: certificateNumber, id: meleeId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
        cy.searchmelee(certificateNumber, 'LABGROWN').then(({ meleeid, pieces, carats }) => {
          cy.addToMeleeShortlist(meleeId, meleeid, pieces, carats);
          cy.getShortlistItems(meleeid);
        });
      });
    });
  });
});
