describe('Export Order Via API', { tags: '@API' }, () => {
  it('Authenticates a User and Exports an Order via API', function () {
    const password = Cypress.env('customerPassword');

    cy.authenticateUser('upfrontaipuser.json', password, null).then(() => {
      cy.getAllOrders('Diamond', 'ALL').then((orderItemIds) => {
        cy.exportOrderAPI(orderItemIds[0], {}, 'ALL');
      });
    });
  });
});
