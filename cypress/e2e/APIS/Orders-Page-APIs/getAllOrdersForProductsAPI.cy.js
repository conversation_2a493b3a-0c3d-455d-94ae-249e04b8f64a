describe('Get All Orders via API', { tags: '@API' }, () => {
  const password = Cypress.env('customerPassword');
  let userEmail;

  beforeEach(() => {
    cy.authenticateUser('upfrontaipuser.json', password, null);
  });

  context('Validating Orders for Different Product Types', () => {
    it(`should fetch orders for Product Type: Diamond`, () => {
      cy.getAllOrders('Diamond', 'ALL');
    });

    it(`should fetch orders for Product Type: Gemstone`, () => {
      cy.getAllOrders('Gemstone', 'ALL');
    });

    it(`should fetch orders for Product Type: Melee`, () => {
      cy.getAllOrders('Melee', 'ALL');
    });
  });
});
