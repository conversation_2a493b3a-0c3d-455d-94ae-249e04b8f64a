describe('Search Express Stones And Place Order', { tags: '@API' }, () => {
  it('Should Search An Express Stone And Place Upfront Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getExpressStone().then((certData) => {
      const certificateNumber = certData[0].certNumber;

      cy.authenticateUser('nivodaexpressuser.json', password, null).then(({ userEmail }) => {
        cy.searchStone(certificateNumber);
        cy.getDestinationForApiUser(userEmail);
        cy.updateCart();
        cy.requestAuth();
        cy.getAPIOTP();
        cy.createOrder();
        cy.getOrder();
      });
    });
  });
});
