describe('Search Nivoda Curated Gemstones And Place Order', { tags: '@API' }, () => {
  it('Should Search A Nivoda Curated Gemstone And Place Upfront Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getUpfrontUser();
    cy.getCuratedGemstonesData().then((certData) => {
      const certificateNumber = certData[0].certNumber;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userEmail }) => {
        cy.searchGemStone(certificateNumber);
        cy.getDestinationForApiUser(userEmail);
        cy.updateCart();
        cy.requestAuth();
        cy.getAPIOTP();
        cy.createOrder();
        cy.getOrder();
      });
    });
  });
});
