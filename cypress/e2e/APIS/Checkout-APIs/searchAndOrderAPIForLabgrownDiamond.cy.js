describe('Search Lab Diamond And Place Order', { tags: '@API' }, () => {
  it('Should Search A LabGrown Diamond And Place Upfront Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getUpfrontUser();
    cy.getLabDiamondData().then((certData) => {
      const certificateNumber = certData[0].certNumber;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userEmail }) => {
        cy.searchStone(certificateNumber);
        cy.getDestinationForApiUser(userEmail);
        cy.updateCart();
        cy.requestAuth();
        cy.getAPIOTP();
        cy.createOrder();
        cy.getOrder();
      });
    });
  });
});
