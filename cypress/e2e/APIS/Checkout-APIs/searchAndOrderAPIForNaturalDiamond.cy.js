describe('Search Natural Diamonds And Place Order', { tags: '@API' }, () => {
  it('Should Search A Natural Diamond And Place Upfront Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getUpfrontUser();
    cy.getNaturalDiamondData().then((certData) => {
      const certificateNumber = certData[0].certNumber;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userEmail }) => {
        cy.searchStone(certificateNumber);
        cy.getDestinationForApiUser(userEmail);
        cy.updateCart();
        cy.requestAuth();
        cy.getAPIOTP();
        cy.createOrder();
        cy.getOrder();
      });
    });
  });
});
