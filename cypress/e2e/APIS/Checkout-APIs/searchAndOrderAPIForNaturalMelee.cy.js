describe('Search Natural Melee Stone And Place Order', { tags: '@API' }, () => {
  it('Should Search A Natural Melee Stone And Place Upfront Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getUpfrontUser();
    cy.getMeleeAPIData().then((certData) => {
      const certificateNumber = certData[0].NivodaStockId;

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userEmail }) => {
        cy.searchmelee(certificateNumber).then(({ meleeid, pieces }) => {
          cy.getDestinationForApiUser(userEmail);
          cy.updateMeleeCart(meleeid, pieces);
          cy.requestAuth();
          cy.getAPIOTP();
          cy.createOrder();
          cy.getOrder();
        });
      });
    });
  });
});
