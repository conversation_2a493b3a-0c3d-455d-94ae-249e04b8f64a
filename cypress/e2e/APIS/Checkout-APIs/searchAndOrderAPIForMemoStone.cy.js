describe('Search Natural Diamonds And Place Order For Memo ', { tags: '@API' }, () => {
  it('Should Search A Natural Diamond And Place Memo Order', function () {
    const password = Cypress.env('customerPassword');

    cy.getMemoStone().then((certData) => {
      const certificateNumber = certData[0].certNumber;

      cy.authenticateUser('LoginasMemoEnabledUser.json', password, null).then(({ userEmail }) => {
        cy.searchStone(certificateNumber);
        cy.getDestinationForApiUser(userEmail);
        cy.addStoneToMemoWallet();
        cy.requestAuth();
        cy.getAPIOTP();
        cy.createOrder({ isMemoItem: true, paymentTerm: 'cr_30_payment' });
        cy.getOrder();
      });
    });
  });
});
