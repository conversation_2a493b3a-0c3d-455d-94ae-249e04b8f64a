describe('Search and Hold a Natural Diamond via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a natural diamond, and places a hold', function () {
    const password = Cypress.env('customerPassword');

    cy.getNaturalDiamondData().then((certData) => {
      const { certNumber, diamondId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userId }) => {
        cy.searchStone(certNumber);
        cy.createHold('Diamond', userId, diamondId);
      });
    });
  });
});
