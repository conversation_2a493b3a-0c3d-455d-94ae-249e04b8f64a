describe('Search and Hold a Gemstone via API', { tags: '@API' }, () => {
  it('Authenticates a user, searches for a Gemstone, and places a hold', function () {
    const password = Cypress.env('customerPassword');

    cy.getGemstonesData().then((certData) => {
      const { certNumber, id: gemsId } = certData[0];

      cy.authenticateUser('upfrontaipuser.json', password, null).then(({ userId }) => {
        cy.searchGemStone(certNumber);
        cy.createHold('Gemstone', userId, gemsId);
      });
    });
  });
});
