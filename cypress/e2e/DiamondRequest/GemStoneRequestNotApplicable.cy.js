import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('GemStoneRequestNotApplicable', () => {
  it.skip('GemStone Request Not Applicable (NE-TC-675)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabgrowndiamondrequest();

    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.requestNotApplicable(
      'cypress/fixtures/labgrowndiamondrequest.json',
      "We couldn't find anything matching your search."
    );
  });
});
