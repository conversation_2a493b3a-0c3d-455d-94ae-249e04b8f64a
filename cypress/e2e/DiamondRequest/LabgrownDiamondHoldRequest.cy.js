import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Navbar from '../../support/Navbar';

describe('LabgrownDiamondHoldRequest', { tags: ['@Diamond-Request', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const navbar = new Navbar();
  const diamondRequests = new DiamondRequests();

  it('Labgrown Diamond Hold Request (NE-TC-674)', () => {
    cy.getlabgrowndiamondrequest();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.diamondRequest(
      'labgrowndiamondrequest.json',
      'Add to stock & hold item',
      'Your request was created successfully!'
    );
  });
  it('Verify Labgrown Diamond Hold Request (NE-TC-674)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    diamondRequests.VerifyGIAPurchaseOrderRequest('labgrowndiamondrequest.json');
  });
});
