import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('NaturalDiamondPurchaseRequestCompleteFlow', { tags: ['@Diamond-Request', '@Smoke'] }, () => {
  const login = new Login();
  const diamondRequests = new DiamondRequests();
  const menu = new Menu();
  const orders = new Orders();
  const product = new Product();

  it('Create a Purchase Diamond request - Natural Diamond (NE-TC-669)', () => {
    cy.getnaturaldiamondbuyrequest();

    login.loginUsingApi('morcustomer.json');
    product.diamondRequest(
      'cypress/fixtures/naturaldiamondbuyrequest.json',
      'Add to stock & create an order',
      'Your request was created successfully!'
    );
  });
  it('Add Diamond and Place Order - Admin (NE-TC-678)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    diamondRequests.VerifyGIAPurchaseOrderRequest('cypress/fixtures/naturaldiamondbuyrequest.json');
    diamondRequests.visitBuyRequestTab();
    diamondRequests.addDiamondAndPerformAction('cypress/fixtures/upfrontpayorder.json');
  });
  it('Add Diamond and Place Order - Admin (NE-TC-678)', () => {
    login.loginUsingApi('morcustomer.json');
    menu.visitOrdersPage();
    orders.searchOrderNumber('cypress/fixtures/naturaldiamondbuyrequest.json');
  });
});
