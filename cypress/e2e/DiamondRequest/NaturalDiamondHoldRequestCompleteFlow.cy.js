import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Menu from '../../support/Menu';
import AdminMenu from '../../support/Admin/Menu';

describe('NaturalDiamondHoldRequestCompleteFlow', { tags: ['@Diamond-Request', '@Regression'] }, () => {
  it('Natural Diamond Hold Request Complete Flow (NE-TC-673) (NE-TC-679)', () => {
    const login = new Login();
    const product = new Product();
    const diamondRequests = new DiamondRequests();
    const menu = new Menu();
    const adminMenu = new AdminMenu();

    cy.getnaturaldiamondholdrequest();

    login.loginUsingApi('morcustomer.json');
    product.diamondRequest(
      'cypress/fixtures/naturaldiamondholdrequest.json',
      'Add to stock & hold item',
      'Your request was created successfully!'
    );
    menu.visitMenu();
    menu.VerifyLogout();

    login.loginUsingAdminApi('loginasadmin.json');
    diamondRequests.VerifyGIAPurchaseOrderRequest('cypress/fixtures/naturaldiamondholdrequest.json');
    diamondRequests.visitHoldRequestTab();
    diamondRequests.addDiamondAndPerformAction('cypress/fixtures/upfrontpayorder.json');
    adminMenu.visitHoldRequest();
    adminMenu.assertCertOnHoldsPage('cypress/fixtures/naturaldiamondholdrequest.json');
  });
});
