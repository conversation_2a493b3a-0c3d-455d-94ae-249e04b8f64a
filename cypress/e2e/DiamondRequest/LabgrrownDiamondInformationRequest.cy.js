import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Navbar from '../../support/Navbar';

describe('NaturalDiamondInformationRequest', { tags: ['@Diamond-Request', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const navbar = new Navbar();
  const diamondRequests = new DiamondRequests();
  it('Natural Diamond Information Request (NE-TC-671)', () => {
    cy.getlabgrowndiamondrequest();
    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.diamondRequest(
      'labgrowndiamondrequest.json',
      'Add to stock & request info',
      'Your request was created successfully!'
    );
  });
  it('Verify Natural Diamond Information Request (NE-TC-671)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    diamondRequests.VerifyGIAPurchaseOrderRequest('labgrowndiamondrequest.json');
  });
});
