import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Navbar from '../../support/Navbar';

describe('LabgrrownDiamondPurchaseRequest', { tags: ['@Diamond-Request', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const navbar = new Navbar();
  const diamondRequests = new DiamondRequests();

  it('Labgrrown Diamond Purchase Request (NE-TC-670)', { tags: '@LastFailed' }, () => {
    cy.getlabgrowndiamondrequest();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.diamondRequest(
      'labgrowndiamondrequest.json',
      'Add to stock & create an order',
      'Your request was created successfully!'
    );
  });
  it('Verify Labgrrown Diamond Purchase Request (NE-TC-670)', { tags: '@LastFailed' }, () => {
    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    diamondRequests.VerifyGIAPurchaseOrderRequest('labgrowndiamondrequest.json');
  });
});
