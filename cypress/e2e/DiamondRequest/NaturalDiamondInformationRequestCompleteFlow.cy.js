import Login from '../../support/Login';
import Product from '../../support/Product';
import DiamondRequests from '../../support/Admin/DiamondRequests';
import Menu from '../../support/Menu';
import AdminMenu from '../../support/Admin/Menu';

describe('NaturalDiamondInformationRequestCompleteFlow', { tags: ['@Diamond-Request', '@Regression'] }, () => {
  it('Natural Diamond Information Request Complete Flow (NE-TC-672) (NE-TC-680)', () => {
    const login = new Login();
    const product = new Product();
    const diamondRequests = new DiamondRequests();
    const menu = new Menu();
    const adminMenu = new AdminMenu();

    cy.getnaturaldiamondinforequest();

    login.loginUsingApi('morcustomer.json');
    product.diamondRequest(
      'cypress/fixtures/naturaldiamondinforequest.json',
      'Add to stock & request info',
      'Your request was created successfully!'
    );
    menu.visitMenu();
    menu.VerifyLogout();

    login.loginUsingAdminApi('loginasadmin.json');
    diamondRequests.VerifyGIAPurchaseOrderRequest('cypress/fixtures/naturaldiamondinforequest.json');
    diamondRequests.visitInfoRequestTab();
    diamondRequests.addDiamondAndPerformAction('cypress/fixtures/upfrontpayorder.json');
    adminMenu.visitEnquiriesInfoPage();
    adminMenu.assertCertOnEnquiriesInfoPage('cypress/fixtures/naturaldiamondinforequest.json');
  });
});
