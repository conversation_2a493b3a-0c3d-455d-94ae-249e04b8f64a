import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('MatchingPairsNotVisibleOnGemstonesDetailsPage', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Matching Pairs Are Not Visible On Gemstone Details Page (NE-TC-666)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getgemsshortlistcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.matchingPairNotVisibleForGemstoneOnDetailsPage('gemsshortlistcert.json');
  });
});
