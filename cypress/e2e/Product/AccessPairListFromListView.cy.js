import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AccessPairsListFromListView', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify User Can Access Pairs List From List View (NE-TC-658)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabdiamondcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitLabgrownDiamonds();
    product.accessPairListFromListView('labdiamondcert.json');
  });
});
