import Login from '../../support/Login';
import Product from '../../support/Product';

describe('ProductOnHoldAllow', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Supplier Allow to Hold the Natural Diamaond', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    product.holdButtonVerify('Place ‘Hold’ Request', 'acceptholdstrue.json');
  });
});
