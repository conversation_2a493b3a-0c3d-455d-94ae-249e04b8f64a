import Login from '../../support/Login';
import Product from '../../support/Product';

describe('HoldButtonNotVisibleOnStoneListViewForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify hold button is not visible for unverified users on stone list view page (NE-TC-939)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('nonverifiedusers.json');
    product.holdButtonNotVisibleOnStoneListViewPage();
  });
});
