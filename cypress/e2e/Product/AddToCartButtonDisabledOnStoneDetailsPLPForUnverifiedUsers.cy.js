import Login from '../../support/Login';
import Product from '../../support/Product';

describe(
  'AddToCartButtonDisabledOnStoneDetailsPagePLPForUnverifiedUsers',
  { tags: ['@Product-PLP', '@Regression'] },
  () => {
    it('Verify Add to cart is disabled on stone details PLP page for unverified users (NE-TC-943)', () => {
      const login = new Login();
      const product = new Product();

      login.loginUsingApi('nonverifiedusers.json');
      product.accessDiamondProductDetails();
      product.addProductToCartDisabled();
    });
  }
);
