import Login from '../../support/Login';
import Product from '../../support/Product';

describe(
  'DiamondPriceNotVisibleOnStoneListViewPageForUnverifiedUsers',
  { tags: ['@Product-PLP', '@Regression'] },
  () => {
    it('Verify Diamond Price  is not visible for unverified users On Stone List View Page (NE-TC-947)', () => {
      const login = new Login();
      const product = new Product();

      login.loginUsingApi('nonverifiedusers.json');
      product.priceNotVisibleOnStoneListViewPage();
    });
  }
);
