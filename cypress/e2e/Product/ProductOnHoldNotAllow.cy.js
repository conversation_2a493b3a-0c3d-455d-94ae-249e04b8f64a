import Login from '../../support/Login';
import Product from '../../support/Product';

describe('ProductOnHold', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Supplier Dont Allow to Hold the Natural Diamaond', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    product.holdButtonVerify("Supplier doesn't accept holds", 'acceptholdsfalse.json');
  });
});
