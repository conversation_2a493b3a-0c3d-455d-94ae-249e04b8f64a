import Login from '../../support/Login';
import Product from '../../support/Product';

describe('DiamondPriceNotVisibleOnStoneDetailsForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Diamond Price  is not visible for unverified users On Stone Details Page (NE-TC-946)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('nonverifiedusers.json');
    product.priceNotVisibleOnStoneDetails();
  });
});
