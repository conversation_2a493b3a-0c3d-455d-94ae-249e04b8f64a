import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';

describe('VerifyStockIdNotVisibleForUnverifiedUsersOnPDP', { tags: ['@Product-PLP', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('nonverifiedusers.json');
  });

  it('Verify Stock Id Is Not Visible For Natural Diamonds in PDP View For Unverified Users (NE-TC-2106)', () => {
    product.stockIdNotVisibleOnStoneDetails();
  });

  it('Verify Stock Id Is Not Visible For Lab Grown Diamonds in PDP View Unverified Users (NE-TC-2109)', () => {
    navBar.visitLabgrownDiamonds();
    product.stockIdNotVisibleOnStoneDetails();
  });

  it('Verify Stock Id Is Not Visible For Gemstones in PDP View Unverified Users (NE-TC-2103)', () => {
    navBar.visitGemStones();
    product.stockIdNotVisibleOnStoneDetails();
  });
});
