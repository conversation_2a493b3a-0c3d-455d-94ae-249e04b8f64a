import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe(
  'GemstonePriceNotVisibleOnStoneListViewPageForUnverifiedUsers',
  { tags: ['@Product-PLP', '@Regression'] },
  () => {
    it('Verify Gemstone Price  is not visible for unverified users on stone List View Page (NE-TC-950)', () => {
      const login = new Login();
      const product = new Product();
      const navbar = new Navbar();

      login.loginUsingApi('nonverifiedusers.json');
      navbar.visitGemStones();
      product.priceNotVisibleOnStoneListViewPage();
    });
  }
);
