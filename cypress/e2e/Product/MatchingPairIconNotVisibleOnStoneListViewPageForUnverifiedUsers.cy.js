import Login from '../../support/Login';
import Product from '../../support/Product';

describe(
  'MatchingPairIconNotVisibleOnStoneListViewPageForUnverifiedUsers',
  { tags: ['@Product-PLP', '@Regression'] },
  () => {
    it('Verify Matching Pair Icon is not visible for unverified users on stone list view page (NE-TC-941)', () => {
      const login = new Login();
      const product = new Product();

      login.loginUsingApi('nonverifiedusers.json');
      product.matchingPairIconNotVisibleOnStoneListViewPage();
    });
  }
);
