import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddToCartFromMatchingPairDetailsPage', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify User Can Add Stone To Cart From Matching Pair Details Page (NE-TC-661)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getdiamondpairdata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalDiamonds();
    product.addToCartFromPairDetails('diamondpairdata.json');
  });
});
