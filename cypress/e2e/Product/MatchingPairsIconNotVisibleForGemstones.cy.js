import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('MatchingPairIconNotVisibleForGemstones', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Matching Pairs Icon Is Not Visible For Gemstones  (NE-TC-667)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getgemsshortlistcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.matchingPairIconNotVisibleForGemstone('gemsshortlistcert.json');
  });
});
