import Login from '../../support/Login';
import Product from '../../support/Product';

describe('HoldButtonNotVisibleOnStoneDetailsForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify hold button is not visible for unverified users on stone details page (NE-TC-938)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('nonverifiedusers.json');
    product.holdButtonNotVisibleOnStoneDetails();
  });
});
