import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddToCartButtonDisabledOnStoneCardPLPForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Add to cart is disabled on stone card PLP page (NE-TC-942)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    login.loginUsingApi('nonverifiedusers.json');
    navbar.visitGemStones();
    product.addProductToCartDisabled();
  });
});
