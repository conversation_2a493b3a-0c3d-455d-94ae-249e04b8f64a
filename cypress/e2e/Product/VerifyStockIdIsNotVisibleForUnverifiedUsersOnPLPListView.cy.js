import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';

describe('VerifyStockIdIsNotVisibleForUnverifiedUsersOnPLPListView', { tags: ['@Product-PLP', '@Regression'] }, () => {
  const login = new Login();
  const product = new Product();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('nonverifiedusers.json');
  });

  it('Verify Stock Id Is Not Visible For Natural Diamonds in PLP List View For Unverified Users (NE-TC-2108)', () => {
    product.stockIdNotVisibleOnStoneListViewPage();
  });

  it('Verify Stock Id Is Not Visible For Lab Grown Diamonds in PLP List View Unverified Users (NE-TC-2111)', () => {
    navBar.visitLabgrownDiamonds();
    product.stockIdNotVisibleOnStoneListViewPage();
  });

  it('Verify Stock Id Is Not Visible For Gemstones in PLP List View Unverified Users (NE-TC-2105)', () => {
    navBar.visitGemStones();
    product.stockIdNotVisibleOnStoneListViewPage();
  });
});
