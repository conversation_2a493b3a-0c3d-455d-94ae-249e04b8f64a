import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('VerifyNonKYCCustomerCheckout', { tags: ['@Product-PLP', '@Regression', '@CX'] }, () => {
  it('Verify that Non Kyc Customer Cannot Checkout (NE-TC-703)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();

    cy.getnaturaldiamondcert();

    login.loginUsingApi('nonkycuser.json');
    product.addProductToCart('naturaldiamondcert.json');
    product.viewCart();
    checkout.checkIfCheckoutIsDisabled();
  });
});
