import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('PriceNotVisibleToNonVerifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Non Verified User Cannot See Price Of The Stones (NE-TC-668)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    login.loginUsingApi('nonverifiedusers.json');
    navbar.visitLabgrownDiamonds();
    product.priceNotVisibleForUnVerifiedUsers();
  });
});
