import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddToCartMultipleStonesFromMatchingPairsPage', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify User Can Add Multiple Stones To Cart From Pairs Page (NE-TC-663)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getmultiplestonepair();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.addMultipleStonesToCartFromPairIcon('multiplestonepair.json');
  });
});
