import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AccessPairsListFromLabDiamondsPLP', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify User Can Access Pairs List For Natural Diamonds (NE-TC-657)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabdiamondcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitLabgrownDiamonds();
    product.accessPairListFromIcon('labdiamondcert.json');
  });
});
