import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddToCartFromPairPage', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify User Can Add Stone To Cart From Pairs Icon Page (NE-TC-662)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getlabdiamondpairdata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.addToCartFromPairIcon('labdiamondpairdata.json');
  });
});
