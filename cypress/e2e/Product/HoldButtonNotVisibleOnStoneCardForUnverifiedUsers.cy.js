import Login from '../../support/Login';
import Product from '../../support/Product';

describe('HoldButtonNotVisibleOnStoneCardForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify hold button is not visible for unverified users on stone card (NE-TC-937)', () => {
    const login = new Login();
    const product = new Product();

    login.loginUsingApi('nonverifiedusers.json');
    product.holdButtonNotVisibleOnStoneCard();
  });
});
