import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddNaturalDiamondToCart', { tags: ['@Product-PLP', '@Regression', '@CX'] }, () => {
  it('Verify Natural Diamond Gets Added To Cart (NE-TC-2417)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getnaturaldiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalDiamonds();
    product.addProductToCart('naturaldiamondcert.json');
  });
});
