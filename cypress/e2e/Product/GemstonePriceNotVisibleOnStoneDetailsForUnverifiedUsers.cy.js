import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('GemstonePriceNotVisibleOnStoneDetailsForUnverifiedUsers', { tags: ['@Product-PLP', '@Regression'] }, () => {
  it('Verify Gemstone Price  is not visible for unverified users on stone Details Page (NE-TC-949)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    login.loginUsingApi('nonverifiedusers.json');
    navbar.visitGemStones();
    product.priceNotVisibleOnStoneDetails();
  });
});
