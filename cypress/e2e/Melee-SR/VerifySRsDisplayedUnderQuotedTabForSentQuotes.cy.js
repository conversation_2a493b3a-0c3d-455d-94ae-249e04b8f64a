import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifySRsDisplayedUnderQuotedTabForSentQuotes', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify SRs marked as Quoted and are displayed under Quoted tab for Sent Quotes (NE-TC-2293)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    cy.getQuotedMeleeRequest();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('quotedsMeleeSrdata.json');
    meleeSR.verifyActiveTab('Quoted');
  });
});
