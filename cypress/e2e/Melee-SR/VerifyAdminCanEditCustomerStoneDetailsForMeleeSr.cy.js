import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyAdminCanEditCustomerStoneDetailsForMeleeSr', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify stone details added by the customer can be edited by Admin (NE-TC-2137)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    meleeSR.editMeleeDeatils();
  });
});
