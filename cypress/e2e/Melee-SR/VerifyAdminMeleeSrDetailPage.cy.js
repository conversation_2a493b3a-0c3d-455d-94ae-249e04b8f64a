import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyAdminMeleeSrDetailPage', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Clicking on SR Number or View Details opens Melee SR detail page (NE-TC-2133)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughSrNumber();
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
  });
});
