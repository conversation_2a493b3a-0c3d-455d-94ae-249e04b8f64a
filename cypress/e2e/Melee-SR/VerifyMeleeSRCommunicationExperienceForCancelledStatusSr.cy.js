import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForCancelledStatusSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer cannot Communicate with Admin related to SR after SR is Cancelled (NE-TC-2473)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('meleeSrCancelledCustomer.json');
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('cancelledMeleeSrData.json');
    requests.meleeSrDetailPageAssertion();
    requests.noSrCommentModal();
  });
});
