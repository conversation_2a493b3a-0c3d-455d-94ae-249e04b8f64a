import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForAcceptedStatusSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer Can Communicate with Admin related to SR with Accepted Quotation (NE-TC-2477)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('meleeSrAcceptedCustomer.json');
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('acceptedMeleeSrData.json');
    requests.meleeSrDetailPageAssertion();
    requests.srComment();
  });
});
