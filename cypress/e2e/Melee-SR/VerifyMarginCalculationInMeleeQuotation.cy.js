import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyMarginCalculationInMeleeQuotation', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Margin is calculated for based on price added in Melee for quotation modal (NE-TC-2138)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    cy.getMeleeSrData();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    meleeSR.addQuotationData('12', '12', '1', '200', '250');
    meleeSR.positiveMarginAssertion();
    meleeSR.updateQuotationData('12', '12', '1', '200', '25');
    meleeSR.negativeMarginAssertion();
  });
});
