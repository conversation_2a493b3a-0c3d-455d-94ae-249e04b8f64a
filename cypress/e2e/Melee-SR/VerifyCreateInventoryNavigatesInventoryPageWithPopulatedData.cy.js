import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';
import CustomerMeleeSR from '../../support/Melee-SR';

describe(
  'VerifyCreateInventoryNavigatesInventoryPageWithPrepopulatedValues',
  { tags: ['@Melee-SR', '@Regression'] },
  () => {
    it('Verify Clicking Create Inventory Navigates to Inventory page with Prepopulated Values (NE-TC-2377)', () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();
      const meleeSR = new MeleeSR();
      const customerMeleeSR = new CustomerMeleeSR();

      login.loginUsingAdminApi('loginasadmin.json');

      accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
      meleeSR.createRequestVisibilityAssertion();
      meleeSR.createSrRequest('upfrontaipuser.json', 'Round', 'VVS1', 'Excellent');
      customerMeleeSR.fillStep2FormForMeleeSR();
      customerMeleeSR.meleeSRFormStep3Assertions('Round', 'VVS1', 'Excellent');

      cy.getMeleeSrData();

      meleeSR.searchSR('meleeSrData.json');
      meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
      meleeSR.addQuotationData('12', '12', '55', '200', '250');
      meleeSR.addAndSaveModal();
      meleeSR.acceptQuote();
      meleeSR.assertCreateInventoryPopulatedFields(
        'Natural',
        'Amsterdam Office NL',
        'D-F',
        'Round',
        'Special request',
        'Excellent',
        'VVS1',
        '12',
        '5',
        '55',
        '200',
        '250'
      );
    });
  }
);
