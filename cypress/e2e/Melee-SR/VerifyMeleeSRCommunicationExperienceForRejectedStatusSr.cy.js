import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForRejectedStatusSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer Can Communicate with Admin related to SR with Rejected Quotation (NE-TC-2476)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('meleeSrRejectedCustomer.json');
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('rejectedMeleeSrData.json');
    requests.meleeSrDetailPageAssertion();
    requests.srComment();
  });
});
