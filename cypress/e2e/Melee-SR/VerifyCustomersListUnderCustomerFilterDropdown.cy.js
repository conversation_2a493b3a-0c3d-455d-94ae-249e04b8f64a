import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyCustomersListUnderCustomerFilterDropdown', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customers listed on SR listing page are displayed under Customer Filter dropdown (NE-TC-2136)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.verifyFilterOptions();
    meleeSR.verifyCustomerListUnderFiltrs();
  });
});
