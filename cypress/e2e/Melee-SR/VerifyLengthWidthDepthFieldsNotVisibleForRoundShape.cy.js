import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyLengthWidthDepthFieldsNotVisibleForRoundShape', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Length Width Depth fields are displayed in Step#2 when Round shape is selected in Step#1 (NE-TC-2085)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Round', 'VVS', 'Excellent');
    meleeSR.lwdFieldNotVisibleAssertion();
  });
});
