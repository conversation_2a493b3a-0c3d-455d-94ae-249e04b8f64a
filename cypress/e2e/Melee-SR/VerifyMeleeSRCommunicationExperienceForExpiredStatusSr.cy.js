import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForExpiredStatusSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer cannot Communicate with Admin related to SR after SR is Expired (NE-TC-2474)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('meleeSrExpiredCustomer.json');
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('expiredMeleeSrData.json');
    requests.meleeSrDetailPageAssertion();
    requests.noSrCommentModal();
  });
});
