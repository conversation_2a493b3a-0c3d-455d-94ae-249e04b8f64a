import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Melee<PERSON> from '../../support/Admin/Melee-SR';

describe(
  'Melee SR Change Status From Admin And Reject Quote From Customer',
  { tags: ['@Melee-SR', '@Regression'] },
  () => {
    it('Verify Quote status changes to Pending Confirmation after Quotation is sent to customer (NE-TC-2151)', () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();
      const meleeSR = new MeleeSR();

      cy.getMeleeSrData();
      login.loginUsingAdminApi('loginasadmin.json');

      accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
      meleeSR.searchSR('meleeSrData.json');
      meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
      meleeSR.addQuotationData('12', '12', '1', '200', '250');
      meleeSR.changeStatusAndSendQuote();
    });

    it('Verify Customer Can Reject Quotations shared by Admin (NE-TC-2379)', () => {
      const login = new Login();
      const menu = new Menu();
      const requests = new Requests();

      cy.getmeleeQuoteSrData();
      login.loginUsingApi('meleeQuoteSrData.json');

      menu.visitRequestPage();
      requests.meleeSectionAssertion();
      requests.searchSR('meleeQuoteSrData.json');
      requests.meleeSrDetailPageAssertion();
      requests.rejectSrQuotation();
    });
  }
);
