import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyIncompleteLWDfieldsAllowCustomerToProceeedToNextForm', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Incomplete Length, Depth or Width fields allow Customer to Proceeed to Next form (NE-TC-2088)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Oval', 'VVS', 'Excellent');
    meleeSR.meleeSRFormStep2Assertions();
    meleeSR.fillStep2FormForMeleeSR();
  });
});
