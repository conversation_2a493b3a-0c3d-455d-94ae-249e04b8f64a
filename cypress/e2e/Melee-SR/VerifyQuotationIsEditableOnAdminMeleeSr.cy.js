import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyQuotationIsEditableOnAdminMeleeSr', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify quotation created by <PERSON><PERSON> can be edited (NE-TC-2145)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    cy.getMeleeSrData();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    meleeSR.addQuotationData('12', '12', '1', '200', '250');
    meleeSR.addAndSaveModal();
    meleeSR.editQuotationData('15', '15', '2', '400', '550');
  });
});
