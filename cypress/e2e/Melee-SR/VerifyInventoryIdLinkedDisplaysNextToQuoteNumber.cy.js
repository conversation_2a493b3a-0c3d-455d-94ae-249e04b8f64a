import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import AdminMeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyInventoryNumberLinkedIsDisplayedNextToQuote', { tags: ['@Regression', '@Melee-SR'] }, () => {
  it('Verify Inventory Number Linked is Dispalyed Next to Quote Number (NE-TC-2378)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const adminMeleeSR = new AdminMeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');

    cy.getMeleeSrData();

    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    adminMeleeSR.addQuotationData('5', '5', '1', '200', '250');
    adminMeleeSR.addAndSaveModal();
    adminMeleeSR.acceptQuote();
    adminMeleeSR.createInventory();
    adminMeleeSR.makeStockLive();
    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    adminMeleeSR.selectInventory();
    adminMeleeSR.verifySelectedInventory();
  });
});
