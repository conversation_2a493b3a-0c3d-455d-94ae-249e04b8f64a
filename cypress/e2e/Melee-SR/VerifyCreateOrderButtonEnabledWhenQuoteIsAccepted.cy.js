import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';
import CustomerMeleeSR from '../../support/Melee-SR';

describe('VerifyCreateOrderButtonEnabledWhenQuoteIsAccepted', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Create Order button is enabled only for Accepted quotation (NE-TC-2148)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();
    const customerMeleeSR = new CustomerMeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.createRequestVisibilityAssertion();
    meleeSR.createSrRequest('upfrontaipuser.json', 'Round', 'VVS', 'Excellent');
    customerMeleeSR.fillStep2FormForMeleeSR();
    customerMeleeSR.meleeSRFormStep3Assertions('Round', 'VVS', 'Excellent');

    cy.getMeleeSrData();

    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    meleeSR.addQuotationData('12', '12', '1', '200', '250');
    meleeSR.addAndSaveModal();
    meleeSR.createOrderButtonAssertion();
  });
});
