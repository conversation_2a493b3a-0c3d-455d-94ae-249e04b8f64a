import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyCustomerCanAdd10ParcelsForMeleeSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer can add maximum 10 parcels in step#2 of SR (NE-TC-2092) ', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Round', 'VVS', 'Excellent');
    meleeSR.meleeSRFormStep2Assertions();
    meleeSR.addParcels();
  });
});
