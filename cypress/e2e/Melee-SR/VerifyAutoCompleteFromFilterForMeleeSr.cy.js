import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyAutoCompleteFromFilterForMeleeSr', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify SR form is auto completed based on Filter section after clicking on Make a request link (NE-TC-2089)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.applyMeleeFilters('Emerald', 'E', 'I1', 'Excellent');
    filter.applyFilters();
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.verifyAutoCompleteFormInFilters('Emerald', 'E', 'I1', 'Excellent');
  });
});
