import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyNoFilterResultsAutoCompleteForMeleeSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify SR form is auto completed based on Filter selection for no filter results (NE-TC-2090)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.applyMeleeFilters('Heart', 'E', 'I1', 'Excellent');
    filter.applyFilters();
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.verifyAutoCompleteFormInFilters('Heart', 'E', 'I1', 'Excellent');
  });
});
