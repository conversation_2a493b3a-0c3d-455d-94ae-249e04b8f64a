import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyStep#2-OfMeleeSpecialRequestForm', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Step 2 For Melee Special Request Form For Melee (NE-TC-2082)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Round', 'VVS', 'Excellent');
    meleeSR.meleeSRFormStep2Assertions();
    meleeSR.fillStep2FormForMeleeSR();
  });
});
