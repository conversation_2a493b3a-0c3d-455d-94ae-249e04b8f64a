import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyMakeARequestButtonForFilterResults', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Make a request button is displayed for filter results (NE-TC-2087)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabGrownMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
  });
});
