import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyMeleeRequestCreationFromCheckoutPage', () => {
  it('Verify Melee Special Request can be created for <PERSON><PERSON> during checkout if lesser carats are available', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();
    const meleeSR = new MeleeSR();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();

    meleeProduct.verifyCaratsLowToHigh();

    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();

    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    meleeProduct.verifyRequestButtonCart('99999');

    meleeSR.verifySRCreationWhenQuantityExceeds();
  });
});
