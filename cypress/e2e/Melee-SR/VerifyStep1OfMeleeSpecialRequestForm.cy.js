import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyStep#1-OfMeleeSpecialRequestForm', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Step 1 For Melee Special Request Form For Melee (NE-TC-2081)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Round', 'VVS', 'Excellent');
  });
});
