import Login from '../../support/Login';
import AccessAdminTabs from './../../support/Admin/AccessAdminTabs';

describe('VerifyMeleeRequestsTabOnAdminMeleeDashboard', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Melee Requests Tab is displayed on Melee Dashboard (NE-TC-2129)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
  });
});
