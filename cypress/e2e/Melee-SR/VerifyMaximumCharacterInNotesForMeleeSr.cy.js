import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyMaximum300CharactersCanBeAddedInNotes', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Maximum 300 characters can be added in Notes section In step#2 of SR (NE-TC-2084)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.verifyShapeFilter('Square');
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.meleeSRFormStep1Assertions();
    meleeSR.fillStep1FormForMeleeSR('Round', 'VVS', 'Excellent');
    meleeSR.maxCharacterAssertion();
  });
});
