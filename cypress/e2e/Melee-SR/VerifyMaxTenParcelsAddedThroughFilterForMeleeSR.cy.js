import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Filter from '../../support/Filter';
import MeleeSR from '../../support/Melee-SR';

describe('VerifyMaxTenParcelsAddedThroughFilterForMeleeSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify maximum 10 parcels are added even though size selected in filters are more than 10 (NE-TC-2091)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();
    const meleeSR = new MeleeSR();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    filter.applyMeleeFilters('Round', 'I-J', 'I1', 'Excellent');
    filter.addMeleeStoneSizeFromFilters();
    filter.applyFilters();
    meleeSR.meleeRequestButtonAssertions();
    meleeSR.verifyMaxStonesInStepTwo();
  });
});
