import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('QuoteStatusPendingConfirmation', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Quote status changes to Pending Confirmation after a Quotation is sent to customer (NE-TC-2151)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    cy.getMeleeSrData();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('meleeSrData.json');
    meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    meleeSR.addQuotationData('12', '12', '1', '200', '250');
    meleeSR.changeStatusAndSendQuote();
  });
});
