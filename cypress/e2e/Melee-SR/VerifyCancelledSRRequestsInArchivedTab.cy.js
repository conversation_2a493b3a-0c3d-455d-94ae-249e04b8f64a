import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyCancelledSRRequestsInArchivedTab', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Cancelled SR request are displayed under Archived tab on Admin listing page (NE-TC-2289)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    cy.getCancelledMeleeRequest();

    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.searchSR('cancelledMeleeSrdata.json');
    meleeSR.verifyActiveTab('Archived');
  });
});
