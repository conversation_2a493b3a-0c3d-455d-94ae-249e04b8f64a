import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForOrderPlacedStatusSR', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Customer cannot Communicate with Admin related to SR after Order is Placed for Acceped Quotation (NE-TC-2475)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('meleeSrOrderPlacedCustomer.json');
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('orderPlacedMeleeSrData.json');
    requests.meleeSrDetailPageAssertion();
    requests.noSrCommentModal();
  });
});
