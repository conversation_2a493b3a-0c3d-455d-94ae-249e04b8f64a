import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe(
  'VerifyRequoteRequestedOrWithdrawnQuotationMovedToCollapsibleSlider',
  { tags: ['@Melee-SR', '@Regression'] },
  () => {
    it.skip('Verify Quotation marked as Requote requested or Withdrawn are moved to a collapsible slider  (NE-TC-2152)', () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();
      const meleeSR = new MeleeSR();

      cy.getMeleeSrData();

      login.visitPage();
      login.loginUsingUi('loginasadmin.json');

      accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
      meleeSR.searchSR('meleeSrData.json');
      meleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
      meleeSR.addQuotationData('12', '12', '1', '200', '250');
      meleeSR.addAndSaveModal();
      meleeSR.collapsibleSliderAssertion();
    });
  }
);
