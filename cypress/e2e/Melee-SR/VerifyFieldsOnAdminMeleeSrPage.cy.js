import Login from '../../support/Login';
import AccessAdminTabs from './../../support/Admin/AccessAdminTabs';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyFieldsOnAdminMeleeSrPage', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Fields on Melee SR listing page (NE-TC-2130)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeSR = new MeleeSR();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
    meleeSR.verifyMeleeSrFields();
  });
});
