import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyRequestsTabDisplaysDiamondAndMelee', { tags: ['@Melee-SR', '@Regression'] }, () => {
  it('Verify Diamond and <PERSON>ee are displayed on Clicking on Requests Tab (NE-TC-2285)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('morcustomer.json');
    menu.visitRequestPage();
    requests.diamondAndMeleeSectionAssertion();
  });
});
