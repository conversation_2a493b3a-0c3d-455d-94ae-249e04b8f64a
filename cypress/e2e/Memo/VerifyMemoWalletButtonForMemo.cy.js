import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoWalletButtonForMemo', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that if the customer is in memo mode then on clicking the memo wallet button the tab selected in the cart modal should be memo wallet. (NE-TC-829)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.validateMemoWalletButtonForMemoOption();
  });
});
