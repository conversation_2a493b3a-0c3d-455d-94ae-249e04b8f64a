import Login from '../../support/Login';
import Memo from '../../support/Memo';
import Company from '../../support/Admin/Company';

describe('VerifyMemoReturnWindow', { tags: ['@Memo', '@Regression'] }, () => {
  const login = new Login();
  const memo = new Memo();
  const company = new Company();

  before(() => {
    cy.getmemouser();
  });

  it('Logs in as an admin, updates memo duration, and verifies the setting (NE-TC-2358)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    company.visitCompanyPage('LoginasMemoEnabledUser.json');
    company.accessCompanySetting();
    memo.updateMemoDuration();
  });

  it('Logs back in as a customer and verifies the updated memo return window (NE-TC-2359)', () => {
    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();
    memo.verifyHowItWorkText('memoDurations.json');
    memo.verifyMemoReturnWindow('memoDurations.json');
  });
});
