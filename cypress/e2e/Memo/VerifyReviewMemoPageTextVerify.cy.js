import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyReviewMemoPageTextVerify', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that from memo wallet the customer is navigate to the Review memo wallet page (memo mode checkout flow. (NE-TC-842)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyReviewMemoCartText();
  });
});
