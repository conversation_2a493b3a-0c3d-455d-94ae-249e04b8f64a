import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCartItemsExist', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that on clicking the cart icon the cart modal should slide out from the right side of the screen. (NE-TC-827)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.visitMemoTab();
    memo.verifyMemoDrawerAnimation();
    memo.verifyCartItems();
  });
});
