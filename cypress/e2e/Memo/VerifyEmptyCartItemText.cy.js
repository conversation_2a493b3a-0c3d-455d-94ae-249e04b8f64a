import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyEmptyCartItemText', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the empty state for the cart. (NE-TC-831)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.validateCartButtonForBuyOption();
    memo.validateEmptyCartText();
  });
});
