import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyGoToInvoicesButton', { tags: ['@Memo', '@Regression'] }, () => {
  it.skip('Verify that the Go to my invoices button in the Need more credit section works. (NE-TC-2356)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
    memo.memoDeliveryOptionDisabled();
    memo.VerifyGoToInvoiceButton();
  });
});
