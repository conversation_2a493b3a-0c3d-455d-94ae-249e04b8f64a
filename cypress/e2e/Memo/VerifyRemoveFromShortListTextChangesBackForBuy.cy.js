import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyRemoveFromShortListTextChangesBackForBuy', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that in the cart, clicking on the Remove from shortlist the item is removed from the shortlist  and the text of the button changes to "Add to shortlist" (NE-TC-840)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('memoEnabledWithOutAIP.json');
    cy.emptyShortListMemoWalletItems();

    login.loginAsCustomerAssertion();

    memo.verifyAllinOnePricingDisabledCartTextVerify('0', 'Cart', '0');
    memo.addToCartShortListButton();
    memo.verifyAddToWishListCount('Remove from Shortlist', 'Add to shortlist');
    memo.verifyRemoveFromShortListTextToAddToShortListForBuy();
  });
});
