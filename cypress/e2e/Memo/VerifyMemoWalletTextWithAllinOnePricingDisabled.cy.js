import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoWalletTextWithAllinOnePricingDisabled', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that for an non-AIP customer the Item price and Delivered price in the Memo Wallet will be displayed separately. (NE-TC-2577)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('memoEnabledWithOutAIP.json');
    login.loginAsCustomerAssertion();
    memo.visitMemoTab();
    memo.verifyAllinOnePricingDisabledMemoTextVerify('1', 'Memo wallet');
    memo.sumOfDeliveredPrice();
  });
});
