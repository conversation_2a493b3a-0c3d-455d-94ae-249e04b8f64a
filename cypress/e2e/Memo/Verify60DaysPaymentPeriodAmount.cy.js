import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('Verify60DaysPaymentPeriodAmount', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Amount is Calculated Correctly For 60 Days Payment Period.(NE-TC-2573)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserNonCompanyUser();

    login.loginUsingApi('LoginasMemoEnabledUserNonCompanyUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
    memo.verifyPaymentPeriod('LoginasMemoEnabledUserNonCompanyUser.json', '60', '1');
    memo.verify60daysAmount('LoginasMemoEnabledUserNonCompanyUser.json');
  });
});
