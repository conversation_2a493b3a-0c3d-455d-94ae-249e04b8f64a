import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyColorOfLimitBar', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify The color of the credit bar is red.(NE-TC-877)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
    memo.memoDeliveryOptionDisabled();
    memo.verifymemoLimitBarColor();
  });
});
