import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyRemoveFromCartButton', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the Remove button in the item cards work. (NE-TC-2361)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyRemoveButton();
  });
});
