import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoWalletLimit', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Memo Wallet Checkout Limit Count. (NE-TC-2576)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.visitMemoTab();
    memo.verifyMemoBadge();
    memo.verifyMemoWalletLimit('LoginasMemoEnabledUser.json');
  });
});
