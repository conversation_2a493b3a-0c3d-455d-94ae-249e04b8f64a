import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyAddToShortListAndRemoveButtonExist', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the Add to shortlist and Remove buttons are present in the cart and memo wallet for every item added. (NE-TC-2350)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.visitMemoTab();
    memo.verifyMemoDrawerAnimation();
    memo.verifyAddToWishListAndRemoveButtonExist();
  });
});
