import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoWalletTextWithAllinOnePricingDisabled', { tags: ['@Memo', '@Regression'] }, () => {
  it(
    '"Validate that in case of memo wallet view Total after invoicing and Total today are present. (NE-TC-2363)',
    { tags: '@LastFailed' },
    () => {
      const login = new Login();
      const memo = new Memo();

      login.loginUsingApi('memoEnabledWithOutAIP.json');
      login.loginAsCustomerAssertion();
      memo.visitMemoTab();
      memo.verifyAllinOnePricingDisabledMemoTextVerify('1', 'Memo wallet');
      memo.sumOfDeliveredPrice();
      memo.verifyTotalToday();
    }
  );
});
