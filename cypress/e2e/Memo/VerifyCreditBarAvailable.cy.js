import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCreditBarAvailable', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the credit bar section is present in Payment Terms. (NE-TC-2353)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
    memo.verifyCreditBarOnPaymentTerms();
  });
});
