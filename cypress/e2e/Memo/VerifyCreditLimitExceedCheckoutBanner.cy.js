import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCreditLimitExceedCheckoutBanner', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the error message is present if the order value is greater than available credit. (NE-TC-2575)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
  });
});
