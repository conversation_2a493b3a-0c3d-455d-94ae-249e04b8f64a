import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyLimitAmountsAfterInvoicing', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Limit Amounts On Payment Terms After Invoicing". (NE-TC-2578)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
    memo.verifyLimitsOnPaymentTermsAfterInvoicing();
  });
});
