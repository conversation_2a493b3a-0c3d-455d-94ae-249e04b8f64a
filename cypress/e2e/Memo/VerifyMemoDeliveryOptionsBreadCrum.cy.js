import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoDeliveryOptionsBreadCrum', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the tab label is "Delivery options". (NE-TC-801)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
  });
});
