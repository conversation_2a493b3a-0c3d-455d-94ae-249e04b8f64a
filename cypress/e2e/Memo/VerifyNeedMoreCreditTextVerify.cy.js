import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyNeedMoreCreditTextVerify', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the Need more credit? section is present. (NE-TC-791)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();
    memo.verifyMemoBreadcrum();

    memo.verifyNeedMoreCredit();
  });
});
