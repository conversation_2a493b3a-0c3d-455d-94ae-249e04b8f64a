import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyChangeDeliveryAddressExist', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that if the customer add a new address in their shipping details that address is present in the delivery options. (NE-TC-804)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.addDeliveryAddress();
  });
});
