import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyTextOfExceedLimit', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the Credit limit bar shows these three values. (NE-TC-2362)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
    memo.memoDeliveryOptionDisabled();
    memo.verifyLimitExceedText();
  });
});
