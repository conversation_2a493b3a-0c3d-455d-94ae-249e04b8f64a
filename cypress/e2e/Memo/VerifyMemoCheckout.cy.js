import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoCheckoutExperienceForCx', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify a memo enabled customer is able to checkout a memo stone (NE-TC-2579)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserCompanyOwner().then(() => {
      login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
      login.loginAsCustomerAssertion();

      memo.visitMemoTab();
      memo.verifyMemoDrawerAnimation();
      memo.verifyMemoCheckout();
    });
  });
});
