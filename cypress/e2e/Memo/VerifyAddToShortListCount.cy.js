import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyAddToShortListCount', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Add to Memo Short List Count Updates. (NE-TC-2351)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    cy.emptyMemoWalletItems();
    cy.emptyShortListMemoWalletItems();
    login.loginAsCustomerAssertion();

    memo.visitMemoTab();
    memo.verifyMemoDrawerAnimation();
    memo.verifyAddToWishListAndRemoveButtonExist();
    memo.verifyAddToWishListCount('Remove from Memo Shortlist', 'Add to Memo Shortlist');
  });
});
