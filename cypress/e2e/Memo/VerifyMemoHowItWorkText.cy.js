import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoHowItWorkText', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the How it works section is present. (NE-TC-787)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserCompanyOwner();
    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.verifyHowItWorkText('LoginasMemoEnabledUserCompanyOwner.json');
  });
});
