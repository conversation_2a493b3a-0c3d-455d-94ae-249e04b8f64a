import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyAddAddressButtonAvailableForCompanyOwner', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that Add new address button is only visible to such customers who are company owners. (NE-TC-803)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyAddressButtonForCompanyUser();
  });
});
