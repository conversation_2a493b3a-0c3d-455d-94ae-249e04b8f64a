import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoSummary', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that To Pay Now & Pay After Invoicing is available (NE-TC-2360)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getmemouser();
    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyHowItWorkText('LoginasMemoEnabledUser.json');
    memo.verifyMemoSummary();
  });
});
