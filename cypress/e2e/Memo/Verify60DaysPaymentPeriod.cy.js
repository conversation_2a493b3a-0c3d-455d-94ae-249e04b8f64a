import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('Verify60DaysPaymentPeriod', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the steps in case of 60 days credit. (NE-TC-813)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserNonCompanyUser();
    login.loginUsingApi('LoginasMemoEnabledUserNonCompanyUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
    memo.verifyPaymentPeriod('LoginasMemoEnabledUserNonCompanyUser.json', '60', '1');
  });
});
