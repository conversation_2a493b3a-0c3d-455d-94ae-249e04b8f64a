import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyMemoBreadCrum', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the tab label is "Memo wallet and notes". (NE-TC-785)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
  });
});
