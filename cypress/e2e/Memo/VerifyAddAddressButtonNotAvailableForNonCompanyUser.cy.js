import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyAddAddressButtonAvailableForNonCompanyOwner.cy', { tags: ['@Memo', '@Regression'] }, () => {
  it('Add new address button is only Not visible to such customers who are Not company owners. (NE-TC-2572)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserNonCompanyUser();
    login.loginUsingApi('LoginasMemoEnabledUserNonCompanyUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyAddressButtonForNonCompanyUser();
  });
});
