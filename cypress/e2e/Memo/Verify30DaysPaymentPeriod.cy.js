import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('Verify30DaysPaymentPeriod', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the steps in case of 30 days credit. (NE-TC-812)', () => {
    const login = new Login();
    const memo = new Memo();

    cy.getLoginasMemoEnabledUserNonCompanyUser();
    login.loginUsingApi('LoginasMemoEnabledUserNonCompanyUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
    memo.verifyPaymentPeriod('LoginasMemoEnabledUserNonCompanyUser.json', 30, '0');
  });
});
