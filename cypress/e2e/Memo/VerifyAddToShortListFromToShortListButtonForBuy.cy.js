import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyAddToShortListFromToShortListButtonForBuy', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the items in the cart can be added to the shortlist  using the To shortlist button. (NE-TC-2352)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('memoEnabledWithOutAIP.json');
    login.loginAsCustomerAssertion();
    memo.verifyAllinOnePricingDisabledCartTextVerify('0', 'Cart', '0');
    memo.verifyAddToWishListCountForBuy('Remove from Shortlist', 'Add to shortlist');
  });
});
