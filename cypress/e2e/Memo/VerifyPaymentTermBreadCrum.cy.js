import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyPaymentTermBreadCrum', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that the tab label is "Payment terms after invoicing". (NE-TC-808)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.verifyDeliveryOptionsBreadcrum();
    memo.verifyPaymentTermsAfterInvoicing();
  });
});
