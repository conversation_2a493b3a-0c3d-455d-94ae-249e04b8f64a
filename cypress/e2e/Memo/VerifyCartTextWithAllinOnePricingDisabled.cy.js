import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCartTextWithAllinOnePricingDisabled', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that for an non-AIP customer the Item price and Delivered price in the cart will be displayed separately. (NE-TC-833)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('memoEnabledWithOutAIP.json');
    login.loginAsCustomerAssertion();
    memo.verifyAllinOnePricingDisabledCartTextVerify('0', 'Cart', '0');
  });
});
