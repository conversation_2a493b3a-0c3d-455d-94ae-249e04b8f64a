import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCartButtonForBuy', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that if the customer is in buy mode then on clicking the cart icon the tab selected in the cart modal should be cart. (NE-TC-828) ', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.validateCartButtonForBuyOption('Cart');
  });
});
