import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyDrawerAnimation', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the side drawer animation. (NE-TC-826)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.visitMemoTab('LoginasMemoEnabledUserCompanyOwner.json');
    memo.verifyMemoDrawerAnimation();
  });
});
