import Login from '../../support/Login';
import Memo from '../../support/Memo';
describe('VerifyAddToShortListCount', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that in the cart, clicking on the Remove from Memo shortlist the item is removed from the shortlist and the text of the button changes to "Add to shortlist" (NE-TC-839)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    cy.emptyShortListMemoWalletItems();

    login.loginAsCustomerAssertion();

    memo.visitMemoTab();
    memo.verifyMemoDrawerAnimation();
    memo.verifyAddToWishListAndRemoveButtonExist();
    memo.verifyAddToWishListCount('Remove from Memo Shortlist', 'Add to Memo Shortlist');
  });
});
