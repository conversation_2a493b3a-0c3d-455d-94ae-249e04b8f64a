import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyTotalInclusivePriceForBuy', { tags: ['@Memo', '@Regression'] }, () => {
  it('Validate that in case of cart view Total incl. taxes is present. (NE-TC-2364)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('memoEnabledWithOutAIP.json');
    login.loginAsCustomerAssertion();
    memo.verifyAllinOnePricingDisabledCartTextVerify('0', 'Cart', '0');
    memo.verifyAllinOnePricingDisabledCartTextVerify('0', 'Cart', '1');
    memo.sumOfDeliveredPrice();
  });
});
