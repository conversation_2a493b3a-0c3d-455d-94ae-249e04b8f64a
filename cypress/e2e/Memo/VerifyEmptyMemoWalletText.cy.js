import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyEmptyMemoWalletText', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify the empty state of the memo wallet. (NE-TC-832)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LoginasMemoEnabledUserCompanyOwner.json');
    login.loginAsCustomerAssertion();

    memo.validateMemoWalletButtonForMemoOption('0', 'Cart');
    memo.validateEmptyMemoWalletText();
  });
});
