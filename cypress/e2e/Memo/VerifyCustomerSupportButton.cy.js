import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyCustomerSupportButton.cy.js', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Customer Support Button Is Available. ( NE-TC-2354)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
    memo.memoDeliveryOptionDisabled();
    memo.verifyCustomerSupportButton();
  });
});
