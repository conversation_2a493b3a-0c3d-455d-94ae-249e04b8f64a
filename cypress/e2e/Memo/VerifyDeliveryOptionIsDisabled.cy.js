import Login from '../../support/Login';
import Memo from '../../support/Memo';

describe('VerifyDeliveryOptionIsDisabled', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify Delivery Option Is Disabled If Limit Exceed.(NE-TC-2355)', () => {
    const login = new Login();
    const memo = new Memo();

    login.loginUsingApi('LessCreditLimitMemoEnabledUser.json');
    login.loginAsCustomerAssertion();

    memo.verifyMemoBreadcrum();
    memo.memoCreditLimitExceedBanner();
    memo.memoDeliveryOptionDisabled();
  });
});
