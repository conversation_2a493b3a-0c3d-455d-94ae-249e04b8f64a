import Login from '../../support/Login';
import Memo from '../../support/Memo';
import Product from '../../support/Product';

describe('VerifyBuyCart&NotesBreadCrum', { tags: ['@Memo', '@Regression'] }, () => {
  it('Verify that from the cart the customer is navigated to the Cart & notes page (buy mode flow). (NE-TC-2574)', () => {
    const login = new Login();
    const memo = new Memo();
    const product = new Product();

    cy.getcertnumber();

    login.loginUsingApi('LoginasMemoEnabledUser.json');
    login.loginAsCustomerAssertion();
    product.addProductToCart('cert.json');
    memo.verifyCartAndNotesBuyBreadcrum();
  });
});
