import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessDoneInHongKong', () => {
  it('Verify User Can Access Done In Hong Kong (NE-TC-1084)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('Hong Kong', 'HK', 'delivered', 'office-orders/HK/delivered');
  });
});
