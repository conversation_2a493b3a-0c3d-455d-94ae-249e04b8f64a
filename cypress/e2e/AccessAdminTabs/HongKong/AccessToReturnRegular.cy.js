import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInHongKong', () => {
  it('Verify User Can Access To Return In Hong Kong (NE-TC-1091)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('Hong Kong', 'HK', 'to-return', 'office-orders/HK/to-return');
  });
});
