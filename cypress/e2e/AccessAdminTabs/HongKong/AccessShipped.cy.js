import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInHongKong', () => {
  it('Verify User Can Access Shipped In Hong Kong (NE-TC-1087)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Hong Kong',
      'HK',
      'shipped-final-delivery',
      'office-orders/HK/shipped-final-delivery'
    );
  });
});
