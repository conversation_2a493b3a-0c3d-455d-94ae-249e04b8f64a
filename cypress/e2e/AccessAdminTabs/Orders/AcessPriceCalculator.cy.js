import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessPriceCalculator', () => {
  it('Verify User Can Access Price Calculator (NE-TC-1158)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', 'Price Calculator', '', 'orders/price-calculator');
  });
});
