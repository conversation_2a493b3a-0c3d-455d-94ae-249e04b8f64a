import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadyToQC', () => {
  it('Verify User Can Access Ready To QC (NE-TC-1153)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', '', 'QC', 'orders/ready-to-qc');
  });
});
