import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessPurchaseOrder', () => {
  it('Verify User Can Access Purchase Order (NE-TC-1152)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
  });
});
