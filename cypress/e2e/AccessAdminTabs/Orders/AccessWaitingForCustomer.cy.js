import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessWaitingForCustomer', () => {
  it('Verify User Can Access Waiting For Customer (NE-TC-1156)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', '', 'Waiting customer', 'orders/waiting-for-customer');
  });
});
