import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAddcertificate', () => {
  it('Verify User Can Access Add certificate (NE-TC-1144)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', 'Add certificate', '', 'orders/add-manual-certificate');
  });
});
