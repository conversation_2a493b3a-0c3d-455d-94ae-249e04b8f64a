import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadyToTag', () => {
  it('Verify User Can Access Ready To Tag (NE-TC-1154)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', '', 'Ready to tag', 'orders/ready-to-tag');
  });
});
