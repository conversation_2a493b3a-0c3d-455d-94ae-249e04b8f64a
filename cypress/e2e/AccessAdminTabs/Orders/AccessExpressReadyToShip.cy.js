import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessExpressReadyToShip', () => {
  it('Verify User Can Access Express Ready To Ship (NE-TC-1149)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', '', 'RTS', 'orders/ready-to-ship');
  });
});
