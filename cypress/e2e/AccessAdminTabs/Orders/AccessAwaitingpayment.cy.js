import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAwaitingpayment', () => {
  it('Verify User Can Access Awaiting payment (NE-TC-1146)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', '', 'Awaiting payment', 'orders/awaiting-payment');
  });
});
