import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAddStone', () => {
  it('Verify User Can Access Add Stone (NE-TC-1145)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', 'Add stone', '', 'orders/add-by-gia');
  });
});
