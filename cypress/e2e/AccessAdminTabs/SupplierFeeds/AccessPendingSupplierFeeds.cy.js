import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessPendingSupplierFeeds', () => {
  it('Verify User Can Access Pending Supplier Feeds (NE-TC-1175)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Supplier Feeds', '', 'Pending', 'feed/suppliers/pending');
  });
});
