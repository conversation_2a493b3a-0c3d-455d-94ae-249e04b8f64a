import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessRegisteredSupplierFeeds', () => {
  it('Verify User Can Access Registered Supplier Feeds (NE-TC-1176)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Supplier Feeds', '', 'Registered', 'feed/suppliers/signup');
  });
});
