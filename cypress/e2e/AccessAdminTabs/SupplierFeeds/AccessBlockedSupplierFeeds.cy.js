import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessBlockedSupplierFeeds', () => {
  it('Verify User Can Access Blocked Supplier Feeds (NE-TC-1170)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Supplier Feeds', '', 'Blocked', 'feed/suppliers/blocked');
  });
});
