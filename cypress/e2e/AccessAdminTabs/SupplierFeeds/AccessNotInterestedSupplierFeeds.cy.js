import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessNotInterestedSupplierFeeds', () => {
  it('Verify User Can Access Not Interested Supplier Feeds (NE-TC-1174)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Supplier Feeds', '', 'Not Interested', 'feed/suppliers/not-interested');
  });
});
