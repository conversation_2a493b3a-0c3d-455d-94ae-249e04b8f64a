import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessIncomingInLondon', () => {
  it('Verify User Can Access Incoming In London (NE-TC-1093)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('London', 'GB', 'office-incoming', 'office-orders/GB/office-incoming');
  });
});
