import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadytoship/DeliverInLondon', { tags: '@Production' }, () => {
  it('Verify User Can Access Ready to ship/ Deliver In London (NE-TC-1094)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'London',
      'GB',
      'ready-final-delivery',
      'office-orders/GB/ready-final-delivery'
    );
  });
});
