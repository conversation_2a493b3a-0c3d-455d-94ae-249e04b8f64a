import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInLondon', () => {
  it('Verify User Can Access Shipped In London (NE-TC-1095)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'London',
      'GB',
      'shipped-final-delivery',
      'office-orders/GB/shipped-final-delivery'
    );
  });
});
