import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadytoship/DeliverInAmsterdam', () => {
  it('Verify User Can Access Ready to ship/ Deliver In Amsterdam (NE-TC-1040) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Amsterdam',
      'NL',
      'ready-final-delivery',
      'office-orders/NL/ready-final-delivery'
    );
  });
});
