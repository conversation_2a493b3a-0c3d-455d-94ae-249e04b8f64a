import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInAmsterdam', () => {
  it('Verify User Can Access To Return In Amsterdam (NE-TC-1043) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('Amsterdam', 'NL', 'to-return', 'office-orders/NL/to-return');
  });
});
