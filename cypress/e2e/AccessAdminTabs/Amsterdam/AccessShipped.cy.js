import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInAmsterdam', () => {
  it('Verify User Can Access Shipped In Amsterdam (NE-TC-1041) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Amsterdam',
      'NL',
      'shipped-final-delivery',
      'office-orders/NL/shipped-final-delivery'
    );
  });
});
