import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShipmentCosts', () => {
  it('Verify User Can Access Shipment Costs in Reports (NE-TC-1159)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Reports', 'Shipment Costs', '', 'reports/shipment-costs');
  });
});
