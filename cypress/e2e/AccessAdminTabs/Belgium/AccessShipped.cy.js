import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInBelgium', () => {
  it('Verify User Can Access Shipped In Belgium (NE-TC-1057) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Belgium',
      'BE',
      'shipped-final-delivery',
      'office-orders/BE/shipped-final-delivery'
    );
  });
});
