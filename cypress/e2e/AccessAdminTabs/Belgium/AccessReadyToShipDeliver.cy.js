import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadytoship/DeliverInBelgium', () => {
  it('Verify User Can Access Ready to ship/ Deliver In Belgium (NE-TC-1056) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Belgium',
      'BE',
      'ready-final-delivery',
      'office-orders/BE/ready-final-delivery'
    );
  });
});
