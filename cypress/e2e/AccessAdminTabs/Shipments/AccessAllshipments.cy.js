import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAllshipments', () => {
  it('Verify User Can Access All shipments (NE-TC-1163)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'All shipments', '', 'shipment/view/all-shipments');
  });
});
