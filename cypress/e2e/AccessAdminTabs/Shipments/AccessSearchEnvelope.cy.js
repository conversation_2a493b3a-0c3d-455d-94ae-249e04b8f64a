import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessSearchEnvelope', () => {
  it('Verify User Can Access Search Envelope (NE-TC-1165)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Search Envelope', '', 'shipment/search-envelope');
  });
});
