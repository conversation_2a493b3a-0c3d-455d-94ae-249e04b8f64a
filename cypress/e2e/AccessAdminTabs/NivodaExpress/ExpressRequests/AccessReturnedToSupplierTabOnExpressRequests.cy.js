import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessReturnedToSupplierTabOnExpressRequests;', () => {
  it(
    'Verify User Can Access Returned To Supplier Tab On Express Requests (NE-TC-1134)',
    { tags: '@Production' },
    () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();

      login.loginUsingAdminApi('loginasadmin.json');
      accessAdminTabs.accessTabs(
        'Nivoda Express',
        'Express requests',
        'Returned to supplier',
        'admin/nivoda-express/express-requests/returned-to-supplier'
      );
    }
  );
});
