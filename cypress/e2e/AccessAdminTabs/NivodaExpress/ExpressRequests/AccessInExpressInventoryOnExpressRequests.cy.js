import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessInExpressInventoryTabOnExpressRequests', () => {
  it(
    'Verify User Can Access In Express Inventory Tab On Express Requests (NE-TC-1130)',
    { tags: '@Production' },
    () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();

      login.loginUsingAdminApi('loginasadmin.json');
      accessAdminTabs.accessTabs(
        'Nivoda Express',
        'Express requests',
        'In express inventory',
        'admin/nivoda-express/express-requests/in-express-inventory'
      );
    }
  );
});
