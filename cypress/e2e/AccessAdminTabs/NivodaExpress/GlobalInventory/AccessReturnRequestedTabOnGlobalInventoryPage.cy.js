import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessReturnRequestedTabOnGlobalInventoryPage;', () => {
  it(
    'Verify User Can Access Return Requested Tab On Global Inventory Page (NE-TC-1141)',
    { tags: '@Production' },
    () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();

      login.loginUsingAdminApi('loginasadmin.json');
      accessAdminTabs.accessTabs(
        'Nivoda Express',
        'Global Inventory',
        'Return requested',
        'admin/nivoda-express/global-inventory/return-requested'
      );
    }
  );
});
