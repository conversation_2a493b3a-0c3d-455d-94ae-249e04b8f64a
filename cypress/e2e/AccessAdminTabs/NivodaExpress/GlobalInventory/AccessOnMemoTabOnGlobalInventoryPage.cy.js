import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessOnMemoTabOnGlobalInventoryPage', () => {
  it('Verify User Can Access On Memo Tab On Global Inventory Page (NE-TC-1138)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Nivoda Express',
      'Global Inventory',
      'On memo',
      'admin/nivoda-express/global-inventory/on-memo'
    );
  });
});
