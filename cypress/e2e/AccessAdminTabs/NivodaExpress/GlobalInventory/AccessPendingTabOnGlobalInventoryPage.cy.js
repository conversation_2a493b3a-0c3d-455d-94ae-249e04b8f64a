import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessPendingTabOnGlobalInventoryPage;', () => {
  it('Verify User Can Access All Pending On Global Inventory Page (NE-TC-1139)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Nivoda Express',
      'Global Inventory',
      'Pending',
      'admin/nivoda-express/global-inventory/pending'
    );
  });
});
