import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessSourceStones', () => {
  it('Verify User Can Access Source Stones (NE-TC-1143)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
  });
});
