import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAirwallexTransactionHistory', () => {
  it('Verify User Can Access Airwallex Transaction History (NE-TC-1028) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Accounting',
      'Awaiting Payment',
      'Airwallex Transaction History',
      'accounting/airwallex-history'
    );
  });
});
