import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessSupplierstoPay', () => {
  it('Verify User Can Access Suppliers to Pay (NE-TC-1035) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Accounting',
      'Awaiting Payment',
      'Suppliers to Pay (INV)',
      'accounting/suppliers-to-pay-inv'
    );
  });
});
