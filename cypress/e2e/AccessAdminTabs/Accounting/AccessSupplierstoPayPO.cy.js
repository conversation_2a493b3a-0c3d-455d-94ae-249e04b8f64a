import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessSupplierstoPay(PO)', () => {
  it('Verify User Can Access Suppliers to Pay (PO) (NE-TC-1036) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Accounting',
      'Awaiting Payment',
      'Suppliers to Pay (PO)',
      'accounting/suppliers-to-pay-po'
    );
  });
});
