import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessCustomerReturns', () => {
  it('Verify User Can Access Customer Returns (NE-TC-1030) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'Credit Notes', 'accounting/customer-returns');
  });
});
