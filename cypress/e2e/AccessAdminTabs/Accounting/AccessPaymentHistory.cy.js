import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessPaymentHistory', () => {
  it('Verify User Can Access Payment History (NE-TC-1033) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'Payment History', 'accounting/payment-history');
  });
});
