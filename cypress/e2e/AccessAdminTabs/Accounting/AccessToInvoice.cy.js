import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToInvoice', () => {
  it('Verify User Can Access To Invoice (NE-TC-1037) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'To Invoice', 'accounting/to-invoice');
  });
});
