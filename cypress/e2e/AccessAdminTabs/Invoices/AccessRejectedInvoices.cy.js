import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';

describe('AccessRejectedInvoicesTab', { tags: ['Production'] }, () => {
  it('Verify User Can Access Rejected Invoices Tab (NE-TC-1129)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'Rejected', 'admin/invoices/rejected');
  });
});
