import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';

describe('AccessPaidInvoicesTab', { tags: ['Production'] }, () => {
  it('Verify User Can Access Paid Invoices Tab (NE-TC-1128)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'Paid', 'admin/invoices/paid');
  });
});
