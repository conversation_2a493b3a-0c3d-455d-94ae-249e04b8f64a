import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';

describe('AccessAllInvoices', { tags: ['Production'] }, () => {
  it('Verify User Can Access All Invoices (NE-TC-1124)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'All invoices', 'admin/invoices/all_invoices');
  });
});
