import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';
describe('AccessApprovedInvoiceTab', { tags: ['Production'] }, () => {
  it('Verify User Can Access Approved Invoice Tab (NE-TC-1125)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'Approved', 'admin/invoices/approved');
  });
});
