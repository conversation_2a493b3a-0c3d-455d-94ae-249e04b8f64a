import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';

describe('AccessAwaitingSupplier', { tags: ['Production'] }, () => {
  it('Verify User Can Access Awaiting Supplier (NE-TC-1127)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'Awaiting supplier', 'admin/invoices/awaiting_supplier');
  });
});
