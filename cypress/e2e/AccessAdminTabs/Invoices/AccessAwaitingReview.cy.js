import Login from './../../../support/Login';
import AccessAdminTabs from './../../../support/Admin/AccessAdminTabs';

describe('AccessAwaitingReviewTab', { tags: ['Production'] }, () => {
  it('Verify User Can Access Awaiting Review Tab (NE-TC-1126)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Invoices', '', 'Awaiting review', 'admin/invoices/awaiting_review');
  });
});
