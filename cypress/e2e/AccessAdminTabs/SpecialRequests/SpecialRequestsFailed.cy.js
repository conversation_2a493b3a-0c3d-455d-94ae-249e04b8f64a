import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';
import Admin from '../../../support/Admin';

describe('AccessDiamondRequestsinSpecialRequestsFailed', { tags: '@Production' }, () => {
  it('Verify User Can Access Diamond Requests in Special Requests Failed (NE-TC-1167)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const admin = new Admin();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Special Requests', '', '', '');
    admin.accessDiamondRequestsInSpecialRequests('Failed');
  });
});
