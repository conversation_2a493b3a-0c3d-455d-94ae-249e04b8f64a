import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';
import Admin from '../../../support/Admin';

describe('AccessDiamondRequestsinSpecialRequestsSentToCustomer', () => {
  it(
    'Verify User Can Access Diamond Requests in Special Requests Sent To Customer (NE-TC-1168)',
    { tags: '@Production' },
    () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();
      const admin = new Admin();

      login.loginUsingAdminApi('loginasadmin.json');
      accessAdminTabs.accessTabs('Special Requests', '', '', '');
      admin.accessDiamondRequestsInSpecialRequests('Sent To Customer');
    }
  );
});
