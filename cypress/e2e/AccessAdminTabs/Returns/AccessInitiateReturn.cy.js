import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessInitiateReturn', () => {
  it('Verify User Can Access Initiate Return (NE-TC-1162)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Returns', 'Initiate return', '', 'returns/initiate');
  });
});
