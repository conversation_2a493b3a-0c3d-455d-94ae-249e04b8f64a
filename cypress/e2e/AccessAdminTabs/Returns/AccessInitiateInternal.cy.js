import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessInitiateInternal', () => {
  it('Verify User Can Access Initiate Internal (NE-TC-1161)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Returns', 'Initiated internal', '', 'returns/initiated-internal');
  });
});
