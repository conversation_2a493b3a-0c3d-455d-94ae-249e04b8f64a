import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInAustralia', () => {
  it('Verify User Can Access Shipped In Australia (NE-TC-1049) ', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Australia',
      'AU',
      'shipped-final-delivery',
      'office-orders/AU/shipped-final-delivery'
    );
  });
});
