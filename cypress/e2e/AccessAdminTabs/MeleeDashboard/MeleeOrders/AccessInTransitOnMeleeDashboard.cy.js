import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessInTransitOnMeleeDashboard', () => {
  it('Verify User Can Access In Transit on Melee Dashboard (NE-TC-1196)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Orders', 'In transit order', 'admin/melee/orders/in-transit');
  });
});
