import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessInitiateReturnOnMeleeDashboard', () => {
  it('Verify User Can Access Initiate Return On Melee Dashboard (NE-TC-1205)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Returns', 'Initiate return', 'admin/returns/initiate');
  });
});
