import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessInsuranceAndCreditSettingsOnMeleeDashboard', () => {
  it(
    'Verify User Can Access Insurance & Credit Settings On Melee Dashboard (NE-TC-1184)',
    { tags: '@Production' },
    () => {
      const login = new Login();
      const accessAdminTabs = new AccessAdminTabs();

      login.loginUsingAdminApi('loginasadmin.json');
      accessAdminTabs.accessTabs(
        'Melee Dashboard',
        'Companies',
        'Insurance & Credit Settings',
        'admin/companies/insurance_credit_settings'
      );
    }
  );
});
