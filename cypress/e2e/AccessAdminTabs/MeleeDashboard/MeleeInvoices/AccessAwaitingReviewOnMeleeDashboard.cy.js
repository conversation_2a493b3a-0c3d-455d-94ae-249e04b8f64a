import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessAwaitingReviewTabOnMeleeDashboard', () => {
  it('Verify User Can Access Awaiting Review Tab On Melee Dashboard (NE-TC-1189)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Invoices', 'Awaiting review', 'admin/invoices/awaiting_review');
  });
});
