import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessAwaitingSupplierOnMeleeDashboard', () => {
  it('Verify User Can Access Awaiting Supplier Tab On Melee Dashboard (NE-TC-1190)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Invoices', 'Awaiting supplier', 'admin/invoices/awaiting_supplier');
  });
});
