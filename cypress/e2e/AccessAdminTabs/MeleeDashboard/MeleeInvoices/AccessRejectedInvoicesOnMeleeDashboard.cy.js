import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessRejectedInvoicesTabOnMeleeDashboard', () => {
  it('Verify User Can Access Rejected Invoices Tab On Melee Dashboard (NE-TC-1192)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Invoices', 'Rejected', 'admin/invoices/rejected');
  });
});
