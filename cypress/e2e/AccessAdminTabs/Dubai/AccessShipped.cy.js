import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInDubai', () => {
  it('Verify User Can Access Shipped In Dubai (NE-TC-1065)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Dubai',
      'AE',
      'shipped-final-delivery',
      'office-orders/AE/shipped-final-delivery'
    );
  });
});
