import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInDubai', () => {
  it('Verify User Can Access To Return In Dubai (NE-TC-1068)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('Dubai', 'AE', 'to-return', 'office-orders/AE/to-return');
  });
});
