import Login from './../../support/Login';
import AccessAdminTabs from './../../support/Admin/AccessAdminTabs';
describe('Access UploadStock page', () => {
  it('Verify admin can access uplaod stock page', { tags: ['@Production', '@CX'] }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Upload Stock', '', '', 'admin/stock');
  });
});
