import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToProcessInNewYork', () => {
  it('Verify User Can Access To Process In New York (NE-TC-1112)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('New York', 'US', 'to-process', 'office-orders/US/to-process');
  });
});
