import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInNewYork', () => {
  it('Verify User Can Access To Return In New York (NE-TC-1113)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('New York', 'US', 'to-return', 'office-orders/US/to-return');
  });
});
