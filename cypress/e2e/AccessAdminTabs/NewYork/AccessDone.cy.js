import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessDoneInNewYork', () => {
  it('Verify User Can Access Done In New York (NE-TC-1108)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('New York', 'US', 'delivered', 'office-orders/US/delivered');
  });
});
