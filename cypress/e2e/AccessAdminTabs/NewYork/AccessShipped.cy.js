import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInNewYork', () => {
  it('Verify User Can Access Shipped In New York (NE-TC-1111)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'New York',
      'US',
      'shipped-final-delivery',
      'office-orders/US/shipped-final-delivery'
    );
  });
});
