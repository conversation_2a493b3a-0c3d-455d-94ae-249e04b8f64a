import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadytoship/DeliverInNewYork', () => {
  it('Verify User Can Access Ready to ship/ Deliver In New York (NE-TC-1110)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'New York',
      'US',
      'ready-final-delivery',
      'office-orders/US/ready-final-delivery'
    );
  });
});
