import Login from '../../../support/Login';
import DiamondRequests from '../../../support/Admin/DiamondRequests';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessInfoDiamoundRequests', () => {
  it('Verify User Can Access Info Diamound Requests (NE-TC-1079)', { tags: '@Production' }, () => {
    const login = new Login();
    const diamondRequests = new DiamondRequests();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Diamond Requests', '', '', 'admin/diamond-request');
    diamondRequests.visitInfoRequestTab();
  });
});
