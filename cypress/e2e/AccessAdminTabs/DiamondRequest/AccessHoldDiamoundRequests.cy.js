import Login from '../../../support/Login';
import DiamondRequests from '../../../support/Admin/DiamondRequests';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessHoldDiamoundRequests', () => {
  it('Verify User Can Access Hold DiamoundRequests (NE-TC-1078)', { tags: '@Production' }, () => {
    const login = new Login();
    const diamondRequests = new DiamondRequests();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Diamond Requests', '', '', 'admin/diamond-request');
    diamondRequests.visitHoldRequestTab();
  });
});
