import Login from '../../../support/Login';
import DiamondRequests from '../../../support/Admin/DiamondRequests';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessBuyDiamoundRequests', () => {
  it('Verify User Can Access Buy DiamoundRequests (NE-TC-1077)', { tags: '@Production' }, () => {
    const login = new Login();
    const diamondRequests = new DiamondRequests();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();
    accessAdminTabs.accessTabs('Diamond Requests', '', '', 'admin/diamond-request');
    diamondRequests.visitBuyRequestTab();
  });
});
