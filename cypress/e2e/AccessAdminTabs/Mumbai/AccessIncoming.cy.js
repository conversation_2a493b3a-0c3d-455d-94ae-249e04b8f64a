import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessIncomingInMumbai', () => {
  it('Verify User Can Access Incoming In Mumbai (NE-TC-1101)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Mumbai',
      'IN/BDB/MUMBAI',
      'office-incoming',
      'office-orders/IN/BDB/MUMBAI/office-incoming'
    );
  });
});
