import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessReadytoship/DeliverInMumbai', () => {
  it('Verify User Can Access Ready to ship/ Deliver In Mumbai (NE-TC-1102)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Mumbai',
      'IN/BDB/MUMBAI',
      'ready-final-delivery',
      'office-orders/IN/BDB/MUMBAI/ready-final-delivery'
    );
  });
});
