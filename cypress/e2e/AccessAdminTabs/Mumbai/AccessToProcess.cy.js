import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToProcessInMumbai', () => {
  it('Verify User Can Access To Process In Mumbai (NE-TC-1104)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Mumbai',
      'IN/BDB/MUMBAI',
      'to-process',
      'office-orders/IN/BDB/MUMBAI/to-process'
    );
  });
});
