import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInMumbai', () => {
  it('Verify User Can Access To Return In Mumbai (NE-TC-1105)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Mumbai',
      'IN/BDB/MUMBAI',
      'to-return',
      'office-orders/IN/BDB/MUMBAI/to-return'
    );
  });
});
