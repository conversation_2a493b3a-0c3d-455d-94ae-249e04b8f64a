import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInMumbai', () => {
  it('Verify User Can Access Shipped In Mumbai (NE-TC-1103)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Mumbai',
      'IN/MUMBAI',
      'shipped-final-delivery',
      'office-orders/IN/MUMBAI/shipped-final-delivery'
    );
  });
});
