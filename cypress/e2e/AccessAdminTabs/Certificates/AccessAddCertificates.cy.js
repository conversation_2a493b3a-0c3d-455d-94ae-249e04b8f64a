import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAddCertificates', () => {
  it('Verify User Can Access Add Certificates (NE-TC-1070)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Certificates', 'Add Certificates', '', 'admin/certificates');
  });
});
