import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessUnansweredInfoPage', () => {
  it('Verify User Can Access Unanswered Info Page (NE-TC-1082)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Enquiries', 'Unanswered Info', '', 'admin/enquiries/unanswered');
  });
});
