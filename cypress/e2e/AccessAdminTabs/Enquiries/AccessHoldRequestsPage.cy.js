import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessHoldRequestPage', () => {
  it('Verify User Can Access Hold Requests Page (NE-TC-1081)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Enquiries', 'Hold Request', '', 'admin/enquiries/hold-requests');
  });
});
