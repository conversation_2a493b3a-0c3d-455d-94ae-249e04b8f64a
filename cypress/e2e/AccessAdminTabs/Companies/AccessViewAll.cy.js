import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessInsurance&CreditSettings', () => {
  it('Verify User Can Access View all in Companies (NE-TC-1075)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Companies', 'View all', '', '/companies');
  });
});
