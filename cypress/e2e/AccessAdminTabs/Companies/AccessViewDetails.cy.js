import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessViewCompanyDetails', () => {
  it('Verify User Can Access View Details in Companies (NE-TC-2449)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Companies', 'View all', '', '/companies');
    accessAdminTabs.viewDetailsClick();
  });
});
