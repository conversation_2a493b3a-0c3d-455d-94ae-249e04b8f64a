import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessIncomingInSurat', () => {
  it('Verify User Can Access Incoming In Surat (NE-TC-1117)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Surat',
      'IN/SURAT',
      'office-incoming',
      'office-orders/IN/SURAT/office-incoming'
    );
  });
});
