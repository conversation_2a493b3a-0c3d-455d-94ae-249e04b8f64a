import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessShippedInSurat', () => {
  it('Verify User Can Access Shipped In Surat (NE-TC-1119)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments(
      'Surat',
      'IN/SURAT',
      'shipped-final-delivery',
      'office-orders/IN/SURAT/shipped-final-delivery'
    );
  });
});
