import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('CreateMeleeShortlistGroup', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify User Can Create A Melee Shortlist Group (NE-TC-916)', () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const product = new Product();
    const navbar = new Navbar();

    cy.getmeleegroupdata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    product.shortlistButtonVerifyMelee('meleegroupdata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Melee');
    shortlist.createShortlistGroup('Melee Group');
    shortlist.addProductToShortlistGroup('meleegroupdata.json');
  });
});
