import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('CreateGemstoneShortlistGroup', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify User Can Create A Gemstone Shortlist Group (NE-TC-915)', () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const product = new Product();
    const navbar = new Navbar();

    cy.getgemsgroupdata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.shortlistButtonVerify('gemsgroupdata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Gemstones');
    shortlist.createShortlistGroup('Gemstones Group');
    shortlist.addProductToShortlistGroup('gemsgroupdata.json');
  });
});
