import Login from '../../support/Login';
import Product from '../../support/Product';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('AddToCartOnHoldDiamondFromShortlistNotAllow', { tags: ['@Shortlist', '@Regression', 'CX'] }, () => {
  const login = new Login();
  const product = new Product();
  const menu = new Menu();
  const shortlist = new Shortlist();

  it('Step 1: User (morcustomer) shortlists the diamond (NE-TC-651)', () => {
    cy.getdiamondonhold();
    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('diamondonhold.json');
    menu.visitMenu();
    menu.VerifyLogout();

    // Clear session for next user
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.window().then((win) => {
      win.sessionStorage.clear();
    });
  });

  it('Step 2: Different user (customer) places the diamond on hold (NE-TC-651)', () => {
    login.loginUsingApi('customer.json');
    product.putProductOnHold('Place ‘Hold’ Request', 'diamondonhold.json');
    menu.visitMenu();
    menu.VerifyLogout();
  });

  it('Step 3: First user (morcustomer) tries to add on-hold diamond to cart and is blocked (NE-TC-651)', () => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.window().then((win) => {
      win.sessionStorage.clear();
    });
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyAddToCartBlockedForOnHoldItem('Diamonds', 'diamondonhold.json');
  });
});
