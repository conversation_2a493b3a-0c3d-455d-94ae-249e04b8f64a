import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('RemoveMeleeFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Remove A Melee From Shortlist (NE-TC-927)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getremovemeleedata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    product.shortlistButtonVerifyMelee('removemeleedata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.removeFromShortlistButtonVerify('Melee', 'removemeleedata.json');
  });
});
