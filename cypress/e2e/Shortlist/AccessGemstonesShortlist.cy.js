import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';

describe('AccessGemstoneShortlist', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify User Can Access Gemstone From Shortlist (NE-TC-734)', () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();

    login.loginUsingApi('morcustomer.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Gemstones');
  });
});
