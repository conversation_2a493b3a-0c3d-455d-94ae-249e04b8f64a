import Login from '../../support/Login';
import Filter from '../../support/Filter';
import Navbar from '../../support/Navbar';

describe('MultipleShortlistGemstone', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Shortlist Multiple Gemstones (NE-TC-2394)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();

    cy.getgemsshortlistcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();

    filter.multiGemshortlist();
  });
});
