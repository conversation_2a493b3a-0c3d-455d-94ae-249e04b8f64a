import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';

describe('AddDiamondsToCartFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Add A Diamond To Cart From Shortlist (NE-TC-650)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();

    cy.getdiamondstockid();
    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('diamondstockid.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.addToCartFromShortlist('Diamonds', 'diamondstockid.json');
  });
});
