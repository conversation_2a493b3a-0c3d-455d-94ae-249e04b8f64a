import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('ShortlistMelee', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Shortlist A Melee (NE-TC-732)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getmeleeshortlistcerts();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    product.shortlistButtonVerifyMelee('meleeshortlistcerts.json');
  });
});
