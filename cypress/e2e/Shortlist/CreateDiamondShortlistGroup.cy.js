import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Product from '../../support/Product';

describe('CreateDiamondShortlistGroup', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify User Can Create A Diamond Shortlist Group (NE-TC-653)', () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const product = new Product();

    cy.getdiamondgroupdata();
    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('diamondgroupdata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Diamonds');
    shortlist.createShortlistGroup('Diamonds Group');
    shortlist.addProductToShortlistGroup('diamondgroupdata.json');
  });
});
