import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';

describe('RemoveDiamondFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Remove Diamond From Shortlist (NE-TC-655)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();

    cy.getremovediamondstockid();
    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('removediamondstockid.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.removeFromShortlistButtonVerify('Diamonds', 'removediamondstockid.json');
  });
});
