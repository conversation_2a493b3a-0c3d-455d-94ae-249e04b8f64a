import Login from '../../support/Login';
import Product from '../../support/Product';

describe('ShortlistDiamond', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Shortlist A Diamond (NE-TC-730)', () => {
    const login = new Login();
    const product = new Product();

    cy.getdiamondshortlistcerts1();
    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('diamondshortlistcerts1.json');
  });
});
