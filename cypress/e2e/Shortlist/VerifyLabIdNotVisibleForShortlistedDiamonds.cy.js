import Login from '../../support/Login';
import Product from '../../support/Product';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifyLabIdNotVisibleForShortlistedDiamonds', { tags: ['@Shortlist', '@Regression'] }, () => {
  it.skip('Verify Lab Id Is Not Visible For Shortlisted Diamonds Upon Hovering On Lab (NE-TC-2112)', () => {
    const login = new Login();
    const product = new Product();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingApi('nonverifiedusers.json');

    product.shortlistStonePDP();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.certIdNotVisibleAssertion();
  });
});
