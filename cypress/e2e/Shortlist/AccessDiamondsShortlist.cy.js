import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';

describe('AccessDiamondShortlist', () => {
  it('Verify User Can Access Diamond From Shortlist (NE-TC-733)', { tags: '@Production' }, () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();

    login.loginUsingApi('morcustomer.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Diamonds');
  });
});
