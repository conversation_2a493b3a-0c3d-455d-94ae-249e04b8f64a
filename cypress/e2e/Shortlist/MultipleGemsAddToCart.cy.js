import Login from '../../support/Login';
import Filter from '../../support/Filter';
import Navbar from '../../support/Navbar';

describe('MultipleGemsAddToCart', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Add to cart Multiple Gemstones (NE-TC-2416)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const filter = new Filter();

    cy.getgemsshortlistcert();

    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    filter.multiGemsAddToCart();
  });
});
