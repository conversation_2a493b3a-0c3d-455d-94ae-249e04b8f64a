import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifyUpdateShortlistModalForMelee', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify Update Shortlist Modal For Melee (NE-TC-2217)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.shortlistMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '2 ct');
    shortlist.verifyUpdateCartModalForMelee();
  });
});
