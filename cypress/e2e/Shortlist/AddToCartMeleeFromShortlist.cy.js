import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('AddMeleeToCartFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Add A Melee To Cart From Shortlist (NE-TC-925)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const navbar = new Navbar();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    product.shortlistButtonVerifyMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.addToCartFromShortlist('Melee');
  });
});
