import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifyMeleePreferenceChangeOnShortlistPage', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify melee preference can be changed on shortlist page (NE-TC-2213)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.shortlistMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '2 ct');
    meleeProduct.editMeleePreference('15');
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '15 pcs');
  });
});
