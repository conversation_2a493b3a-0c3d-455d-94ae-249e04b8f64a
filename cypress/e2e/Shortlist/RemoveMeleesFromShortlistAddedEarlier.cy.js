import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('RemoveMeleesFromShortlist', { tags: ['@Shortlist', '@CX'] }, () => {
  it('Verify User Can Remove Melees Already Added to the Shortlist', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const navbar = new Navbar();
    const melee = new MeleeProduct();

    //cy.getremovemeleedata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    melee.verifyCaratsLowToHigh();

    //Add first item to shortlist
    product.shortlistMelee(0);

    //Add second item to shortlist
    product.shortlistMelee(1);

    menu.visitMenu();
    menu.visitShortList();
    shortlist.selectProductType('Melee');
    melee.removeMeleeViaShortlistModal();
    melee.removeMeleeViaShortlistMenu();
  });
});
