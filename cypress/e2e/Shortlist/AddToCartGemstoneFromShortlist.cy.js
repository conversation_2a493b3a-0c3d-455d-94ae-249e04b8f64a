import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('AddGemstoneToCartFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Add A Gemstone To Cart From Shortlist (NE-TC-924)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getaddtocartgemsdata();
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.shortlistButtonVerify('addtocartgemsdata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.addToCartFromShortlist('Gemstone', 'addtocartgemsdata.json');
  });
});
