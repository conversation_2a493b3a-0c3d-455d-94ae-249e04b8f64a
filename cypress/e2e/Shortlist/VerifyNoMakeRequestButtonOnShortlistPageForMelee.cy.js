import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifyNoMakeRequestButtonOnShortlistPageForMelee', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify make a request button is not displayed on Shortlist page after user input exceeds max quantity (NE-TC-2216)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.shortlistMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '2 ct');
    meleeProduct.verifyRequestButtonNotVisible('99999 ');
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '99999 pcs');
  });
});
