import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifyDisabledAddedToCartTextForMelee', { tags: ['@Shortlist', '@Regression'] }, () => {
  it('Verify Melee added to Cart and shortlist is displayed by disabled Added to Cart text (NE-TC-2218)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.shortlistMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyMeleeDetails('Melee', 'addtocartmeleedata.json', '2 ct');
    shortlist.verifyAddToCartDisabled();
  });
});
