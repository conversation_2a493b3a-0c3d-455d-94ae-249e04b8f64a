import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('ShortlistGemstone', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Shortlist A Gemstone (NE-TC-731)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getgemsshortlistcert();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.shortlistButtonVerify('gemsshortlistcert.json');
  });
});
