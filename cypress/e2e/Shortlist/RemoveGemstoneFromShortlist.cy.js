import Login from '../../support/Login';
import Product from '../../support/Product';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('RemoveGemstoneFromShortlist', { tags: ['@Shortlist', '@Regression', '@CX'] }, () => {
  it('Verify User Can Remove A Gemstone From Shortlist (NE-TC-926)', () => {
    const login = new Login();
    const product = new Product();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getremovegemsdata();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.shortlistButtonVerify('removegemsdata.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.removeFromShortlistButtonVerify('Gemstone', 'removegemsdata.json');
  });
});
