import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyMeleePLPSortingPriceLowToHigh', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee PLP reorders based on Price Low to High sorting (NE-TC-2201)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.verifyPriceLowToHigh();
  });
});
