import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import MeleeSR from '../../support/Admin/Melee-SR';

describe('VerifyMakeRequestButtonOnCheckoutPage', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Make a request button is displayed on checkout page if user input exceeds max quantity (NE-TC-2215)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    meleeProduct.verifyRequestButtonCart('99999999');
  });
});
