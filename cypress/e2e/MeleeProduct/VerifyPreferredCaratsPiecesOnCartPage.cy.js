import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';

describe('VerifyPreferredCaratsPiecesOnCartPage', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Carats/ Pieces preferred by customer is displayed on Cart page (NE-TC-2207)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    meleeProduct.caratsAndPiecesAssertionForCartPage('2 carat');
  });
});
