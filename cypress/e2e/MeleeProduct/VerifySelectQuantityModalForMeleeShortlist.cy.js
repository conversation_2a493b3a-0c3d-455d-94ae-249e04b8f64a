import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifySelectQuantityModalForMeleeShortlist', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Select Quantity modal for Shortlist (NE-TC-2184)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.shortlistQuantityModalVisibleAssertion();
    meleeProduct.verifyQuantityModalForShortlist();
  });
});
