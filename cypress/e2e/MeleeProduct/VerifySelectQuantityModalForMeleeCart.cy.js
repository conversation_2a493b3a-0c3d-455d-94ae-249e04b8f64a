import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifySelectQuantityModalForMeleeCart', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Select Quantity modal for Cart (NE-TC-2185)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    //    meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.verifyQuantityModalForCart();
  });
});
