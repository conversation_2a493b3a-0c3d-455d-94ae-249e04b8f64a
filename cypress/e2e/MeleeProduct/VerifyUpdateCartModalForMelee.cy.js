import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyUpdateCartModalForMelee', () => {
  it('Verify Update Cart modal (NE-TC-2189)', { tags: ['@Melee-Product', '@Regression'] }, () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.verifyUpdateCartModal();
  });
});
