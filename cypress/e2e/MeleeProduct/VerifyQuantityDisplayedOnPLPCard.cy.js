import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyQuantityDisplayedOnPLPCardForMelee', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify quantity added to cart is displayed on PLP card for Melee (NE-TC-2188)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.verifyQuantityAddedToCart('2', '3');
  });
});
