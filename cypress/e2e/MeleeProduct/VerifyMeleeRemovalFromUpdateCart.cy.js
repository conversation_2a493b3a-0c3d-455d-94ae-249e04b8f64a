import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyMeleeRemovalFromUpdateCart', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee added to the cart can be removed from the Update cart modal (NE-TC-2192)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2.0');
    meleeProduct.addMeleeToCart();
    meleeProduct.removeMeleeFromUpdateCartModal();
  });
});
