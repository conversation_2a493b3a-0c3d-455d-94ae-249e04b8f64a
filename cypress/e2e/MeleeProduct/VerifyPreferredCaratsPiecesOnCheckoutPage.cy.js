import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('VerifyPreferredCaratsPiecesOnCheckoutPage', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Carats/ Pieces preferred by customer is displayed on Checkout page (NE-TC-2208)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.verifyCaratsAnsPiecesQuantity('2 carat');
  });
});
