import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyMeleePLPSortingCaratsLowToHigh', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee PLP reorders based on Carat low to high sorting (NE-TC-2203)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.verifyCaratsLowToHigh();
  });
});
