import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyPreferredCaratsPiecesOnMiniCart', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Carats/ Pieces preferred by customer is displayed on Mini cart modal (NE-TC-2206)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.caratsAndPiecesAssertionForMiniCart('2', 'pcs');
  });
});
