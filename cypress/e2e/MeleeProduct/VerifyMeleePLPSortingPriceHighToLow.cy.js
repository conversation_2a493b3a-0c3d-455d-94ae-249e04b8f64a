import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyMeleePLPSortingPriceHighToLow', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee PLP reorders based on Price High to Low sorting (NE-TC-2202)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.verifyPriceHighToLow();
  });
});
