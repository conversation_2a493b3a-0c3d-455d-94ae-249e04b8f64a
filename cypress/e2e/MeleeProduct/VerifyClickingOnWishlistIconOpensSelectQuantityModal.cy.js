import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyClickingOnShortlistIconOpensSelectQuantityModal', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Clicking on Shortlist icon opens Select Quantity modal (NE-TC-2183)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    //    meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.shortlistQuantityModalVisibleAssertion();
  });
});
