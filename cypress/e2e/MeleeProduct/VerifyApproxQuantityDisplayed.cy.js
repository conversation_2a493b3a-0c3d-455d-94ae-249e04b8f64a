import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyApproxQuantityDisplayed', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Approx quantity in other preference is displayed after adding value in quantity input box (NE-TC-2187)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    //meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.totalPriceNotVisibleAssertion();
    meleeProduct.addMeleeInputs('0.5');
    meleeProduct.verifyApproxQuantityOfCaratsPieces();
  });
});
