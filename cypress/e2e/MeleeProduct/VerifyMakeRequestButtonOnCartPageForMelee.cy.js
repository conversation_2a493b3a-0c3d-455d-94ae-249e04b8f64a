import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';

describe('VerifyMakeRequestButtonOnCartPageForMelee', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Make a request button is displayed on cart page if user input exceeds max quantity (NE-TC-2214)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    meleeProduct.verifyRequestButtonCart('999999');
  });
});
