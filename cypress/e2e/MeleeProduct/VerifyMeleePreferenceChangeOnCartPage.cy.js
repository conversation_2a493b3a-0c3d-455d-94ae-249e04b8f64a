import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';

describe('VerifyMeleePreferenceChangeOnCartPage', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify the Melee preference can be changed on cart page (NE-TC-2211)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    meleeProduct.caratsAndPiecesAssertionForCartPage('2 carat');
    meleeProduct.editMeleePreference('15');
    meleeProduct.caratsAndPiecesAssertionForCartPage('15 pieces');
  });
});
