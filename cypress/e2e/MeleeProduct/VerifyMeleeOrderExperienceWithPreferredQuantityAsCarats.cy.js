import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('VerifyMeleeOrderExperienceWithPreferredQuantityAsCarats', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee Order Experience with Preferred Quantity as Carats (NE-TC-2375)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2.0');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
    checkout.verifyCaratsAnsPiecesQuantity('2 carat');
  });
});
