import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyMeleePLPSortingCaratsHighToLow', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Melee PLP reorders based on Carat High to Low sorting (NE-TC-2204)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    meleeProduct.verifyCaratsHighToLow();
  });
});
