import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Menu from '../../support/Menu';
import Shortlist from '../../support/Shortlist';

describe('VerifySameQuantityAddedToShortlist', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify same quantity as that in the cart is added to shortlist on clicking on Shortlist icon (NE-TC-2191)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const menu = new Menu();
    const shortlist = new Shortlist();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    //    meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2.0');
    meleeProduct.addMeleeToCart();
    meleeProduct.shortlistMelee();
    menu.visitMenu();
    menu.visitShortList();
    shortlist.verifyShortlistedMeleeDetails('Melee', '2 ct');
  });
});
