import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyNivodaVerifiedModal', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Nivoda Verified modal (NE-TC-2193)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.verifyNivodaVerifiedModal();
  });
});
