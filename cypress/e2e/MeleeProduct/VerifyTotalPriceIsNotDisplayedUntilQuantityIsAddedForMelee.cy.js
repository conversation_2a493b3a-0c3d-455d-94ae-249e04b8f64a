import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe(
  'VerifyTotalPriceIsNotDisplayedUntilQuantityIsAddedForMelee',
  { tags: ['@Melee-Product', '@Regression'] },
  () => {
    it('Verify Total Price is not displayed until quantity is added on Melee PLP (NE-TC-2182)', () => {
      const login = new Login();
      const navbar = new Navbar();
      const meleeProduct = new MeleeProduct();

      login.loginUsingUniqueUsersApi();
      navbar.visitNaturalMelee();
      // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
      meleeProduct.totalPriceNotVisibleAssertion();
      meleeProduct.addMeleeInputs('2.0');
      meleeProduct.addMeleeToCart();
      meleeProduct.totalPriceVisibleAssertion();
    });
  }
);
