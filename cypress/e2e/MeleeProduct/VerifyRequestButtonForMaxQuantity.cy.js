import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyRequestButtonForMaxQuantity', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Make a request button is displayed when max quantity is exceeded for Select Quantity modal (NE-TC-2186)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('999999');
    meleeProduct.meleeRequestButtonVisibilityAssertion();
  });
});
