import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe(
  'Verify upload Success for Diamonds Via .CSV file and .XLSX file ',
  { tags: ['@Regression', '@Supplier'] },
  () => {
    const login = new Login();
    const supplier = new Supplier();

    beforeEach(() => {
      login.visitPage();
      login.loginUsingUi('loginasdirectuploadsupplier.json');
      login.loginAsSupplierAssertion();
      supplier.getPreviousTime();
    });

    it('Verify Upload stock for diamonds using .csv file (NE-TC-2789)', () => {
      supplier.uploadFile('diamonds.csv', 'diamonds.csv');
      supplier.getLatestTimeAndStatus();
    });

    it('Verify upload stock for Diamonds using .XLSX file (NE-TC-3210)', () => {
      supplier.uploadXlsxFile('diamonds.xlsx');
      supplier.getLatestTimeAndStatus();
    });
  }
);
