import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify upload Size', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Verify file size greater than 30MB is not accepted(NE-TC-3250)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    supplier.uploadFile('30mb.csv', '30mb.csv');
    supplier.uploadFile30MbPopupVerify(
      'File not uploaded. Only csv, xls and xlsx files that are less than 30 MB can be uploaded.'
    );
  });
});
