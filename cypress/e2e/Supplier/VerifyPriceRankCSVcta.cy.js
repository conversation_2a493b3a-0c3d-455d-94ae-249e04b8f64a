import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify download price rank CTA', () => {
  it(
    'Verify by clicking on download price ranking file under price analytics page it should get downloaded (NE-TC-2769)',
    { tags: ['@Production', '@Supplier'] },
    () => {
      const login = new Login();
      const menu = new Menu();
      const supplier = new Supplier();

      login.visitPage();
      login.loginUsingUi('loginassupplier.json');
      login.loginAsSupplierAssertion();

      menu.accessSupplierTabs('Analytics', '/analytics');
      supplier.priceRankClick();
    }
  );
});
