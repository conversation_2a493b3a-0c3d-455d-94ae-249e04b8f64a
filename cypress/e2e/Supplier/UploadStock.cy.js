import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Download My Stock Button on Upload Stock Page', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier has uploaded stock with images and video', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    supplier.uploadFile('uploadsupplierdata.csv', 'uploadsupplierdata.csv');
  });
});
