import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Download Sample File', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const supplier = new Supplier();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify sample file download for diamonds (NE-TC-3339)', () => {
    supplier.sampleDiamondFileDownload();
  });

  it('Verify sample file download for gems (NE-TC-2858)', () => {
    supplier.sampleGemsFileDownload();
  });
});
