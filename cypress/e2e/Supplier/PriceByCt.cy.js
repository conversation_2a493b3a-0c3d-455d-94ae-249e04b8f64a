import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Price Filter NE-TC- 2650 ', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify Price filter for Naturals NE-TC-3359 ', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.priceCt();
  });

  it('Verify Price filter for Labgrown NE-TC-3360', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.priceCt();
  });

  it('Verify Price filter for Gemstone Ne-Tc-3361', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.priceCt();
  });
});
