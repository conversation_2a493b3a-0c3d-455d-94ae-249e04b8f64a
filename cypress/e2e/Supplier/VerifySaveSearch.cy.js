import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';
import Menu from '../../support/Menu';

describe('Verify sx save search', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to save search (NE-TC-2655)', () => {
    const login = new Login();
    const supplier = new Supplier();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');

    menu.allFilterMenu();
    supplier.fancyColor();
    menu.applyFilters();
    supplier.searchSave();
  });
});
