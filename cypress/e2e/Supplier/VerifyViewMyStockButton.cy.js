import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify View My Stock Button', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const supplier = new Supplier();

  it('Verify View My Stock Button for a Natural Supplier (NE-TC-2506)', () => {
    login.visitPage();
    login.loginUsingUi('NaturalDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    supplier.verifyViewMyStockButton('search/natural/diamond');
  });

  it('Verify View My Stock Button for a labgrown Supplier (NE-TC-2580)', () => {
    login.visitPage();
    login.loginUsingUi('LabGrownDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    supplier.verifyViewMyStockButton('search/labgrown/diamond');
  });

  it('Verify View My Stock Button for a gemstones Supplier (NE-TC-2581)', () => {
    login.visitPage();
    login.loginUsingUi('gemstonesupplier.json');
    login.loginAsSupplierAssertion();
    supplier.verifyViewMyStockButton('search/gemstones');
  });
});
