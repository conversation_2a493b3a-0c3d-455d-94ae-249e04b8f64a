import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Need Certs for Gemstones', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Verify NeedCert for Gemstones  (NE-TC-3398)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginasdirectuploadsuppliergemstone.json');
    login.loginAsSupplierAssertion();
    supplier.getPreviousTime();
    supplier.gemstoneDropdown();
    supplier.uploadFile('needcertgemstone.csv', 'needcertgemstone.csv');
    supplier.getLatestTimeAndStatus();
    supplier.checkAndClickInTheTopandBottomTable(1, 12, 0, 0);
  });
});
