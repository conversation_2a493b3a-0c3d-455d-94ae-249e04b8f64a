import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Supplier from '../../../support/Supplier/Supplier';

describe('New location add', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  it('supplier should add new location  NE-TC-2666', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.visitSupplierLocationPage();
    supplier.addNewLocation();
  });
});
