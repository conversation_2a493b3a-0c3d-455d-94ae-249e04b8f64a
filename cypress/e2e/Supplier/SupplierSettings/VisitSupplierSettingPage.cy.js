import Login from '../../../support/Login';
import Menu from '../../../support/Menu';

describe('Visit sx setting page', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();

  it('supplier should visit supplier settings page NE-TC-3386', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.visitSupplierSettingsPage();
  });
});
