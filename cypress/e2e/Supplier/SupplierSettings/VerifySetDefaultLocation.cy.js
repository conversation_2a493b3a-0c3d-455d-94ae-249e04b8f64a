import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Supplier from '../../../support/Supplier/Supplier';

describe('Verify set default location', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  it('supplier should set default location NE-TC-2669', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.visitSupplierLocationPage();
    supplier.setDefaultLocation();
  });
});
