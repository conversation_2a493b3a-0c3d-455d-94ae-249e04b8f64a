import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Supplier from '../../../support/Supplier/Supplier';

describe('Update password', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  it('supplier should be able to update password (NE-TC-3390)', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.visitSupplierPasswordSettingsPage();
    supplier.updatePassword();
  });
});
