import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Supplier from '../../../support/Supplier/Supplier';

describe('Verify update location', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  it('supplier should update the location NE-TC-2667', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.visitSupplierLocationPage();
    supplier.updateLocation();
  });
});
