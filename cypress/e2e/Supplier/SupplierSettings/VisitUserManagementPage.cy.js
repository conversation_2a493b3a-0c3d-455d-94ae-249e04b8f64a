import Login from '../../../support/Login';
import Menu from '../../../support/Menu';

describe('Visit user page', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();

  it('supplier should visit user page NE-TC-3394', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.visitSupplierUserPage();
  });
});
