import Login from '../../../support/Login';
import Menu from '../../../support/Menu';

describe('Visit location page', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();

  it('supplier should visit location page NE-TC-3393', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.visitSupplierLocationPage();
  });
});
