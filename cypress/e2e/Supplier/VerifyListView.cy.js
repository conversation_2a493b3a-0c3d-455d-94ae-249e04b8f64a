import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';
import Menu from '../../support/Menu';

describe('Verify the list view', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardAnalyticsDisabled.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify List View in Naturals(NE-TC-3221)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.verifyListView();
  });

  it('Verify List View in Labgrown(NE-TC-3222)', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    supplier.verifyListView();
  });

  it('Verify List view in Gemstone (NE-TC-3220) ', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    supplier.verifyListView();
  });
});
