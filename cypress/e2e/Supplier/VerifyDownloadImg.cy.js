import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Verify Download img', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify download img for Naturals NE-TC-3350', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyArrow();
    filter.downloadImg();
  });

  it('Verify download img for Labgrown NE-TC-3351', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyArrow();
    filter.downloadImg();
  });

  it('Verify download img for gems NE-TC-3352', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.verifyArrow();
    filter.downloadImg();
  });
});
