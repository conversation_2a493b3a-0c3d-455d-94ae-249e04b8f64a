import Login from '../../../support/Login';
import Filter from '../../../support/Filter';
import Menu from '../../../support/Menu';

describe('Verify Help drop down', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier has clicked on HELP and Help Center (NE-TC-3342)(NE-TC-3365)', () => {
    const login = new Login();
    const menu = new Menu();
    const filter = new Filter();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    filter.helpClicked();
  });
});
