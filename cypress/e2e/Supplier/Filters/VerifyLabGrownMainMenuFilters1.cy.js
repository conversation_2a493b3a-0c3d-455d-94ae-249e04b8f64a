import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Main Menu Filters', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to  cut, polish & symmetry, Fluorescence filter (NE-TC-2646, NE-TC-2647', () => {
    const login = new Login();
    const supplier = new Supplier();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');

    menu.allFilterMenu();
    supplier.applyLabFilters('3EX', 'Medium');
    menu.applyFilters();
  });
});
