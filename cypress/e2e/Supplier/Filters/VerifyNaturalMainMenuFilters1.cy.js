import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Main Menu Filters', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to filter certificate, shape,carat,  color, clarity (NE-TC-2639, NE-TC-2642, NE-TC-2343, NE-TC-2644,NE-TC-2645)', () => {
    const login = new Login();
    const supplier = new Supplier();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');

    menu.allFilterMenu();
    supplier.applyNaturalDiamondFilters('GIA', 'Round', '30s', 'E', 'VVS1');
    menu.applyFilters();
  });
});
