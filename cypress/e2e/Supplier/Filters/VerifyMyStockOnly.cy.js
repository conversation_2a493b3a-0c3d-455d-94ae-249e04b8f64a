import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify my stock only', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardAnalyticsDisabled.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify my stock for Naturals (NE-TC-3400)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.myStockOnly();
  });

  it('Verify my stock for Labgrown (NE-TC-3401)', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    supplier.myStockOnly();
  });

  it('Verify my stock for Gemstone (NE-TC-3402) ', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    supplier.myStockOnly();
  });
});
