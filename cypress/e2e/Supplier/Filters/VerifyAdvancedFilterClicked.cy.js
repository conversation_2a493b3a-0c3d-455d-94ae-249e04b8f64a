import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Advanced filter', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify advanced filter for Naturals NE-TC-2651', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    menu.allFilterMenu();
    supplier.verifyAdvancedClicked();
  });

  it('Verify Advanced filter for Labgrown NE-TC-3395', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    menu.allFilterMenu();
    supplier.verifyAdvancedClicked();
  });
});
