import Login from '../../../support/Login';
import Filter from '../../../support/Filter';
import Menu from '../../../support/Menu';

describe('Verify Labels ', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify Lab label for Naturals NE-TC-3341', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyNaturalLabel();
  });

  it('Verify Natural label for Labgrown NE-TC-3340', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyLabLabel();
  });
});
