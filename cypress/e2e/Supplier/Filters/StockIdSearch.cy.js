import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Stock id Search', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const supplier = new Supplier();
  const menu = new Menu();

  it('Verify stock id search for Natural Supplier (NE-TC-3400)', () => {
    login.visitPage();
    login.loginUsingUi('NaturalDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.myStockOnly();
    supplier.certSearch('suppliernaturaldiamondstockid.json');
  });

  it('Verify stock id search for labgrown  (NE-TC-3401)', () => {
    login.visitPage();
    login.loginUsingUi('NaturalDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    supplier.myStockOnly();
    supplier.certSearch('supplierlabdiamondstockid.json');
  });

  it('Verify stock id search for a gemstones  (NE-TC-3402)', () => {
    login.visitPage();
    login.loginUsingUi('gemstonesupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    supplier.myStockOnly();
    supplier.certSearch('suppliergemstonestockid.json');
  });
});
