import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Menu Filters', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to filter as shade and luster NE-TC-2649)', () => {
    const login = new Login();
    const supplier = new Supplier();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');

    menu.allFilterMenu();
    supplier.shadeFilter();
    menu.applyFilters();
  });
});
