import Login from '../../../support/Login';
import Filter from '../../../support/Filter';
import Menu from '../../../support/Menu';

describe('Verify img video filter NE-TC-2816', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify img/video filter for Naturals NE-TC-3356', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.HasImg();
    filter.HasVideo();
  });

  it('Verify img/video filter  for Labgrown NE-TC-3357', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.HasImg();
    filter.HasVideo();
  });

  it('Verify img/video filter for Gemstone NE-TC-3358', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.HasImg();
    filter.HasVideo();
  });
});
