import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Gems Main Menu Filters', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to  gems filter NE-TC-3397', () => {
    const login = new Login();
    const supplier = new Supplier();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    menu.accessSupplierTabs('Gemstones', '/search/gemstones');

    menu.allFilterMenu();
    supplier.gemsFilters('AGL', 'Emerald', 'Oiling');
    menu.applyFilters();
  });
});
