import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe(
  'Verify upload Success for Gemstones Via .CSV file and .XLSX file',
  { tags: ['@Regression', '@Supplier'] },
  () => {
    const login = new Login();
    const supplier = new Supplier();

    beforeEach(() => {
      login.visitPage();
      login.loginUsingUi('loginasdirectuploadsuppliergemstone.json');
      login.loginAsSupplierAssertion();
      supplier.getPreviousTime();
      supplier.gemstoneDropdown();
    });

    it('Verify Upload stock for gemstones using .csv file (NE-TC-3211)', () => {
      supplier.uploadFile('gemstones.csv', 'gemstones.csv');
      supplier.getLatestTimeAndStatus();
    });

    it('Verify upload Stock  for Gemstones Via  .XLSX file (NE-TC-3212)', () => {
      supplier.uploadXlsxFile('gemstonesxls.xlsx');
      supplier.getLatestTimeAndStatus();
    });
  }
);
