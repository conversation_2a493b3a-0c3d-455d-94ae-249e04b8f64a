import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify the Gridview', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardAnalyticsDisabled.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify Grid View in Naturals(NE-TC-2625)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.verifyGridView();
  });

  it('Verify Grid View in Labgrown (NE-TC-2626)', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    supplier.verifyGridView();
  });

  it('Verify Grid view in Gemstone (NE-TC-2627)', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    supplier.verifyGridView();
  });
});
