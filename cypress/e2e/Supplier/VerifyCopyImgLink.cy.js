import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Verify copy img', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify copy img for Naturals NE-TC-3344', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyArrow();
    filter.copyImgLink();
  });

  it('Verify copy img for Labgrown NE-TC-3345', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyArrow();
    filter.copyImgLink();
  });

  it('Verify copy img for gems NE-TC-3346', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.HasImg();
    filter.verifyArrow();
    filter.copyImgLink();
  });
});
