import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Invalid for Diamonds', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Verify Invalid for diamonds  (NE-TC-2706)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginasdirectuploadsupplier.json');
    login.loginAsSupplierAssertion();
    supplier.getPreviousTime();

    supplier.uploadFile('invaliddiamond.csv', 'invaliddiamond.csv');
    supplier.getLatestTimeAndStatus();
    supplier.checkAndClickInTheTopandBottomTable(0, 6, 3, 0);
  });
});
