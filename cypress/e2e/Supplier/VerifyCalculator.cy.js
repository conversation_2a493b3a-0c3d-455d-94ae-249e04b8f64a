import Calculator from '../../support/Supplier/Calculator';
import Login from '../../support/Login';

describe('Diamond Calculator Tests', { tags: ['@Regression', '@Supplier'] }, () => {
  const calculator = new Calculator();
  const login = new Login();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardenabled.json');
    login.loginAsDashboardEnabledSupplierAssertion();
  });

  it('Verifies price calculation with Negative discount(NE-TC-3213)', () => {
    calculator.verifyNegativeDiscount();
  });

  it('Verifies price calculation with Positive discount(NE-TC-3214)', () => {
    calculator.verifyPositiveDiscount();
  });

  it('Verifies the Calculator access from the Holds Page (NE-TC-3215)', () => {
    calculator.navigateTo('Holds');
  });

  it('Verifies the Calculator access from the Global inventory Page (NE-TC-3216)', () => {
    calculator.navigateTo('Global inventory');
  });

  it('Verifies the Calculator access from the Consignments Page(NE-TC-3223)', () => {
    calculator.navigateTo('Consignments');
  });

  it('Verifies the Calculator access from the Orders Page(NE-TC-3227)', () => {
    calculator.navigateTo('Orders');
  });

  it('Verifies the Calculator access from the Invoices Page(NE-TC-3228)', () => {
    calculator.navigateTo('Invoices');
  });

  it('Verifies the Calculator access from the Diamond enquiries Page(NE-TC-3226)', () => {
    calculator.navigateTo('Diamond enquiries');
  });

  it('Verifies the Calculator access from the Feed center Page(NE-TC-3225)', () => {
    calculator.navigateTo('Feed center');
  });

  it('Verifies the Calculator access from the Upload stock Page(NE-TC-3224)', () => {
    calculator.navigateTo('Upload stock');
  });

  it('Verifies the Calculator access from the Market Insights Page(NE-TC-3217)', () => {
    calculator.navigateTo('Market Insights');
  });

  it('Verifies the Calculator access from the My Pricing Insights Page (NE-TC-3219)', () => {
    calculator.navigateTo('My Pricing Insights');
  });

  it('Verifies the Calculator access from the Settings Page(NE-TC-3218)', () => {
    calculator.navigateTo('Settings');
  });
});
