import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Filtered stone for Diamonds', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Verify Filtered stones  for diamonds  (NE-TC-3403)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginasdirectuploadsupplier.json');
    login.loginAsSupplierAssertion();
    supplier.getPreviousTime();
    supplier.uploadFile('filtereddiamond.csv', 'filtereddiamond.csv');
    supplier.getLatestTimeAndStatus();
    supplier.filteredStonesCheck();
  });
});
