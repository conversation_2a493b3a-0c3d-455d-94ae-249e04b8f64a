import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';
import Menu from '../../support/Menu';

describe('Download Sample File', { tags: ['@Regression', '@Supplier'] }, () => {
  const login = new Login();
  const supplier = new Supplier();
  const menu = new Menu();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Default Price: low to high sort (NE-TC-2638) ', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaultSort();
  });

  it('Default Price: high to low sort (NE-TC-2634)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaultPriceSort();
  });

  it('Default Carat: high to low sort (NE-TC-2635)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaulhighCaratSort();
  });

  it('Default Carat: low to high sort (NE-TC-2635)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaultlowCaratSort();
  });

  it('Default Discount: high to low sort (NE-TC-2636)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaulDiscountSort();
  });

  it('Default recently added sort (NE-TC-2637)', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.defaulrecentaddedSort();
  });
});
