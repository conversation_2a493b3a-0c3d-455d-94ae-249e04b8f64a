import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';
import Returns from '../../support/Admin/Returns';
import Accounting from '../../support/Admin/Accounting';
import Navbar from '../../support/Navbar';

describe('CreateCreditNoteForGemstone&DiamondTogether', { tags: ['@CreditNote', '@OTP'] }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();
  const returns = new Returns();
  const accounting = new Accounting();
  const navbar = new Navbar();
  it('Verify User Can Create Credit Note ForGemstone & Diamond Together (NE-TC-2003)', () => {
    cy.getcreditNoteDiamond();
    cy.getcreditNoteGems();
    cy.getMultipleAddressUser();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/creditNoteDiamond.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/creditNoteGems.json');
    product.viewCart();
    checkout.proceedToDeliveryOptions();
    checkout.deliverToMultipleAddresses('multipleaddressuser.json');
    checkout.multipleOrderConfirmationText();
  });
  it('Verify User Can Create Credit Note ForGemstone & Diamond Together (NE-TC-2003)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteDiamond.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/creditstoneorderNumber2.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteDiamond.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.confirmRTC(
      'ALL',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/creditstoneorderNumber2.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.selectStone('Gemstone');
    admin.markGemStoneQcPass('cypress/fixtures/creditstoneorderNumber2.json');
    admin.selectStone('Diamond');
    admin.markStoneQcPass('cypress/fixtures/creditstoneorderNumber1.json');
    admin.accessAccounting('To Invoice');
    admin.createInvoice('cypress/fixtures/multipleaddressuser.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createShipmentAndVerifyAddress(
      'BDB Mumbai Office IN',
      'NYC Office US',
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'cypress/fixtures/creditstoneorderNumber2.json'
    );
    accessAdminTabs.accessTabs('Returns', 'Initiate return', '', 'returns/initiate');
    returns.initiateReturn(
      'cypress/fixtures/creditstoneorderNumber1.json',
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditNoteDiamond.json'
    );
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'Credit Notes', 'accounting/customer-returns');
    accounting.createCreditNote(
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditstoneorderNumber1.json'
    );
  });
});
