import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Accounting from '../../support/Admin/Accounting';

describe('CreateCreditNoteECF', { tags: ['@CreditNote', '@OTP'] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const accounting = new Accounting();

  it('Verify User Can Create ECF Credit Note', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Credit Notes', '', 'customer-returns');
    accounting.createECFCreditNote('cypress/fixtures/multipleaddressuser.json');
  });
});
