import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Accounting from '../../support/Admin/Accounting';

describe('CreateCreditNoteFPPromotional', { tags: ['@CreditNote', '@OTP'] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const accounting = new Accounting();

  it('Verify User Can Create First Purchase Promotional Credit Note', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Credit Notes', '', 'customer-returns');
    accounting.createSelectedCreditNote(
      'cypress/fixtures/multipleaddressuser.json',
      'FP_PROMOTION',
      'Create First Purchase Promotional Credit Note'
    );
  });
});
