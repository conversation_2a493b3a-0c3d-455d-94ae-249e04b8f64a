import Login from '../../support/Login';
import Company from '../../support/Admin/Company';

describe('VoidCreditNote', { tags: ['@CreditNote', '@Regression'] }, () => {
  it('Verify User Can Void A Credit Note (NE-TC-2002)', () => {
    const login = new Login();
    const company = new Company();

    login.loginUsingAdminApi('loginasadmin.json');
    company.voidCreditNote('multipleaddressuser.json');
  });
});
