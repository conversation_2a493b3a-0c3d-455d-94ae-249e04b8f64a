import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Accounting from '../../support/Admin/Accounting';

describe('CreateCreditNoteDiscount', { tags: ['@CreditNote', '@OTP'] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const accounting = new Accounting();

  it('Verify User Can Create Discount Credit Note', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Credit Notes', '', 'customer-returns');
    accounting.createSelectedCreditNote(
      'cypress/fixtures/multipleaddressuser.json',
      'DISCOUNT',
      'Create Discount Credit Note'
    );
  });
});
