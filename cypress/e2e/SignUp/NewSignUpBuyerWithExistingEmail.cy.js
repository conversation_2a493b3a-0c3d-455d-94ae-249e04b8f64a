import SignUp from '../../support/SignUp';
import Login from '../../support/Login';

describe('NewSignUpBuyerWithExistingEmail', { tags: ['@Regression', '@SignUp', '@CX'] }, () => {
  it('Verify Buyer Cant Sign Up With An Existing Email (NE-TC-691)', () => {
    const signUp = new SignUp();
    const login = new Login();

    login.visitPage();
    signUp.signUpBuyerWithExistingEmail('morcustomer.json');
  });
});
