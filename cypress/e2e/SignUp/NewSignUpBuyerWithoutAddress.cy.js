import SignUp from '../../support/SignUp';
import Login from '../../support/Login';

describe('NewSignUpBuyerWithoutAdrress', { tags: ['@OTP', '@SignUp', '@CX'] }, () => {
  it('Verify Buyer Can Sign Up Without Address  (NE-TC-690)', () => {
    const signUp = new SignUp();
    const login = new Login();

    login.visitPage();
    signUp.signUpBuyerWithoutaddress();
  });
  it('Verify Login Redirect to Address Page (NE-TC-690)', () => {
    const signUp = new SignUp();
    const login = new Login();
    login.visitPage();
    login.loginUsingUi('emailData.json');
    signUp.verifyAddressUrl();
  });
});
