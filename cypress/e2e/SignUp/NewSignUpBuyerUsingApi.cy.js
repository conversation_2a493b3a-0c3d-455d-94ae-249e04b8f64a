import { faker } from '@faker-js/faker';
import Login from '../../support/Login';

describe('GraphQL API Test - Initialize Signup Step One', { tags: ['@API', '@SignUp', '@Regression', '@CX'] }, () => {
  let firstName, lastName, email, phone, token;

  before(() => {
    firstName = faker.person.firstName();
    lastName = faker.person.lastName();
    email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@apinivoda.com`;
    phone = faker.phone.number('+923344######');

    cy.log(`Generated User Details: 
      First Name: ${firstName}, 
      Last Name: ${lastName}, 
      Email: ${email}, 
      Phone: ${phone}`);
  });

  it('should send a mutation request to initialize signup step one and save the response id', () => {
    const mutation = `
      mutation ($email: String!, $password: String!, $first_name: String!, $last_name: String!, $role: String!, $phone: String!, $preferred_language: PreferredLanguages!) {
        
          initialize_signup_step_one(
            email: $email
            first_name: $first_name
            last_name: $last_name
            password: $password
            role: $role
            phone: $phone
            preferred_language: $preferred_language
          ) {
            id
            verify_status
            __typename
          }
        }
      
    `;

    const variables = {
      email,
      first_name: firstName,
      last_name: lastName,
      password: 'Nivoda123',
      role: 'CUSTOMER',
      phone,
      preferred_language: 'EN'
    };
    const apiUrl = Cypress.env('apiurl');

    cy.request({
      method: 'POST',
      url: apiUrl,
      body: {
        query: mutation,
        variables: variables
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${variables.token}`
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      console.log(JSON.stringify(response.body));
      const responseId = response.body.data.initialize_signup_step_one.id;
      expect(responseId).to.exist;

      cy.writeFile('cypress/fixtures/responseId.json', { id: responseId });
    });
  });

  it('should confirm the email using the OTP from the database', () => {
    const reCaptchaToken =
      '03AFcWeA6wUSy32gvb6C072YhHjMYv7Upp6FPCMotAru9jcX73o9ERSgkh-jSRAPzueOtuTMZNEf8y2pV-kbkGDp1N0EREY1B59R_Zbpuothd8ZqOBFrT3mnLXdLkyIRXCerj9Bpr8gy_rfqnPy0SrSjftdUqy4ZhZqH0IQqBXLW73xwVKM7QylTqvenR40eBlKxZLw9hW0aSrlnPLU4aeWLa2ZdJDr97VcCbBOAT8XOkTSO1TPpXmiu_XhLptuId6Hbmy7z-xg1EfP3TJ6AXtkDe5CVX2ttwkyTf8ylzesPP7iU_wUD_tJ0Y6OmX4cQrwC36VDwAG88yoFTKcJsUt1kCwm5o410tA5LrY1NjPArmLj6UXM8WtUGkfZt2h07Md4aPhwF2IiiSNYaahRdHoWlUVsFAfc0hznk7cfvvCyHEiMa9lwa_-LkPGzXW5yZDmUqaQha8fb88CJ8JUHeihx_bZBDe-BdchlUTeb3uTYutdrQF16WWZ51TRRObkSnFkW1rJ2AJ7jX3D-GVjyDST_zpgT-QC9I8frBTU7JX6Ro_qhBaGw8n7Mv9jlWwQIkIiAG_BugTOqCqxmdZpI_CF2wveIbfLi9kpm580uzJM7cFvRbRe_R4scahDEoAcawwIWmDLQap3nRWVGGOT4HOdhIGYuM98R9pKL-MBOoVj7w45rynAobWMeImBlyUJ0pRvyPCpw1IpKJ68g32SrG3HqvZ9NQPzep6wsusk1-S08Dp16J7qRPrOs8NkagChfjihuAwwJrHffANtuVKpc5g6RyMpPT05WxPif2n1B6oQD4kPNB_rmyooIQ_YXbGKrMqRiN8svhyu2nEn_r2BAijmRj4MtUMukN0s42N5fbWAitNRtPVGNKjOSDpM3MsDYIhp1HLgKGMWvf1--O0njpy5W0xjAjDF9arlbYoXNGXvlInbTnMIoAM5gPI9xt_2LeU5e1S-vX5zUvaOioWDWEkG6AcoOjekPyGQxvt_56sGBHUqAbU76rF-mKWmfa2DlW_MXL3BkmCuvsJGQBRetTQnP-02bzimN_ryXT04pk-nsurfn-6q_yyuYcZkevJb77o6aNxA4NoCbbPqW_irwZzmdUbCdc5yLz-w1bjNvC6yjQYNXn8eu7TKnOA1v6so5wlNQMaJwgQvI7WoAjvHte-fBMyCFFpLD_Gn0VjrLnmt1RsYKUyNm63ripLg55AmUIx-N_yDwD8Vx-cQcEP2y1LQwkmTwmiwkdkPQNIIVB9euZ7rpWJ_qmbxTPqxpKOrhEDZc76Uo1vDqi-Oi_Hb9NMp6TcGW2RVomgtH5vMCUO8iIbZCXfaIN7t1FnETuTz0kSoNu5mT06d2ffq6bdIzcQz-uRftv85xPPwk6SmyWZ921yBH1nRuDGWD6lFicP4KpNYgNXybGtwHt9kviiE7HYHTltoGCqI_fSkZBOBnIHiKJmn1ADklXWdT4JvSARYPdjQha2yHFmA0sIcsoIVCnxTm9KVd0o7bTm9ZtiphlD6w9Stt34l7i7SVqqUJsFP5QXTJ80XBZrsJjYO43qauob6MwJOXtYmrtWOkR2cMlF33wu3DVEK5BROibjJ3Z55hojuzAji7xY2U5sxROIAhINWl4SLtxMS7eEkfCx7sFGvQO2-QOX5U1Nkpa03psdOc8FzQjbLDhpzf8-wKKT20pv4lQbHLeC6_ANjBiWmNRPFeD2Nv3eoPIgv9_mD_i9Z5eHIR8EJD4FSDli9Y32Dfy5KibdLnLndlltlkkxtDiR2cbdIQUyeCV8Ocl_ysrY8ru0nKzI7ihsAj2D-F_23WAKoBjuNi8xZaDCr1e8kDPKTo5GmtO7nxpeyIY5lhKr7tZ7J5TKdq1Lxhw-5hTg5pF10yIIMCPp9PGyKF4279HiFrNdUJbtt2af9cZ4isYUwKojkIquMh3Blwn2gjAzAN5f2gReThhkjfuOJf0LhYrdYIqHW5BexSRvJrPxTD_Pwf2GUUl9FYH0H9r8dJTrI33dYcWT-0na5hnl0aXY48ZiZeWa0BND_j0KuNCOlnLrgUZPzWwXGWBekjyGn';
    cy.wait(1000);
    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT code, "createdAt"
      FROM "CodeConfirms"
      WHERE value = '${email}'
      ORDER BY "createdAt" DESC`
    }).then((result) => {
      const confirmationCode = result.rows[0].code;
      cy.log(confirmationCode);
      console.log(confirmationCode);
      const query = `
      mutation ($email: String!, $confirmation_code: String!, $reCaptchaToken: String!) {
        
          confirm_email_with_code(
            email: $email
            confirmation_code: $confirmation_code
            reCaptchaToken: $reCaptchaToken
          )
        }
      
    `;
      const apiUrl = Cypress.env('apiurl');

      cy.request({
        method: 'POST',
        url: apiUrl,
        body: {
          query,
          variables: {
            email,
            confirmation_code: confirmationCode,
            reCaptchaToken
          }
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
      });
    });
  });
  it('should confirm the phone using the SMS code from the database', () => {
    cy.wait(2000);
    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT code, "createdAt"
            FROM "CodeConfirms"
            WHERE value = '${phone}'
            ORDER BY "createdAt" DESC`
    }).then((result) => {
      const smsCode = result.rows[0].code;
      cy.log(smsCode);
      console.log(smsCode);

      const query = `
        mutation ($phone: String!, $sms_code: String!) {
            confirm_phone(phone: $phone, sms_code: $sms_code)
          }
        
      `;
      const apiUrl = Cypress.env('apiurl');

      cy.request({
        method: 'POST',
        url: apiUrl,
        body: {
          query,
          variables: {
            token,
            phone,
            sms_code: smsCode
          }
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
      });
    });
  });
  it('should execute the GraphQL mutation with the loaded signup_id', () => {
    cy.fixture('responseId.json').then((data) => {
      const signupId = data.id;

      const mutation = `
      mutation (
        $signup_id: ID!,
        $company_name: String!,
        $company_type: String!,
        $company_role: String!,
        $country_of_incorporation: String!,
        $company_registration_number: String,
        $vat_number: String,
        $company_website: String,
        $average_monthly_purchase: String,
        $first_touch: String,
        $referrer: String,
        $rapnet_id: String,
        $jbt_number: String,
        $utm_source: String,
        $utm_medium: String,
        $utm_campaign: String,
        $utm_term: String,
        $utm_content: String
      ) {
        initialize_signup_step_two(
          signup_id: $signup_id
          company_name: $company_name
          company_type: $company_type
          company_role: $company_role
          country_of_incorporation: $country_of_incorporation
          vat_number: $vat_number
          company_registration_number: $company_registration_number
          company_website: $company_website
          average_monthly_purchase: $average_monthly_purchase
          first_touch: $first_touch
          referrer: $referrer
          rapnet_id: $rapnet_id
          jbt_number: $jbt_number
          utm_source: $utm_source
          utm_medium: $utm_medium
          utm_campaign: $utm_campaign
          utm_term: $utm_term
          utm_content: $utm_content
        ) {
          token
          expires
          refresh_token
          user {
            id
            steps_required
            role
            country
            __typename
          }
          __typename
        }
      }
    `;

      const variables = {
        signup_id: signupId,
        company_name: 'diamoworld',
        company_type: 'Designer',
        company_role: 'Owner / CEO',
        country_of_incorporation: 'PK',
        company_registration_number: '',
        vat_number: '',
        company_website: '',
        average_monthly_purchase: null,
        first_touch: '',
        referrer: '',
        rapnet_id: null,
        jbt_number: null,
        utm_source: null,
        utm_medium: null,
        utm_campaign: null,
        utm_term: null,
        utm_content: null
      };
      const apiUrl = Cypress.env('apiurl');

      cy.request({
        method: 'POST',
        url: apiUrl,
        body: {
          query: mutation,
          variables: variables
        },
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        const token = response.body.data.initialize_signup_step_two.token;
        expect(token).to.exist;

        // Save the token to a JSON file
        cy.writeFile('cypress/fixtures/token.json', { token });
      });
    });
  });

  it('should update user addresses using the GraphQL mutation', () => {
    cy.fixture('responseId.json').then((data) => {
      const signupId = data.id;
      const userId = signupId;

      // Load the token from the token.json file
      cy.fixture('token.json').then((tokenData) => {
        const token = tokenData.token; // Access the token from the loaded file

        const mutation = `
        mutation ($token: String!, $user_registered_address: LocationInput!, $user_shipping_address: LocationInput, $same_address: Boolean, $user_id: ID!) {
          as(token: $token){
            set_address(
              user_registered_address: $user_registered_address
              user_shipping_address: $user_shipping_address
              same_address: $same_address
              user_id: $user_id
            ) {
              token
              expires
              refresh_token
              user {
                ...setup_user
                __typename
              }
              __typename
            }
          }
        }

        fragment setup_user on User {
          id
          steps_required
          email
          role
          country
          company {
            id
            name
            __typename
          }
          __typename
        }
      `;

        const variables = {
          token: token,
          user_registered_address: {
            name: 'Registered address',
            country: 'PK',
            city: 'Lahore',
            state: 'Punjab',
            address1: 'Lahore area',
            address2: '',
            postalCode: '2102',
            suburb: ''
          },
          user_shipping_address: {
            name: 'Shipping address',
            country: 'PK',
            city: 'Lahore',
            state: 'Punjab',
            address1: 'lahore area',
            address2: '',
            postalCode: '21202',
            suburb: ''
          },
          same_address: true,
          user_id: userId
        };
        const apiUrl = Cypress.env('apiurl');

        cy.request({
          method: 'POST',
          url: apiUrl,
          body: {
            query: mutation,
            variables: variables
          },
          headers: {
            'Content-Type': 'application/json'
          }
        }).then((response) => {
          expect(response.status).to.eq(200);
          console.log(JSON.stringify(response.body));
          const email = response.body.data.as.set_address.user.email;
          const emailArray = [{ email: email }];
          cy.writeFile('cypress/fixtures/apiemail.json', emailArray);
        });
      });
    });
  });
  it('Login To Nivoda As A Customer', () => {
    const login = new Login();

    login.visitPage();
    login.loginUsingUi('apiemail.json');
    login.loginAsCustomerAssertion();
  });
});
