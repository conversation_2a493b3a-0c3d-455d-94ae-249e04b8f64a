import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyVideoLinkButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Video Link Button Is Visible For Diamonds', () => {
    product.searchStone('cypress/fixtures/certimage.json');
    sharingOptions.verifyCopyVideoLinkButton();
  });
  it('Verify Video Link Button Is Visible For Lab Grown Diamonds', () => {
    navBar.visitLabgrownDiamonds();
    product.searchStone('cypress/fixtures/labgrowncertimage.json');
    sharingOptions.verifyCopyVideoLinkButton();
  });

  it('Verify Video Link Button Is Visible For Gemstones', () => {
    navBar.visitGemStones();
    sharingOptions.verifyCopyVideoLinkButton();
  });
});
