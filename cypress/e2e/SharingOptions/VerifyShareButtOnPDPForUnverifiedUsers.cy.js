import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyShareButtonInPDPForUnverifiedUsers', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('nonverifiedusers.json');
  });

  it('Verify Share Button Is Not Visible For Diamonds in PDP View For Unverified Users (NE-TC-2101)', () => {
    sharingOptions.verifyShareButtonNotVisibleInProduct();
  });

  it('Verify Share Button Is Not Visible For Lab Grown Diamonds in PDP View Unverified Users (NE-TC-2102)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyShareButtonNotVisibleInProduct();
  });

  it.skip('Verify Share Button Is Visible For Gemstones in PDP View Unverified Users (NE-TC-2100)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyShareButtonNotVisibleInProduct();
  });
});
