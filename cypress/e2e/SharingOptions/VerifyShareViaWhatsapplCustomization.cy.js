import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';
import Settings from '../../support/Settings';

describe('VerifyWhatsAppShareButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();
  const settings = new Settings();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
    settings.updateLanguageandVerify('English', 'Natural diamonds');
  });

  it('Verify Cust Share Via Whatsapp Is Visible For Natural Diamonds (NE-TC-3240)', () => {
    sharingOptions.verifyWhatsappSharingOption();
    sharingOptions.verifyWhatsappCust();
  });

  it('Verify Cust Share Via Whatsapp Is Visible For Lab Grown Diamonds (NE-TC-3241)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyWhatsappSharingOption();
    sharingOptions.verifyWhatsappCust();
  });

  it('Verify Cust Share Via Whatsapp Is Visible For Gemstones (NE-TC-3242)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyWhatsappSharingOption();
    sharingOptions.verifyWhatsappCust();
  });
});
