import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyCopyInfoButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingUniqueUsersApi();
  });

  it('Verify Share Via Copy Info Button Is Visible For Diamonds (NE-TC-1926)', () => {
    sharingOptions.verifycopyinfoButton();
  });

  it('Verify Share Via Copy Info Button Is Visible For Lab Grown Diamonds (NE-TC-1933)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifycopyinfoButton();
  });

  it('Verify Share Via Copy Info Button Is Not Visible For Gemstones (NE-TC-2342)', () => {
    navBar.visitGemStones();
    sharingOptions.verifycopyinfoButtonNotVisibleForGemstone();
  });
});
