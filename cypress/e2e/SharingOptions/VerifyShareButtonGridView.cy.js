import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyShareButtonInGridView', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingUniqueUsersApi();
  });

  it('Verify Share Button Is Visible For Diamonds in Grid View (NE-TC-1919)', () => {
    sharingOptions.verifyShareButton();
  });

  it('Verify Share Button Is Visible For Lab Grown Diamonds in Grid View (NE-TC-1924)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyShareButton();
  });

  it('Verify Share Button Is Visible For Gemstones in Grid View (NE-TC-2339)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyShareButton();
  });

  it('Verify Share Button Is Not Visible For Natural melee in Grid View (NE-TC-2346)', () => {
    navBar.visitNaturalMelee();
    sharingOptions.verifyShareButtonNotVisibleForMelee();
  });

  it('Verify Share Button Is Not Visible For Labgrown Natural melee in Grid View (NE-TC-2349)', () => {
    navBar.visitLabGrownMelee();
    sharingOptions.verifyShareButtonNotVisibleForMelee();
  });
});
