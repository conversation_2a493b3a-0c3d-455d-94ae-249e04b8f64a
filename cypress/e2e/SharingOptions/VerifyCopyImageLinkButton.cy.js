import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';
import SharingOptions from '../../support/SharingOptions';

describe('VerifycopyImageLinkButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Copy Image Link Button For Diamonds (NE-TC-1927)', () => {
    product.searchStone('cypress/fixtures/certimage.json');
    sharingOptions.verifyCopyImageLinkButton();
  });
  it('Verify Copy Image Link Button For Lab Grown Diamonds (NE-TC-1931)', () => {
    navBar.visitLabgrownDiamonds();
    product.searchStone('cypress/fixtures/labgrowncertimage.json');
    sharingOptions.verifyCopyImageLinkButton();
  });

  it('Verify Copy Image Link Button For Gemstones (NE-TC-2337)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyCopyImageLinkButton();
  });
});
