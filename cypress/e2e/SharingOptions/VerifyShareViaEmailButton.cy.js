import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyEmailShareButton', { tags: ['@Sharing-Options', '@Regression'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Share Via Email Is Visible For Diamonds (NE-TC-1922)', () => {
    sharingOptions.verifyemailSharingOption();
  });

  it('Verify Share Via Email Is Visible For Lab Grown Diamonds (NE-TC-1934)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyemailSharingOption();
  });

  it('Verify Share Via Email Is Visible For Gemstones (NE-TC-2296)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyemailSharingOption();
  });
});
