import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyEmailShareButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Customisation Share Via Email Is Visible For Natural Diamonds (NE-TC-3244)', () => {
    sharingOptions.verifyemailSharingOption();
    sharingOptions.verifyemailCust();
  });

  it('Verify Share Via Email Is Visible For Lab Grown Diamonds (NE-TC-3245)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyemailSharingOption();
    sharingOptions.verifyemailCust();
  });

  it('Verify Share Via Email Is Visible For Gemstones (NE-TC-2298)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyemailSharingOption();
    sharingOptions.verifyemailCust();
  });
});
