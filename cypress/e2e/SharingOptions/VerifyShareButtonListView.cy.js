import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyShareButtonInListView', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingUniqueUsersApi();
  });

  it('Verify Share Button Is Visible For Diamonds in List View (NE-TC-1921)', () => {
    sharingOptions.verifyShareButtonInListView();
  });

  it('Verify Share Button Is Visible For Lab Grown Diamonds in List View (NE-TC-1925)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyShareButtonInListView();
  });

  it('Verify Share Button Is Not Visible For Gemstones in List View (NE-TC-2340)', { tags: '@LastFailed' }, () => {
    navBar.visitGemStones();
    sharingOptions.verifyShareButtonInListViewNotVisibleForGemstone();
  });

  it('Verify Share Button Is Not Visible For Natural melee in List View (NE-TC-2347)', () => {
    navBar.visitNaturalMelee();
    sharingOptions.verifyShareButtonInListViewNotExistForMelee();
  });

  it('Verify Share Button Is Not Visible For Labgrown Natural melee in List View (NE-TC-2348)', () => {
    navBar.visitLabGrownMelee();
    sharingOptions.verifyShareButtonInListViewNotExistForMelee();
  });
});
