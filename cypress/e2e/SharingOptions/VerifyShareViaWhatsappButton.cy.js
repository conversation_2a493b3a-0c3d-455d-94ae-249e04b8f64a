import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyWhatsAppShareButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
  });

  it('Verify Share Via Whatsapp Is Visible For Diamonds (NE-TC-1923)', () => {
    sharingOptions.verifyWhatsappSharingOption();
  });

  it('Verify Share Via Whatsapp Is Visible For Lab Grown Diamonds (NE-TC-1935)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyWhatsappSharingOption();
  });

  it('Verify Share Via Whatsapp Is Visible For Gemstones (NE-TC-2341)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyWhatsappSharingOption();
  });
});
