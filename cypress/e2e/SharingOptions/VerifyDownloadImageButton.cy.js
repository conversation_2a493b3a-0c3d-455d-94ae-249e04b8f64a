import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyImageDownloadButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Image Download Button For Diamonds (NE-TC-2344)', () => {
    product.searchStone('cypress/fixtures/certimage.json');
    sharingOptions.verifyDownloadImageButton();
  });
  it('Verify Image Download Button For Lab Grown Diamonds (NE-TC-1928)', () => {
    navBar.visitLabgrownDiamonds();
    product.searchStone('cypress/fixtures/labgrowncertimage.json');
    sharingOptions.verifyDownloadImageButton();
  });

  it('Verify Image Dowbload Button For Gemstones (NE-TC-2335)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyDownloadImageButton();
  });
});
