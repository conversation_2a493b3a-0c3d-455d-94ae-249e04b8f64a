import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyVideoDownloadButton', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify Video Download Button For Diamonds (NE-TC-2345)', () => {
    product.searchStone('certimage.json');
    sharingOptions.verifyDownloadVideoButton();
  });
  it('Verify Video Download Button For Lab Grown Diamonds (NE-TC-1930)', () => {
    navBar.visitLabgrownDiamonds();
    product.searchStone('labgrowncertimage.json');
    sharingOptions.verifyDownloadVideoButton();
  });

  it('Verify Video Download Button For Gemstones (NE-TC-2336)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyDownloadVideoButton();
  });
});
