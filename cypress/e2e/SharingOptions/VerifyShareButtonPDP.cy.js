import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import SharingOptions from '../../support/SharingOptions';

describe('VerifyShareButtonInPDP', { tags: ['@Sharing-Options', '@Regression', '@CX'] }, () => {
  const login = new Login();
  const sharingOptions = new SharingOptions();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingUniqueUsersApi();
  });

  it('Verify Share Button Is Visible For Diamonds in PDP View (NE-TC-1920)', () => {
    sharingOptions.verifyShareButtonInProduct();
  });

  it('Verify Share Button Is Visible For Lab Grown Diamonds in PDP View (NE-TC-1936)', () => {
    navBar.visitLabgrownDiamonds();
    sharingOptions.verifyShareButtonInProduct();
  });

  it('Verify Share Button Is Visible For Gemstones in PDP View (NE-TC-2343)', () => {
    navBar.visitGemStones();
    sharingOptions.verifyShareButtonInProduct();
  });
});
