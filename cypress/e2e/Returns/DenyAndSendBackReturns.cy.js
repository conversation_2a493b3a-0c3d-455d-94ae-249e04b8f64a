import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';
import Returns from '../../support/Admin/Returns';

describe('DenyReturnAndSendBack', { tags: ['@OTP'] }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();
  const returns = new Returns();

  it('Verify User Can Place an Order', () => {
    cy.getcreditnotestone();
    cy.getMultipleAddressUser();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/creditnotestone.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });
  it('Verify User Can Confirm RTC', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
  });

  it('Verify User Can Pass QC', () => {
    cy.clearAllSessionStorage();
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.accessRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    cy.wait(4000);
    admin.markStoneQcPass('orderNumber.json');
  });

  it('Verify User Can Deny an Initiated Return (NE-TC-2608)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createShipmentAndVerifyAddress(
      'BDB Mumbai Office IN',
      'NYC Office US',
      'cypress/fixtures/multipleaddressuser.json',
      'orderNumber.json'
    );
    accessAdminTabs.accessTabs('Returns', 'Initiate return', '', 'returns/initiate');
    returns.denyAndSendBackReturn(
      'cypress/fixtures/orderNumber.json',
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditnotestone.json'
    );
  });
});
