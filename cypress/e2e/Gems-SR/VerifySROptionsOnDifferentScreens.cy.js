import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import GemsSR from '../../support/Gems-SR';
import Filter from '../../support/Filter';
import Product from '../../support/Product';

describe('VerifyGemsSROptionsOnPLPAndInFilters', { tags: ['@Gems-SR', '@Regression'] }, () => {
  const login = new Login();
  const navbar = new Navbar();
  const gemsSR = new GemsSR();
  const filter = new Filter();
  const product = new Product();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
  });

  it('Verify Gems SR options is displayed on PLP (NE-TC-2489)', () => {
    gemsSR.verifySpecialRequestBannerAndModal();
    gemsSR.closeSRModal();
  });

  it('Verify Gems SR options is displayed in filters (NE-TC-2584)', () => {
    filter.openMainFilter();
    gemsSR.verifySpecialRequestBannerAndModal();
    gemsSR.closeSRModal();
  });

  it('Verify Gems SR options is displayed for not available stockID (NE-TC-2585)', () => {
    product.searchNotAvailableGemStone();
    product.makeARequest();
    gemsSR.verifyRequestModalIsVisible();
    gemsSR.closeSRModal();
  });
});
