import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import GemsSR from '../../support/Gems-SR';

describe('VerifyGemsSRRequestFromPLP', { tags: ['@Gems-SR', '@Regression'] }, () => {
  const login = new Login();
  const navbar = new Navbar();
  const gemsSR = new GemsSR();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
  });

  it('Verify Gems SR request can be placed (NE-TC-2490)', () => {
    gemsSR.verifySpecialRequestBannerAndModal();
    gemsSR.gemsSRFormStep1Assertions();
    gemsSR.gemsSRFormStep2Assertions();
    gemsSR.gemsSpecialRequestSubmission();
  });
});
