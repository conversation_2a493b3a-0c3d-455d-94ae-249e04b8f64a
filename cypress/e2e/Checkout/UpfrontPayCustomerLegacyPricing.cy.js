import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('UpfrontPayCustomerLegacyPricing', { tags: ['@CustomerCheckout', '@Regression'] }, () => {
  it('Verify Checkout Flow For Upfront Legacy Pricing Customer (NE-TC-957)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();

    cy.getdiamondshortlistcerts();

    login.loginUsingApi('upfrontlegacyuser.json');
    product.addProductToCart('cypress/fixtures/diamondshortlistcerts.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyPaymentMethod('Upfront payment selected');
    checkout.assertAccountLimitBarNotVisible();
    checkout.assertDiscountPill('FREE');
  });
});
