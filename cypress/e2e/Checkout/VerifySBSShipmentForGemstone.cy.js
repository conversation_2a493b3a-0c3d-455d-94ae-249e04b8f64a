import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';
import Navbar from '../../support/Navbar';

describe('Verify SBS Shipment for Gemstone is Created Successfully', { tags: '@OTP' }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();
  const navbar = new Navbar();

  it('Verify User Can Create A Gemstone Order', () => {
    cy.getcreditNoteGems();
    cy.getHKUser();

    login.loginUsingApi('getHKuser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/creditNoteGems.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });

  it('Verify User Can Confirm The Order (NE-TC-2600)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Gemstone',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'Gemstone',
      'cypress/fixtures/creditNoteGems.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.selectStone('Gemstone');
    admin.markGemStoneQcPass('cypress/fixtures/orderNumber.json');
  });

  it('Verify User Can Create An SBS Shipment For Gemstone (NE-TC-2602)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createSbsShipment('BDB Mumbai Office IN', 'HK Office HK', 'orderNumber.json');
  });
});
