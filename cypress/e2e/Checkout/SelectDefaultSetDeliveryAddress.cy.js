import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('Select Default Set Delivery Address', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify Address Is Selected By Default (NE-TC-700)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const navbar = new Navbar();

    cy.getgemsdata1();

    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/gemsdata1.json');
    product.viewCart();
    checkout.proceedToDeliveryOptions();
    checkout.defaultAddressSelectionAssertion();
    checkout.placeOrder();
  });
});
