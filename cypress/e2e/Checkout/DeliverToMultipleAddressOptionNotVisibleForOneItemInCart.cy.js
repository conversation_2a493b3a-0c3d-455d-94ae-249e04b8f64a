import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe(
  'Deliver To Multiple Address Option Not Visible For One Item In Cart',
  { tags: ['@CustomerCheckout', '@Regression'] },
  () => {
    it('Verify Deliver To Multiple Address Option Is Not Visible Incase 1 Stone In Cart (NE-TC-699)', () => {
      const login = new Login();
      const product = new Product();
      const checkout = new Checkout();
      const navbar = new Navbar();

      cy.getgemsshortlistcert();

      login.loginUsingUniqueUsersApi();
      navbar.visitGemStones();
      product.addProductToCart('cypress/fixtures/gemsshortlistcert.json');
      product.viewCart();
      checkout.proceedToDeliveryOptions();
      checkout.deliverToMultipleAddressNotVisible();
    });
  }
);
