import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('VerifyIncentivePayOrderAIP', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify Checkout Flow And Other Details For AIP Customer (NE-TC-961)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getincentivepayaipuser();
    cy.getincentivepayorderaip();

    login.loginUsingApi('incentivepayaipuser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/incentivepayorderaip.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyPaymentMethod('Pay in 3 days');
    checkout.verifyAccountLimitBarDetails('incentivepayaipuser.json');
    checkout.assertDiscountPill('-1.25%');
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.verifyConfirmationPageDetails('You have 3 days to pay the invoice', 'Pay in 3 days');
  });
});
