import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('ContactUsOpensLiveChat', { tags: ['@CustomerCheckout', '@Smoke'] }, () => {
  it('Verify Clicking Contact Us Opens Live Chat On Checkout Page (NE-TC-963)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const navbar = new Navbar();

    cy.getincentivepayaipuser();
    cy.getgemsshortlistcert();
    login.loginUsingApi('incentivepayaipuser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/gemsshortlistcert.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyContactUsOpensLiveChat();
  });
});
