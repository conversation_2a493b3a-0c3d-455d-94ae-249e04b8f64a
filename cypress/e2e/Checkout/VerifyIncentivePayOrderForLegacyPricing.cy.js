import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('VerifyIncentivePayOrderLegacyPricing', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify Checkout Flow And Other Details For Incentive Pay Legacy Pricing Customer (NE-TC-960)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getshortlistlabdiamond();
    cy.getincentivepaylegacyuser();

    login.loginUsingApi('incentivepaylegacyuser.json');
    navbar.visitLabgrownDiamonds();
    product.addProductToCart('cypress/fixtures/shortlistlabdiamond.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyPaymentMethod('Pay in 3 days');
    checkout.verifyAccountLimitBarDetails('incentivepaylegacyuser.json');
    checkout.assertDiscountPill('FREE');
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.verifyConfirmationPageDetails('You have 3 days to pay the invoice', 'Pay in 3 days');
  });
});
