import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('Add Gems Nivoda Curated Stones To Cart', { tags: ['@Regression', '@gems'] }, () => {
  it('Verify User Can Add Nivoda Curated Stones To Cart (NE-TC-2592)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getNivodaCuratedStone();
    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.addProductToCart('getNivodaCuratedStone.json');
    product.viewCart();
  });
});
