import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('ValidateReturnOptionOnCheckoutForMelee', { tags: ['@CustomerCheckout', '@Skip'] }, () => {
  it.skip('Validate that Melee Stones Are Not Returnable On Checkout Page', () => {
    const login = new Login();
    const navbar = new Navbar();
    const product = new Product();
    const checkout = new Checkout();

    cy.getmeleeaddtocart();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    product.addMeleeToCart('cypress/fixtures/meleeaddtocart.json');
    product.viewCart();
    checkout.verifyReturnOptionOnCheckoutForMelee();
  });
});
