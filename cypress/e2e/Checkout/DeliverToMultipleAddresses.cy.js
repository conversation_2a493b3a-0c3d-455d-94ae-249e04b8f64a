import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';

describe('Deliver To Multiple Addreses', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it.skip('Verify User Can Deliver To Multiple Products For Multiple Stones (NE-TC-702)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const accessAdminTabs = new AccessAdminTabs();
    const shipment = new Shipment();
    const admin = new Admin();

    cy.getmultipleaddressstone1();
    cy.getmultipleaddressstone2();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/multipleaddressstone1.json');
    product.addProductToCart('cypress/fixtures/multipleaddressstone2.json');
    product.viewCart();
    checkout.proceedToDeliveryOptions();
    checkout.deliverToMultipleAddresses('multipleaddressuser.json');
    checkout.multipleOrderConfirmationText();

    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/multipleaddressstone1.json',
      'cypress/fixtures/creditstoneorderNumber1.json',

      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/multipleaddressstone2.json',
      'cypress/fixtures/creditstoneorderNumber2.json',

      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/multipleaddressstone1.json',
      'cypress/fixtures/creditstoneorderNumber1.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/multipleaddressstone2.json',
      'cypress/fixtures/creditstoneorderNumber2.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.markStoneQcPass('cypress/fixtures/creditstoneorderNumber1.json');
    admin.markStoneQcPass('cypress/fixtures/creditstoneorderNumber2.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createShipmentAndVerifyAddress(
      'BDB Mumbai Office IN',
      'NYC Office US',
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditstoneorderNumber1.json'
    );
  });
});
