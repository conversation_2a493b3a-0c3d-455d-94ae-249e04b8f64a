import Login from '../../support/Login';
import Product from '../../support/Product';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import MeleeDashboard from '../../support/Admin/Melee-Dashboard';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('Verify SBS Shipment for Melee Order', { tags: '@OTP' }, () => {
  const login = new Login();
  const meleeDashboard = new MeleeDashboard();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const navbar = new Navbar();

  it('Verify User Can Create A Natural Melee Order', () => {
    cy.getmeleeaddtocart();
    cy.getHKUser();

    login.loginUsingApi('getHKuser.json');
    navbar.visitNaturalMelee();
    product.addMeleeToCart('meleeaddtocart.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });
  it('Verify User Can Create A Melee SBS Shipment', () => {
    login.loginUsingAdminApi('loginasadmin.json');

    accessAdminTabs.accessTabs(
      'Melee Dashboard',
      'Melee Orders',
      'Purchase order',
      'admin/melee/orders/purchase-order'
    );

    meleeDashboard.searchOrder('orderNumber.json');
    meleeDashboard.viewAndConfirmOrder();

    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createSbsShipment('BDB Mumbai Office IN', 'HK Office HK', 'orderNumber.json');
  });
});
