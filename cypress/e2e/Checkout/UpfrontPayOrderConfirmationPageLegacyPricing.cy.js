import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('UpfrontPayOrderConfirmationPageLegacyPricing', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify Confirmation Page For Upfront Legacy Pricing Customer (NE-TC-958)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getupfrontpayorder();

    login.loginUsingApi('upfrontlegacyuser.json');
    navbar.visitLabgrownDiamonds();
    product.addProductToCart('cypress/fixtures/upfrontpayorder.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyPaymentMethod('Upfront payment selected');
    checkout.assertAccountLimitBarNotVisible();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.verifyConfirmationPageDetails('proforma invoice', 'Upfront payment');
  });
});
