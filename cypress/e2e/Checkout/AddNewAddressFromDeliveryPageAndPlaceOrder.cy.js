import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('Add New Address And Place Order', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify User Can Add A New Address And Place Order (NE-TC-701)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const navbar = new Navbar();

    cy.clearAllLocalStorage();

    cy.getgemsdata();
    login.loginUsingUniqueUsersApi();
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/gemsdata.json');
    product.viewCart();
    checkout.proceedToDeliveryOptions();
    checkout.addDeliveryAddress();
    checkout.proceedToPaymentOptions();
    checkout.verifyAddressInStoneDetails();
    checkout.placeOrder();
  });
});
