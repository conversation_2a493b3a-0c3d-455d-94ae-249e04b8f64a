import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Menu from '../../support/Menu';
import Navbar from '../../support/Navbar';

describe('VerifyUpfrontPayOrderAIP', { tags: ['@CustomerCheckout', '@OTP'] }, () => {
  it('Verify Checkout Flow And Other Details For AIP Customer (NE-TC-959)', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const menu = new Menu();
    const navbar = new Navbar();

    cy.getgemsupfrontdata();

    login.loginUsingApi('upfrontaipuser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/gemsupfrontdata.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyPaymentMethod('Upfront payment selected');
    checkout.assertAccountLimitBarNotVisible();
    checkout.assertDiscountPill('-1.25%');
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.verifyConfirmationPageDetails('proforma invoice', 'Upfront payment');
  });
});
