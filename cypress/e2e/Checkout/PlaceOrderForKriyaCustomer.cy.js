import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Navbar from '../../support/Navbar';

describe('Place Order For Kriya Customer', { tags: '@OTP' }, () => {
  it('Verify Kriya User Can Place An Order (NE-TC-2620) ', () => {
    const login = new Login();
    const product = new Product();
    const checkout = new Checkout();
    const navbar = new Navbar();

    cy.getKriyaCustomer();
    cy.getgemsupfrontdata();

    login.loginUsingApi('kriyaUser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/gemsupfrontdata.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.verifyKriyaPaymentMethod('Pay in 30 days');
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
  });
});
