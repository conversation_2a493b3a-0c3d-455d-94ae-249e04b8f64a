import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';

describe('Verify SBS Shipment is Created Successfully For Express Stones (NE-TC-2334)', { tags: '@OTP' }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();

  it.skip('Verify User Can Create A Express Diamond Order', () => {
    cy.getnivodaexpressstone();
    cy.getHKUser();

    login.loginUsingApi('getHKuser.json');
    product.addProductToCart('cypress/fixtures/nivodaexpressstone.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });

  it.skip('Verify User Can Confirm The Order', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/nivodaexpressstone.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessExpressRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/nivodaexpressstone.json',
      'cypress/fixtures/orderNumber.json',
      null,
      null,
      true
    );
    cy.wait(4000);
  });

  it.skip('Verify User Can Create A Shipment', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createSbsShipment('BDB Mumbai Office IN', 'HK Office HK', 'orderNumber.json');
  });
});
