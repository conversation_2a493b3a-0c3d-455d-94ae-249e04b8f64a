import WiseSourcing from '../../support/WISE/Wise-Sourcing';
import WiseFulfillment from '../../support/WISE/Wise-Fulfillment.js';

describe('WISE E2E Flow', () => {
  it('WISE E2E FLOW', { tags: ['@wise'] }, () => {
    const sourcing = new WiseSourcing();
    const fulfillment = new WiseFulfillment();

    sourcing.visitPage();
    sourcing.loginUsingUi('loginasadmin.json');
    sourcing.createWiseRequest();
    fulfillment.visitFulfillmentPage();
    fulfillment.searchWiseRequest();
    fulfillment.tagAndCollect();
    fulfillment.gradeWiseStonesFirstStep();
    fulfillment.gradeWiseStonesSecondStep();

    cy.setupGradingFilesFetcherService();

    fulfillment.gradeWiseStonesThirdStep();
  });
});
