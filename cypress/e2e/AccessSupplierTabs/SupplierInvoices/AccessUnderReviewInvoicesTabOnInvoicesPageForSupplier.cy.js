import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Invoices from '../../../support/Invoices';

describe('AccessUnderReviewInvoicesTabOnInvoicesPageForSupplier', () => {
  it('Verify Supplier Can Access Under Review Tab On Invoices Page (NE-TC-1248)', { tags: '@Production' }, () => {
    const login = new Login();
    const menu = new Menu();
    const invoices = new Invoices();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Invoices', 'invoices/all-invoices');
    invoices.accessStatusTab('Under review', 'invoices/under-review');
  });
});
