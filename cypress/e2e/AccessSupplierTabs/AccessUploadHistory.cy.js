import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Access Upload history', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should redirect to upload history (NE-TC-2438)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    supplier.uploadHistory();
  });
});
