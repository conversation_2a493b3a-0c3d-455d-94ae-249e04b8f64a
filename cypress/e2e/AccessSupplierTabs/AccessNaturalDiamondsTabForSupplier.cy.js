import Login from '../../support/Login';
import Menu from '../../support/Menu';

describe('AccessNaturalDiamondsTabForASupplier', () => {
  it('Verify Supplier Can Access Natural Diamonds Tab (NE-TC-1255)', { tags: '@Production' }, () => {
    const login = new Login();
    const menu = new Menu();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
  });
});
