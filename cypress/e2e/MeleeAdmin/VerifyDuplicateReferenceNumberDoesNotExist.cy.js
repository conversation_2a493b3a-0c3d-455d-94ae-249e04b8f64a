import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import MeleeDashboard from '../../support/Admin/Melee-Dashboard';

describe('VerifyDuplicateReferenceNumbersDoesNotExist', { tags: ['@Melee-Admin', '@Regression'] }, () => {
  it('Verify Duplicate Reference Numbers Does Not Exist (NE-TC-3332)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const meleeDashboard = new MeleeDashboard();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Melee Dashboard',
      'Melee Inventory',
      'Reference Report',
      'admin/melee/page/reference-report'
    );

    meleeDashboard.applySupplierFilterReferenceReport('Carboreal xxxx');
    meleeDashboard.selectReferenceReport('-');
    meleeDashboard.sendReferenceReport();
    meleeDashboard.checkDuplicateReferenceNumber();
  });
});
