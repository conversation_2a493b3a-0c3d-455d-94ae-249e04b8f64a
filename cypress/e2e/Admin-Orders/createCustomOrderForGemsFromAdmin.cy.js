import Login from './../../support/Login';
import AccessAdminTabs from './../../support/Admin/AccessAdminTabs';
import AdminOrders from './../../support/Admin/Admin-Orders';

describe('CreateCustomOrderForGemsFromAdmin', () => {
  it('Verify User Can Create Custom Order For Gems From Admin (NE-TC-2487)', { tags: '@Smoke' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const adminOrders = new AdminOrders();

    cy.getgemsdata();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('', 'Create order', '', 'orders/create');
    adminOrders.createCustomOrder('Gemstone', 'upfrontaipuser.json', 'gemsdata.json');
  });
});
