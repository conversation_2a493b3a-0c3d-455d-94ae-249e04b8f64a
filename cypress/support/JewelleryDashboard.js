class JewellerySKU {
  constructor() {
    this.searchInput = '#search_in_jewellery';
    this.searchButton = '[data-automation-id="action--btn"]';
    this.tableRows = '#table_rows';
    this.tableRow = '.table__row';
    this.tableColumns = '[data-automation-id="table-column"]';
    this.stockdetailsdropdown = '.css-cp5063';
    this.selectOption = 'div[id^="react-select-"]';
    this.option = 'div[role="option"]';
    this.addstonesbtns = 'button.add_stone_btn';
    this.jewType = '[data-automation-id="create-jew-type"]';
    this.uploadImagesbtn = '[type="button"]';
    this.qcRequirementsandcomments = 'div.text_area_wrapper textarea.note_textarea';
    this.createJewellerySKUbtn = '[type="submit"]';
    this.successMessage = '._title';
    this.updateJewellerysku = '[name="update"]';
    this.searchforCompanyName = '[data-automation-id="company-name"]';
    this.selectCompany = '[data-automation-id="select-company"]';
    this.jewellerySKUnumber = '[data-automation-id="Enter Jewellery SKU No"]';
    this.jewellerySKUoption = 'ul.search_result_options li';
    this.grossweight = '[data-automation-id="metal-gross-weight"]';
    this.netweight = '[data-automation-id="metal-net-weight"]';
    this.ordersdropdowns = '.css-5h3scw';
    this.estimatedLabourPrice = '[data-automation-id="metal-total-labour-value"]';
    this.estimatedMetalPrice = '[data-automation-id="metal-approx-unit-rate"]';
    this.addDiamondbtn = '[data-automation-id="create_order__action--add-stone-Diamond"]';
    this.addGemstonebtn = '[data-automation-id="create_order__action--add-stone-Gemstone"]';
    this.addMaleebtn = '[data-automation-id="create_order__action--add-stone-Melee"]';
    this.diamondCarats = '[data-automation-id="Diamond-carats"]';
    this.diamondpieces = '[data-automation-id="Diamond-pcs"]';
    this.diamondlength = '[data-automation-id="Diamond-length"]';
    this.diamondwidth = '[data-automation-id="Diamond-width"]';
    this.diamonddepth = '[data-automation-id="Diamond-depth"]';
    this.diamindnivodaprice = '[data-automation-id="Diamond-nivoda-quoted-price-ct"]';
    this.diamondsupplierquotedprice = '[data-automation-id="Diamond-supplier-quoted-price-ct"]';
    this.diamondsupplierfinalprice = '[data-automation-id="Diamond-supplier-quoted-price-ct"]';
    this.gemstoneCarats = '[data-automation-id="Gemstone-carats"]';
    this.gemstonePieces = '[data-automation-id="Gemstone-pcs"]';
    this.gemstoneLength = '[data-automation-id="Gemstone-length"]';
    this.gemstoneWidth = '[data-automation-id="Gemstone-width"]';
    this.gemstoneDepth = '[data-automation-id="Gemstone-depth"]';
    this.gemstoneNivodaPrice = '[data-automation-id="Gemstone-nivoda-quoted-price-ct"]';
    this.gemstoneSupplierQuotedPrice = '[data-automation-id="Gemstone-supplier-quoted-price-ct"]';
    this.gemstoneSupplierFinalPrice = '[data-automation-id="Gemstone-supplier-quoted-price-ct"]';
    this.maleeCarats = '[data-automation-id="Melee-carats"]';
    this.maleePieces = '[data-automation-id="Melee-pcs"]';
    this.maleeNivodaPrice = '[data-automation-id="Melee-nivoda-quoted-price-ct"]';
    this.maleeSupplierQuotedPrice = '[data-automation-id="Melee-supplier-quoted-price-ct"]';
    this.maleeSupplierFinalPrice = '[data-automation-id="Melee-supplier-quoted-price-ct"]';
    this.estimatedJewelleryValue = '[data-automation-id="ESTIMATED-price-section"]';
    this.createOrderbtn = '[data-automation-id="create-order-btn"]';
  }
  searchSKU(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((jewelleryData) => {
      const sku = jewelleryData[0]?.sku;
      if (sku) {
        cy.get(this.searchInput, { timeout: 10000 }).should('be.enabled').clear().type(`${sku}{enter}`);
      } else {
        throw new Error('SKU is not available in the fixture');
      }
      cy.get(this.searchButton).click;
      cy.get(this.tableRows, { timeout: 150000 })
        .find(this.tableRow, { timeout: 150000 })
        .should('have.length.greaterThan', 0);
      cy.get(this.tableRows, { timeout: 150000 })
        .find(this.tableRow, { timeout: 150000 })
        .contains(sku, { timeout: 150000 })
        .should('exist');
    });
  }
  selectDropdownOptionSKU(index, text) {
    cy.get(this.stockdetailsdropdown).eq(index).click();
    cy.get(this.selectOption).find(this.option).contains(text).click();
  }
  createSKU() {
    this.selectDropdownOptionSKU(5, '10 KT');
    this.selectDropdownOptionSKU(6, 'Yellow Gold');
    this.selectDropdownOptionSKU(7, '10 KT');
    this.selectDropdownOptionSKU(9, 'G');
    cy.get(this.addstonesbtns).contains('Add Diamond').scrollIntoView().click();
    this.selectDropdownOptionSKU(16, '1.1ct');
    cy.get(this.addstonesbtns).contains('Add Gemstone').scrollIntoView().click();
    this.selectDropdownOptionSKU(25, '4');
    cy.get(this.addstonesbtns).contains('Add Melee').scrollIntoView().click();
    cy.get(this.jewType, { timeout: 10000 }).eq(4).type('1.1');
    cy.get(this.jewType, { timeout: 10000 }).eq(5).type('1.5');
    cy.get(this.jewType, { timeout: 10000 }).eq(6).type('2');
    cy.get(this.jewType, { timeout: 10000 }).eq(7).type('5');
    cy.get(this.uploadImagesbtn).contains('Upload images').click();
    cy.get('input[type="file"]').attachFile('ring.jpg');
    this.selectDropdownOptionSKU(31, 'SDC jewelxxxx');
    cy.get(this.qcRequirementsandcomments, { timeout: 10000 })
      .eq(0)
      .should('be.enabled')
      .clear()
      .type('these are the automated requirements');
    cy.get(this.qcRequirementsandcomments, { timeout: 10000 })
      .eq(1)
      .should('be.enabled')
      .clear()
      .type('these are the automated comments');
    cy.get(this.createJewellerySKUbtn).click();
    cy.get(this.successMessage).contains('Created SKU successfully').should('be.visible');
  }

  editSKU(fixtureFileName) {
    this.searchSKU(fixtureFileName);
    cy.get(this.tableColumns).eq(11).click();
    cy.get(this.qcRequirementsandcomments, { timeout: 10000 })
      .eq(0)
      .should('be.enabled')
      .clear()
      .type('these are the editied requirements');
    cy.get(this.qcRequirementsandcomments, { timeout: 10000 })
      .eq(1)
      .should('be.enabled')
      .clear()
      .type('these are the edited comments');

    cy.get(this.updateJewellerysku).scrollIntoView().click();

    cy.get(this.updateJewellerysku).scrollIntoView().click();
  }

  selectDropdownOptionOrder(index, text) {
    cy.get(this.ordersdropdowns).eq(index).click();
    cy.get(this.selectOption).find(this.option).contains(text).click();
  }

  createOrder(fixtureFileName) {
    cy.get(this.searchforCompanyName).type('sdc');
    cy.get('body').click();
    cy.get(this.selectCompany, { timeout: 99000 }).should('be.visible').and('not.be.disabled').select('SDC Desxxxx');

    cy.readFixtureFile(fixtureFileName).then((jewelleryData) => {
      const sku = jewelleryData[0]?.sku;
      if (sku) {
        cy.get(this.jewellerySKUnumber, { timeout: 10000 }).should('be.enabled').clear().type(`${sku}{enter}`);
      } else {
        throw new Error('SKU is not available in the fixture');
      }
      cy.contains(this.jewellerySKUoption, `${sku}`).click();
    });
    cy.wait(2000);

    cy.get(this.grossweight, { timeout: 10000 }).type('10');
    cy.get(this.netweight, { timeout: 10000 }).type('12');
    this.selectDropdownOptionOrder(0, 'French set');
    this.selectDropdownOptionOrder(1, 'Rose Gold');
    this.selectDropdownOptionOrder(2, '14 KT');

    cy.get(this.estimatedLabourPrice, { timeout: 10000 }).type('100');
    cy.get(this.estimatedMetalPrice, { timeout: 10000 }).type('150');

    cy.get('body').then(($body) => {
      if ($body.find(this.ordersdropdowns).eq(4).length === 0) {
        cy.get(this.addDiamondbtn).click();
      }
    });

    this.selectDropdownOptionOrder(4, 'Natural');
    this.selectDropdownOptionOrder(5, 'Bullet');
    this.selectDropdownOptionOrder(6, 'D');
    this.selectDropdownOptionOrder(7, 'VVS1');
    this.selectDropdownOptionOrder(8, 'Good');

    cy.get(this.diamondCarats, { timeout: 10000 }).type('24');
    cy.get(this.diamondpieces, { timeout: 10000 }).type('2');
    cy.get(this.diamondlength, { timeout: 10000 }).type('1');
    cy.get(this.diamondwidth, { timeout: 10000 }).type('2');
    cy.get(this.diamonddepth, { timeout: 10000 }).type('3');
    cy.get(this.diamindnivodaprice, { timeout: 10000 }).type('100');
    cy.get(this.diamondsupplierquotedprice).eq(0).type('120');
    cy.get(this.diamondsupplierquotedprice).eq(1).type('150');
    this.selectDropdownOptionOrder(9, 'Signature');

    cy.get('body').then(($body) => {
      if ($body.find(this.ordersdropdowns).eq(11).length === 0) {
        cy.get(this.addGemstonebtn).click();
      }
    });

    this.selectDropdownOptionOrder(11, 'Ruby');
    this.selectDropdownOptionOrder(11, 'Emerald');
    this.selectDropdownOptionOrder(12, 'Oval');
    this.selectDropdownOptionOrder(13, 'Blue');
    this.selectDropdownOptionOrder(14, 'Slightly included');
    this.selectDropdownOptionOrder(15, 'Brilliant');

    cy.get(this.gemstoneCarats, { timeout: 10000 }).type('22');
    cy.get(this.gemstonePieces, { timeout: 10000 }).type('2');
    cy.get(this.gemstoneLength, { timeout: 10000 }).type('2');
    cy.get(this.gemstoneWidth, { timeout: 10000 }).type('3');
    cy.get(this.gemstoneDepth, { timeout: 10000 }).type('1');
    cy.get(this.gemstoneNivodaPrice, { timeout: 10000 }).type('100');
    cy.get(this.gemstoneSupplierQuotedPrice, { timeout: 10000 }).clear().eq(0).type('120');
    cy.get(this.gemstoneSupplierFinalPrice, { timeout: 10000 }).clear().eq(1).type('150');
    this.selectDropdownOptionOrder(16, 'Side');

    cy.get('body').then(($body) => {
      if ($body.find(this.ordersdropdowns).eq(17).length === 0) {
        cy.get(this.addMaleebtn).click();
      }
    });

    this.selectDropdownOptionOrder(17, 'Lab grown');
    this.selectDropdownOptionOrder(18, 'Baguette');
    this.selectDropdownOptionOrder(19, 'G-H');
    this.selectDropdownOptionOrder(20, 'VS');
    this.selectDropdownOptionOrder(21, 'Very good');
    cy.get(this.maleeCarats, { timeout: 10000 }).clear().type('24');
    cy.get(this.maleePieces, { timeout: 10000 }).clear().type('8');
    this.selectDropdownOptionOrder(22, '0.50 - 0.52');
    cy.get(this.maleeNivodaPrice, { timeout: 10000 }).type('100');
    cy.get(this.maleeSupplierQuotedPrice, { timeout: 10000 }).eq(0).clear().type('120');
    cy.get(this.maleeSupplierFinalPrice, { timeout: 10000 }).eq(1).clear().type('150');
    cy.get(this.estimatedJewelleryValue, { timeout: 10000 }).clear().type('1000');
    cy.get(this.createOrderbtn).click();
    cy.get(this.successMessage).contains('Created Jewellery Order successfully').should('be.visible');
  }

  createOrderwithoutdata(fixtureFileName) {
    cy.get(this.searchforCompanyName).type('sdc');
    cy.get('body').click();
    cy.get(this.selectCompany, { timeout: 99000 }).should('be.visible').and('not.be.disabled').select('SDC Desxxxx');

    cy.readFixtureFile(fixtureFileName).then((jewelleryData) => {
      const sku = jewelleryData[0]?.sku;
      if (sku) {
        cy.get(this.jewellerySKUnumber, { timeout: 10000 }).should('be.enabled').clear().type(`${sku}{enter}`);
      } else {
        throw new Error('SKU is not available in the fixture');
      }
      cy.contains(this.jewellerySKUoption, `${sku}`).click();
    });

    cy.get(this.createOrderbtn).click();
    cy.get(this.successMessage).contains('Created Jewellery Order successfully').should('be.visible');
  }
}

export default JewellerySKU;
