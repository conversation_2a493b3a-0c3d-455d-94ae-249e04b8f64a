Cypress.Commands.add('uploadJewelleryFile', () => {
  const fileName = 'diamondring.jpg';
  const fileUploadedBy = '958ccc53-c242-4f68-b49d-32ddc1d9b107';
  const apiUrl = Cypress.env('jewelleryUploadApi');

  return cy.fixture(fileName, 'binary').then((fileBinary) => {
    const boundary = '----WebKitFormBoundaryWysLvIg4y45aTHVe';
    const body = [
      `--${boundary}`,
      `Content-Disposition: form-data; name="file"; filename="${fileName}"`,
      `Content-Type: image/png`,
      ``,
      fileBinary,
      `--${boundary}`,
      `Content-Disposition: form-data; name="file_uploaded_by"`,
      ``,
      fileUploadedBy,
      `--${boundary}--`,
      ``
    ].join('\r\n');

    return cy
      .api({
        method: 'POST',
        url: apiUrl,
        headers: {
          Authorization: `Bearer ${Cypress.env('authToken')}`,
          'Content-Type': `multipart/form-data; boundary=${boundary}`
        },
        body,
        encoding: 'binary'
      })
      .then((res) => res.body.data); // Return only the data part
  });
});
Cypress.Commands.add('createJewellerySku', () => {
  return cy.uploadJewelleryFile().then((uploaded) => {
    const input = {
      type: 'FINISHED',
      kind: 'BESPOKE',
      CompanyId: 'ee2aa9e8-ece7-473a-b0b3-98eebdcbdfff',
      jewellery_type: 'ENGAGEMENT_RING',
      jewellery_files: [
        {
          file_name: uploaded.file_name,
          url: uploaded.url,
          file_uploaded_by: uploaded.file_uploaded_by,
          type: 'IMAGE'
        }
      ],
      gender: 'WOMAN',
      metal_type: 'WHITE_GOLD',
      karat: 9,
      size_type: 'UK',
      qc_requirements: '',
      comments: '',
      components: [
        {
          component_type: 'Diamond',
          Diamond: {
            ProductType: 'Diamond',
            ProductSubtype: 'NATURAL',
            shape: 'ROUND',
            color: 'D',
            clarity: 'FL',
            cut: 'EIGHTX',
            pieces: 1,
            cert_number: null
          }
        }
      ]
    };

    return cy
      .api({
        method: 'POST',
        url: `${Cypress.env('adminApiUrl')}`,
        headers: {
          Authorization: `Bearer ${Cypress.env('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: {
          operationName: 'adminCreateJewellerySku',
          variables: { input },
          query: `
          mutation adminCreateJewellerySku($input: InputJewellerySKU!) {
            admin_create_jewellery_sku(input: $input) {
              id
              sku
              ...JewelleryExtendedFragment
              __typename
            }
          }

          fragment JewelleryExtendedFragment on Jewellery {
            ...JewelleryFragment
            type
            jewellery_type
            kind
            gender
            karat
            metal_type
            accent_metal_type
            accent_karat
            size
            size_type
            createdAt
            supplier {
              name
              id
              __typename
            }
            jewellery_components {
              ...JewelleryComponentFragment
              __typename
            }
            __typename
          }

          fragment JewelleryFragment on Jewellery {
            id
            sku
            status
            jewellery_type
            comments
            qc_requirements
            jewellery_files {
              id
              type
              url
              file_name
              __typename
            }
            supplier {
              id
              name
              __typename
            }
            description
            sku_supplier
            collection_name_supplier
            supplier_notified
            quote_price
            metal_price
            stones_price
            labour_price
            isFinished
            __typename
          }

          fragment JewelleryComponentFragment on JewelleryComponent {
            id
            ProductType
            ProductSubtype
            color
            cut
            cert_number
            shape
            carats
            clarity
            pieces
            gem_type
            from_mm_range
            to_mm_range
            nivoda_material_quoted_price
            nivoda_material_quoted_price_per_carat
            __typename
          }
        `
        }
      })
      .then((response) => {
        expect(response.status).to.eq(200);
        const jewellery = response.body.data.admin_create_jewellery_sku;
        expect(jewellery).to.have.property('id');
        expect(jewellery).to.have.property('sku');
        return jewellery;
      });
  });
});
Cypress.Commands.add('searchCustomerCompany', (q) => {
  const query = `
    query searchCustomerCompany($q: String!, $type: String!) {
      search_by_company(q: $q, type: $type) {
        items {
          id
          is_faas_company
          name
          company_users {
            id
            firstName
            lastName
            steps_required
            __typename
          }
          OwnerId
          main_location
          company_settings {
            CompanyId
            holds_enabled
            accept_returns
            action_other_user_hold
            memo_enabled
            __typename
          }
          locations {
            id
            name
            city
            country
            default_shipping_address
            last_mile_delivery_fee {
              id
              country_name
              country_code
              last_mile_fee
              local_fee
              __typename
            }
            __typename
          }
          charge_last_mile_fee
          default_credit_enabled
          ip_active
          default_checkout_option
          all_in_pricing
          __typename
        }
        __typename
      }
    }`;

  const variables = {
    q,
    type: 'CUSTOMER'
  };

  return cy
    .request({
      method: 'POST',
      url: `${Cypress.env('adminApiUrl')}`,
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      },
      body: {
        query,
        variables
      }
    })
    .then((res) => {
      expect(res.status).to.eq(200);
      return res.body.data.search_by_company.items;
    });
});

Cypress.Commands.add('getJewelleryOrdersByStatus', (status) => {
  const query = `
    query jewelleryOrdersByStatus($query: JewelleryOrderQuery, $limit: Int, $offset: Int) {
      jewellery_orders_by_status(query: $query, limit: $limit, offset: $offset) {
        total_count
        items {
          id
          sku
          status
          jewellery_type
          description
          quote_price
          order_item {
            id
            order_number
            status
          }
          __typename
        }
        __typename
      }
    }`;

  const variables = {
    query: { status },
    limit: 10,
    offset: 0
  };

  const allowedStatusMap = {
    DELIVERED: ['DELIVERED', 'OUT_FOR_DELIVERY', 'SHIPPED'],
    PROCESSING: [
      'PROCESSING',
      'READY_FOR_DISPATCH',
      'QC_IN_PROCESS',
      'CONFIRMED',
      'ORDER_ACCEPTED',
      'SHIPMENT_IN_PROCESS',
      'SHIPPED',
      'ORDER_IN_PROCESS'
    ],
    CANCELLED: ['CANCELLED'],
    PURCHASE_ORDER: ['PURCHASE_ORDER', 'ORDER_RECEIVED'],
    READY_TO_SHIP: [
      'READY_TO_SHIP',
      'QC_IN_PROCESS',
      'CONFIRMED',
      'SHIPMENT_IN_PROCESS',
      'SHIPPED',
      'OUT_FOR_DELIVERY',
      'ORDER_IN_PROCESS'
    ]
  };

  const allowedStatuses = allowedStatusMap[status] || [status];

  return cy
    .api({
      method: 'POST',
      url: `${Cypress.env('adminApiUrl')}`,
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      },
      body: {
        query,
        variables
      }
    })
    .then((res) => {
      expect(res.status).to.eq(200);
      const items = res.body.data.jewellery_orders_by_status.items;

      items.forEach((item) => {
        expect(allowedStatuses).to.include(item.status);
        if (item.order_item?.status) {
          expect(allowedStatuses).to.include(item.order_item.status);
        }
      });

      return items;
    });
});
Cypress.Commands.add('searchJewellerySku', () => {
  const query = `
    query AdminGetJewellerySKU($query: InputJewellerySKUQuery!, $offset: Int, $limit: Int) {
      admin_get_jewellery_sku(query: $query, offset: $offset, limit: $limit) {
        items {
          id
          ...JewelleryExtendedFragment
          __typename
        }
        total_count
        __typename
      }
    }

    fragment JewelleryExtendedFragment on Jewellery {
      ...JewelleryFragment
      type
      jewellery_type
      kind
      gender
      karat
      metal_type
      accent_metal_type
      accent_karat
      size
      size_type
      createdAt
      supplier {
        name
        id
        __typename
      }
      jewellery_components {
        ...JewelleryComponentFragment
        __typename
      }
      __typename
    }

    fragment JewelleryFragment on Jewellery {
      id
      sku
      status
      jewellery_type
      comments
      qc_requirements
      jewellery_files {
        id
        type
        url
        file_name
        __typename
      }
      supplier {
        id
        name
        __typename
      }
      description
      sku_supplier
      collection_name_supplier
      supplier_notified
      quote_price
      metal_price
      stones_price
      labour_price
      isFinished
      __typename
    }

    fragment JewelleryComponentFragment on JewelleryComponent {
      id
      ProductType
      ProductSubtype
      color
      cut
      cert_number
      shape
      carats
      clarity
      pieces
      gem_type
      from_mm_range
      to_mm_range
      nivoda_material_quoted_price
      nivoda_material_quoted_price_per_carat
      __typename
    }
  `;

  cy.fixture('jewellerySku.json').then((skus) => {
    const sku = skus[0].sku;

    cy.api({
      method: 'POST',
      url: Cypress.env('adminApiUrl'),
      body: {
        query,
        variables: {
          query: { sku },
          offset: 0,
          limit: 10
        }
      },
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      }
    }).then((response) => {
      expect(response.status).to.eq(200);

      if (response.body.errors) {
        throw new Error(`GraphQL error: ${JSON.stringify(response.body.errors)}`);
      }

      const result = response.body.data?.admin_get_jewellery_sku;

      if (!result || !result.items || result.items.length === 0) {
        throw new Error(`No jewellery item found for SKU: ${sku}`);
      }

      const item = result.items[0];
      expect(item.sku).to.eq(sku);
      expect(item.status).to.eq('CREATED');
      expect(result.total_count).to.eq(1);
    });
  });
});

Cypress.Commands.add('searchCertificateByQuery', (queryValue) => {
  const gqlQuery = {
    query: `
      query ($q: String!) {
        search_certificates(q: $q) {
          total_count
          items {
            id
            price
            rapaportDiscount
            supplierStockId
            location { id }
            supplier { id }
            certificate {
              id
              certNumber
              carats
              color
              clarity
              shape
              cut
              lab
              image
              pdfUrl
              length
              width
              depth
            }
            offer {
              id
              price
              discount
            }
          }
        }
      }
    `,
    variables: { q: queryValue }
  };

  return cy
    .api({
      method: 'POST',
      url: `${Cypress.env('adminApiUrl')}`,
      body: gqlQuery,
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      }
    })
    .then((response) => {
      const item = response.body?.data?.search_certificates?.items?.[0];

      if (!item) {
        throw new Error(`No certificate found for query: ${queryValue}`);
      }

      const payload = [
        {
          certificateId: item.certificate.id,
          diamondId: item.id,
          offerId: item.offer?.id,
          locationId: item.location?.id,
          supplierStockId: item.supplierStockId,
          supplierId: item.supplier?.id,
          price: item.offer?.price / 100,
          discount: item.offer?.discount,
          rapaportDiscount: item.rapaportDiscount,
          carats: item.certificate?.carats,
          color: item.certificate?.color,
          clarity: item.certificate?.clarity,
          shape: item.certificate?.shape,
          lab: item.certificate?.lab,
          image: item.certificate?.image,
          certNumber: item.certificate?.certNumber,
          pdfUrl: item.certificate?.pdfUrl,
          cut: item.certificate?.cut,
          length: item.certificate?.length,
          width: item.certificate?.width,
          depth: item.certificate?.depth
        }
      ];
      cy.writeFile(`cypress/fixtures/certificatepayload.json`, payload);
    });
});
Cypress.Commands.add('createJewelleryOrder', () => {
  cy.fixture('certificatepayload.json').then((certData) => {
    const diamond = certData[0];

    cy.fixture('upfrontaipuser.json').then((customerData) => {
      cy.fixture('jewellerySku.json').then((jewelleryData) => {
        const JewellerySku = jewelleryData[0];
        const customerId = customerData[0];
        console.log(diamond.locationId);

        const mutation = `
          mutation admin_create_jewellery_order(
            $customer_id: ID!,
            $location_id: ID!,
            $jewellery_id: ID!,
            $components: InputJewelleryOrderComponents!,
            $quote_price: Float,
            $stones_price: Float,
            $metal_price: Float,
            $labour_price: Float,
            $expected_cost: Float,
            $payment_term_selected: String!
          ) {
            admin_create_jewellery_order(
              customer_id: $customer_id,
              location_id: $location_id,
              jewellery_id: $jewellery_id,
              components: $components,
              quote_price: $quote_price,
              stones_price: $stones_price,
              metal_price: $metal_price,
              labour_price: $labour_price,
              expected_cost: $expected_cost,
              payment_term_selected: $payment_term_selected
            ) {
              items {
                id
                customer_price
                order_number
                diamond {
                  diamondSupplierStockId
                  rapaportDiscount
                  __typename
                }
                status
                request_source
                return_option
                __typename
              }
              __typename
            }
          }
        `;

        const variables = {
          customer_id: customerId.id,
          location_id: diamond.locationId,
          jewellery_id: JewellerySku.id,
          components: {
            mounts: [
              {
                nivoda_labor_quoted_price: 1200,
                nivoda_material_quoted_price: 1400,
                accent_karat: '9',
                accent_metal_type: 'YELLOW_GOLD'
              }
            ],
            diamonds: [
              {
                pieces: 1,
                carats: null,
                nivoda_material_quoted_price_per_carat: certData.price,
                nivoda_material_quoted_price: certData.price,
                color: certData.color,
                cut: certData.cut,
                shape: certData.shape,
                clarity: certData.clarity,
                cert_number: certData.certNumber,
                length: certData.length,
                width: certData.width,
                depth: certData.depth,
                ProductId: diamond.id,
                ProductSubtype: 'NATURAL',
                supplier_material_final_price: null,
                supplier_material_quoted_price: null
              }
            ],
            melees: [],
            gemstones: []
          },
          quote_price: 1200,
          stones_price: certData.price,
          metal_price: 100,
          labour_price: 200,
          expected_cost: 2000,
          payment_term_selected: 'ip_payment'
        };

        cy.api({
          method: 'POST',
          url: Cypress.env('adminApiUrl'),
          headers: {
            Authorization: `Bearer ${Cypress.env('authToken')}`,
            'Content-Type': 'application/json'
          },
          body: {
            query: mutation,
            variables
          }
        }).then((res) => {
          expect(res.status).to.eq(200);

          return res.body.data.admin_create_jewellery_order.items;
        });
      });
    });
  });
});
Cypress.Commands.add('getJewelleryById', () => {
  cy.fixture('jewellerySku.json').then((jewelleryData) => {
    const JewellerySku = jewelleryData[0];

    const query = `
      query Get_Jewellery($id: ID!) {
        jewellery(id: $id) {
          id
          sku
          gender
          sku_supplier
          supplier {
            name
            id
            __typename
          }
          accent_metal_type
          accent_karat
          collection_name_supplier
          type
          kind
          jewellery_type
          metal_type
          karat
          size
          size_type
          description
          comments
          qc_requirements
          createdAt
          supplierStockId
          status
          NivodaStockId
          supplier_notified
          quote_price
          metal_price
          stones_price
          labour_price
          supplier_request_method
          supplier_order_data
          jewellery_files {
            id
            type
            url
            file_name
            __typename
          }
          stones {
            id
            is_mount
            ProductType
            ProductSubtype
            description
            comments
            cert_number
            carats
            pieces
            shape
            color
            clarity
            cut
            stone_position
            length
            width
            depth
            from_mm_range
            to_mm_range
            gem_type
            metal_type
            metal_color
            metal_weight
            metal_size
            metal_width
            width_tolerance
            metal_depth
            depth_tolerance
            engraving_font
            engraving_text
            chain_type
            clasp_type
            createdAt
            supplierStockId
            status
            __typename
          }
          mounts {
            id
            is_mount
            ProductType
            ProductSubtype
            description
            comments
            cert_number
            carats
            pieces
            shape
            color
            clarity
            cut
            stone_position
            length
            width
            depth
            from_mm_range
            to_mm_range
            gem_type
            metal_type
            metal_color
            metal_weight
            metal_size
            metal_width
            width_tolerance
            metal_depth
            depth_tolerance
            engraving_font
            engraving_text
            chain_type
            clasp_type
            createdAt
            supplierStockId
            status
            __typename
          }
          __typename
        }
      }
    `;

    cy.api({
      method: 'POST',
      url: `${Cypress.env('adminApiUrl')}`,
      body: {
        query,
        variables: {
          id: JewellerySku.id
        }
      },
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      const jewelleryData = response.body.data.jewellery;
      cy.writeFile('cypress/fixtures/getJewelleryData.json', jewelleryData);
    });
  });
});

Cypress.Commands.add('updateJewellerySku', () => {
  cy.fixture('getJewelleryData').then((data) => {
    const jewellery_id = data.id;

    const jewellery = {
      CompanyId: 'ee2aa9e8-ece7-473a-b0b3-98eebdcbdfff',
      type: data.type,
      jewellery_type: data.jewellery_type,
      kind: data.kind,
      gender: data.gender,
      metal_type: data.metal_type,
      karat: data.karat,
      size: data.size,
      size_type: data.size_type,
      qc_requirements: data.qc_requirements,
      comments: data.comments,
      components: [
        {
          component_type: data.stones[0].ProductType,
          Diamond: {
            id: data.stones[0].id,
            ProductSubtype: data.stones[0].ProductSubtype,
            ProductType: data.stones[0].ProductType,
            carats: data.stones[0].carats,
            cert_number: data.stones[0].cert_number,
            clarity: data.stones[0].clarity,
            color: data.stones[0].color,
            cut: data.stones[0].cut,
            depth: data.stones[0].depth,
            length: data.stones[0].length,
            pieces: data.stones[0].pieces,
            shape: data.stones[0].shape
          }
        }
      ],
      jewellery_files: data.jewellery_files,
      accent_metal_type: data.accent_metal_type ?? null,
      accent_karat: data.accent_karat ?? null
    };

    const mutation = `
      mutation adminUpdateJewellerySku($input: InputUpdateJewellerySKU!) {
        admin_update_jewellery_sku(input: $input) {
          id
          sku
          ...JewelleryExtendedFragment
          __typename
        }
      }

      fragment JewelleryExtendedFragment on Jewellery {
        ...JewelleryFragment
        type
        jewellery_type
        kind
        gender
        karat
        metal_type
        accent_metal_type
        accent_karat
        size
        size_type
        createdAt
        supplier {
          name
          id
          __typename
        }
        jewellery_components {
          ...JewelleryComponentFragment
          __typename
        }
        __typename
      }

      fragment JewelleryFragment on Jewellery {
        id
        sku
        status
        jewellery_type
        comments
        qc_requirements
        jewellery_files {
          id
          type
          url
          file_name
          __typename
        }
        supplier {
          id
          name
          __typename
        }
        description
        sku_supplier
        collection_name_supplier
        supplier_notified
        quote_price
        metal_price
        stones_price
        labour_price
        isFinished
        __typename
      }

      fragment JewelleryComponentFragment on JewelleryComponent {
        id
        ProductType
        ProductSubtype
        color
        cut
        cert_number
        shape
        carats
        clarity
        pieces
        gem_type
        from_mm_range
        to_mm_range
        nivoda_material_quoted_price
        nivoda_material_quoted_price_per_carat
        __typename
      }
    `;

    cy.api({
      method: 'POST',
      url: Cypress.env('adminApiUrl'),
      headers: {
        Authorization: `Bearer ${Cypress.env('authToken')}`,
        'Content-Type': 'application/json'
      },
      body: {
        query: mutation,
        variables: {
          input: {
            jewellery_id,
            jewellery
          }
        }
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      cy.log('Jewellery SKU updated successfully');
    });
  });
});
Cypress.Commands.add('updateJewelleryStatus', (id, status) => {
  const mutation = `
    mutation updateJewelleryStatus($id: ID!, $status: JewelleryStatus!) {
      update_jewellery_status(id: $id, status: $status) {
        id
        sku
        status
        jewellery_type
        comments
        description
        jewellery_components {
          id
          is_mount
          status
          stone_position
          ProductType
          description
          __typename
        }
        order_item {
          id
          status
          __typename
        }
        __typename
      }
    }
  `;

  cy.api({
    method: 'POST',
    url: Cypress.env('adminApiUrl'),
    body: {
      query: mutation,
      variables: { id, status }
    },
    headers: {
      Authorization: `Bearer ${Cypress.env('authToken')}`,
      'Content-Type': 'application/json'
    }
  }).then((response) => {
    expect(response.status).to.eq(200);

    if (response.body.errors) {
      cy.log('GraphQL Errors: ' + JSON.stringify(response.body.errors));
      throw new Error('GraphQL returned errors');
    }

    const updated = response.body?.data?.update_jewellery_status;

    // Ensure updated data exists
    if (!updated) {
      cy.log('No update_jewellery_status data returned');
      throw new Error('update_jewellery_status is undefined or null');
    }

    expect(updated).to.have.property('status', status);

    cy.wrap(updated).as('updatedJewellery');
  });
});
