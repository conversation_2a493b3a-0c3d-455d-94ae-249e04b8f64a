Cypress.Commands.add('addToShortlist', (diamond_Id, options = {}) => {
  const { is_memo = false } = options;

  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `
        mutation updateShortlistItems($items: [InputShortlistItem!]!, $is_memo: Boolean) {
          update_shortlist(items: $items, is_memo: $is_memo) {
            id
            shortlist_items {
              id
              added_at
              carats
              pieces
              is_memo
              note
            }
          }
        }
      `,
      variables: {
        items: [
          {
            id: diamond_Id,
            added_at: Date.now(),
            carats: 0,
            pieces: 0,
            offer_id: Cypress.env('offerId'),
            note: null
          }
        ],
        is_memo: is_memo
      }
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    cy.log(`✅ Shortlist updated successfully for ID: ${diamond_Id}`);
  });
});

Cypress.Commands.add('addToMeleeShortlist', (melee_Id, melee_Offer_Id, melee_Pieces, melee_Carats) => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `
        mutation updateShortlistItems($items: [InputShortlistItem!]!, $is_memo: Boolean) {
          update_shortlist(items: $items, is_memo: $is_memo) {
            id
            shortlist_items {
              id
              added_at
              carats
              pieces
              is_memo
              note
            }
          }
        }
      `,
      variables: {
        items: [
          {
            id: melee_Id,
            added_at: Date.now(),
            carats: melee_Carats,
            pieces: melee_Pieces,
            offer_id: melee_Offer_Id,
            note: null,
            customer_preference: 'carats'
          }
        ],
        is_memo: false
      }
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    cy.log(`✅ Shortlist updated successfully for ID: ${melee_Id}`);
  });
});
Cypress.Commands.add('getShortlistItems', (melee_Offer_Id) => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    body: {
      operationName: 'getShortlistItems',
      variables: { offer_id: melee_Offer_Id },
      query: `query getShortlistItems {
        me {
          id
          shortlist_items {
            id
            added_at
            carats
            is_memo
            customer_preference
            offer {
              id
              price
              price_per_carat
              discount
              Product {
                __typename
                ... on Melee {
                  id
                  carats
                  certificate {
                    certNumber
                  }
                }
              }
            }
          }
          shortlist_memo_items {
            id
            added_at
            carats
            is_memo
            customer_preference
            offer {
              id
              price
              price_per_carat
              discount
              Product {
                __typename
                ... on Melee {
                  id
                  carats
                  certificate {
                    certNumber
                  }
                }
              }
            }
          }
        }
      }`
    },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    }
  }).then((response) => {
    const regularItems = response.body.data.me.shortlist_items || [];
    const memoItems = response.body.data.me.shortlist_memo_items || [];

    const allOfferIds = [
      ...regularItems.map((item) => item.offer?.id || ''),
      ...memoItems.map((item) => item.offer?.id || '')
    ];

    cy.log(`Found ${regularItems.length} regular items and ${memoItems.length} memo items`);
    cy.log(`Looking for offer ID: ${melee_Offer_Id}`);
    cy.log(`Available offer IDs: ${JSON.stringify(allOfferIds)}`);

    expect(allOfferIds).to.include(melee_Offer_Id);
  });
});
Cypress.Commands.add('updateShortlistItems', (offerId, carats) => {
  const addedAt = Date.now() / 1000; // Get current time in seconds

  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    body: {
      operationName: 'updateShortlistItems',
      variables: {
        items: [
          {
            offer_id: offerId,
            carats: carats,
            customer_preference: 'carats',

            added_at: addedAt // Include current timestamp
          }
        ]
      },
      query: `mutation updateShortlistItems($items: [InputShortlistItem!]!) {
        update_shortlist(items: $items) {
          id
          shortlist_items {
            id
            carats
            added_at
            __typename
          }
          __typename
        }
      }`
    },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
  });
});
