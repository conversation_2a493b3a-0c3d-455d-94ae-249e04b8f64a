Cypress.Commands.add('getAllOrders', (productType, status) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        query: `
        query ($status: CustomerOrderItemStatus, $query: OrderItemQuery, $offset: Int) {
          order_items(status: $status, limit: 30, offset: $offset, query: $query) {
            total_count
            items {
              id
              order_number
            }
          }
        }
      `,
        variables: { status, query: { ProductType: productType } }
      }
    })
    .then(({ status, body }) => {
      expect(status).to.eq(200);
      const itemIds = body?.data?.order_items?.items.map((order) => order.id) || [];
      cy.log(`✅ Total Orders: ${body?.data?.order_items?.total_count}`);
      return cy.wrap(itemIds);
    });
});

Cypress.Commands.add('exportOrderAPI', (orderItemIds, orderDate, status) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        query: `
        mutation CreateOrderItemsForExcelExport($order_item_ids: [String!], $order_date: OrderDate!, $status: CustomerOrderItemStatus!) {
          create_order_items_excel_export(
            query: { order_item_ids: $order_item_ids, order_date: $order_date, status: $status }
          )
        }
      `,
        variables: { order_item_ids: orderItemIds, order_date: orderDate, status }
      }
    })
    .then(({ status }) => {
      expect(status).to.eq(200);
      cy.log(`✅ Order Exported Successfully`);
    });
});
