function getNextDayWithOffset() {
  const currentDate = new Date();
  currentDate.setDate(currentDate.getDate() + 1); // Add 1 day

  const offset = currentDate.getTimezoneOffset(); // Get timezone offset in minutes
  const offsetHours = Math.abs(Math.floor(offset / 60));
  const offsetMinutes = Math.abs(offset % 60);
  const sign = offset > 0 ? '-' : '+'; // Convert offset to correct sign

  const formattedDate = currentDate.toISOString().split('.')[0]; // Remove milliseconds
  const timezoneOffset = `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;

  return `${formattedDate}${timezoneOffset}`;
}

Cypress.Commands.add('createHold', (type, user_Id, stone_Id) => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `
        mutation ($ProductId: ID!, $ProductType: ProductType!, $until: String, $customer_id: ID) {
          create_hold(
            ProductId: $ProductId
            ProductType: $ProductType
            until: $until
            customer_id: $customer_id
          ) {
            id
            status
            until
          }
        }
      `,
      variables: {
        ProductId: stone_Id,
        ProductType: type,
        customer_id: user_Id,
        until: getNextDayWithOffset()
      }
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    expect(response.body.data.create_hold.status).to.eq('REQUESTED');
    cy.log(`✅ Hold Placed successfully for ID: ${stone_Id}`);
  });
});
