Cypress.Commands.add('diamondsDownloadLinkAPI', (cert_id) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        variables: { cert_id },
        query: `
          query GetVideoLink($cert_id: ID!) {
            get_video_download_link(cert_id: $cert_id)
          }
        `
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);

      const downloadLink = response.body.data.get_video_download_link;

      cy.log('🎥 Diamonds Video Download Link:', downloadLink);
    });
});

Cypress.Commands.add('gemsDownloadLinkAPI', (cert_id) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        variables: { cert_id },
        query: `
          query GetGemsVideoLink($cert_id: ID!) {
          get_gemstone_video_download_link(cert_id: $cert_id)
          }
          `
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);

      const downloadLink = response.body.data.get_gemstone_video_download_link;

      cy.log('🎥 Gems Video Download Link:', downloadLink);
    });
});
