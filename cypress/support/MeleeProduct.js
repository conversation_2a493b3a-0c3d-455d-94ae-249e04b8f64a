/// <reference types="cypress" />
class MeleeProduct {
  constructor() {
    // Locators

    this.returnText = '.returnText';
    this.productContainer = '[data-automation-id="product-container"]';
    this.searchBarText = '[data-automation-id="search-by-certificate-number-or-stockIdtext"]';
    this.selectQuantityButton = '[data-automation-id="melee_action__selectQuantityBtn"]';
    this.addToCartButton = '[data-automation-id="add-to-cart-btn"]';
    this.meleeCartInput = '[data-automation-id="select-quantity-modal__input-field"]';
    this.addToCartNotification = '.go3958317564 > div';
    this.meleeShortlistButton = '[data-automation-id="melee_action__shortlist"]';
    this.addMeleeToShortList = '[data-automation-id="add-to-cart-btn"]';
    this.contentArea = '.content-area';
    this.selectQuantityContent = '.select-quantity-modal__content';
    this.piecesButton = '.select-quantity-modal__input_toggle_btn.select-quantity-modal__input_toggle_pcs';
    this.caratsButton = '.select-quantity-modal__input_toggle_btn.select-quantity-modal__input_toggle_ct';
    this.quantityToggle = '.select-quantity-modal__input_toggle';
    this.quantityInputField = '.select-quantity-modal__input_field';
    this.deliveryNotes = '.select-quantity-modal__delivery_notes';
    this.modalFooter = '.select-quantity-modal__footer';
    this.applyChangesButton = '[data-automation-id="apply-changes-btn"]';
    this.selectQuantityDescription = '.select-quantity-modal__description';
    this.shortlistedIcon = '[data-automation-id="melee_action__shortlist--icon"]';
    this.removeFromCartButton = '[data-testid="remove_btn"]';
    this.verifiedBtn = '.verified';
    this.diamondImageContainer = '.diamond_image_container';
    this.descriptionContainer = '.description-container';
    this.stockIdContainer = '.description-container__stock-id';
    this.productDetails = '.product_details';
    this.deliveryInfo = '.delivery_info';
    this.priceSection = '[data-automation-id="price-section"]';
    this.meleeActions = '.melee_actions';
    this.listViewButton = '[data-testid="search-melee-list-view-btn"]';
    this.countryFlag = '.meleeTable__delivery .country-flag-img';
    this.sortDropdown = '[data-automation-id="select--inp"]';
    this.sortOption = '[data-automation-id="select-option"]';
    this.priceSection = '.left-section > .left-section__sub > .smallBold';
    this.caratSection = '.product_details > :nth-child(6) > .product_details_columns > .smallBold';
    this.stockIdPLP = '.description-container__stock-id_num';
    this.caratsAndPiecesFieldMiniCart = '.product_description__info--item.quantity';
    this.caratsAndPiecesFieldCartPage = '.cart_item_basic__item_info__list--object--value';
    this.editButton = '.edit-btn';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.menuButton = '.menu--btn';
    this.menuRemoveButton = '.remove';
    this.meleeShortlistToastModal = '.go3958317564';
    this.menuShortlistRemoveButton = '.menu--item.remove';
    this.emptyShortlistWrapper = '.empty_cart_text_wrapper';
    this.cartItems = '.mini_card_descriptions';
    this.drawerTop = '.mini_card';
  }

  verifyReturnText() {
    cy.get(this.returnText, { timeout: 5000 }).should('be.visible').contains('Not returnable');
  }

  totalPriceNotVisibleAssertion() {
    cy.get(this.productContainer, { timeout: 5000 }).should('be.visible').should('not.contain', 'Total Price');
  }

  totalPriceVisibleAssertion() {
    cy.get(this.productContainer, { timeout: 5000 }).should('be.visible').should('contain.text', 'Total Price');
  }

  searchMeleeStone(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const stockId = data[0].NivodaStockId;

      cy.get(this.searchBarText, { timeout: 60000 })
        .eq(1)
        .should('be.visible')
        .should('be.enabled')
        .type(stockId, { force: true, delay: 0 });
      cy.get(this.searchBarText).eq(1).invoke('val').should('eq', stockId);
      cy.get(this.stockIdPLP, { timeout: 60000 }).should('be.visible').contains(stockId);
    });
  }

  addMeleeInputs(carats) {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();
    cy.get(this.meleeCartInput, { timeout: 60000 })
      .should('be.visible')
      .clear({ force: true })
      .click({ force: true })
      .type(carats, { force: true });
  }

  addMeleeToCart() {
    cy.get(this.addToCartButton, { timeout: 60000 }).should('be.visible').eq(0).click();
    cy.get(this.addToCartNotification, { timeout: 50000 })
      .should('be.visible')
      .contains('Melee has been added to the cart');
    cy.get(this.drawerTop).find(this.cartItems, { timeout: 50000 }).should('have.length.greaterThan', 0);
  }

  shortlistQuantityModalVisibleAssertion() {
    cy.get(this.meleeShortlistButton, { timeout: 60000 }).eq(0).should('be.visible').click();
    cy.get(this.contentArea, { timeout: 60000 }).should('be.visible');
  }

  verifyQuantityModalForShortlist() {
    cy.contains('Select quantity');
    cy.contains('Enter quantity in either carats or pieces');
    cy.get(this.selectQuantityDescription, { timeout: 60000 }).should('be.visible');
    cy.get(this.quantityToggle, { timeout: 5000 }).should('be.visible');
    cy.get(this.quantityInputField, { timeout: 5000 }).should('be.visible');
    cy.get(this.deliveryNotes, { timeout: 5000 }).should('be.visible');
    cy.get(this.modalFooter, { timeout: 5000 }).should('be.visible');
    cy.get(this.addMeleeToShortList, { timeout: 60000 }).should('be.disabled');
  }

  verifyQuantityModalForCart() {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();
    this.verifyQuantityModalForShortlist();
  }

  openSelectQuantityModal() {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();
  }

  meleeRequestButtonVisibilityAssertion() {
    cy.get(this.addToCartButton, { timeout: 60000 }).should('be.visible').contains('Make a request');
  }

  addMeleePieces(pieces) {
    cy.get(this.piecesButton, { timeout: 60000 }).should('be.visible').click();
    cy.get(this.meleeCartInput, { timeout: 60000 })
      .should('be.visible')
      .clear({ force: true })
      .type(pieces, { force: true });
  }

  addMeleeCarats(carats) {
    cy.get(this.caratsButton, { timeout: 60000 }).should('be.visible').click();
    cy.get(this.meleeCartInput, { timeout: 60000 })
      .should('be.visible')
      .clear({ force: true })
      .type(carats, { force: true });
  }

  verifyApproxQuantityOfCaratsPieces() {
    cy.get(this.selectQuantityContent, { timeout: 60000 }).should('be.visible').contains('pieces');
    this.addMeleePieces('3');
    cy.get(this.selectQuantityContent, { timeout: 60000 }).should('be.visible').contains('carats');
  }

  verifyQuantityAddedToCart(carats, pieces) {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).should('contain.text', `Added to cart (${carats} ct)`);
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();

    this.addMeleePieces(pieces);

    cy.get(this.applyChangesButton, { timeout: 6000 }).should('be.visible').click();
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).should('contain.text', `Added to cart (${pieces} pcs)`);
  }

  verifyUpdateCartModal() {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();
    cy.contains('Update Cart');
    cy.contains('Change the quantity of melee you have in your cart');
    cy.get(this.selectQuantityDescription, { timeout: 60000 }).should('be.visible');
    cy.get(this.quantityToggle, { timeout: 5000 }).should('be.visible');
    cy.get(this.quantityInputField, { timeout: 5000 }).should('be.visible');
    cy.get(this.deliveryNotes, { timeout: 5000 }).should('be.visible');
    cy.get(this.applyChangesButton, { timeout: 60000 }).should('be.enabled');
  }

  shortlistMelee() {
    cy.get(this.meleeShortlistButton, { timeout: 60000 }).eq(0).should('be.visible').click();
    cy.get(this.shortlistedIcon, { timeout: 60000 }).should('have.attr', 'color', '#EF4444');
  }

  verifyUpdateModalNotVisibleForShortlist() {
    this.shortlistMelee();
    cy.get(this.contentArea, { timeout: 60000 }).should('not.exist');
  }

  removeMeleeFromUpdateCartModal() {
    cy.get(this.selectQuantityButton, { timeout: 60000 }).eq(0).click();
    cy.wait(3000);
    cy.get(this.removeFromCartButton, { timeout: 50000 }).should('be.visible').contains('Remove').dblclick();
    cy.get(this.addToCartNotification, { timeout: 50000 })
      .should('be.visible')
      .contains('Melee has been removed from the cart');
  }

  verifyNivodaVerifiedModal() {
    cy.get(this.verifiedBtn, { timeout: 60000 }).should('be.visible').eq(0).click();
    cy.get(this.contentArea, { timeout: 60000 })
      .should('be.visible')
      .contains(
        'Our selection is verified by a GIA Certified Gemologist in-house. To ensure grading accuracy and product quality, the stones are inspected, evaluated and vetted before shipping.'
      );
  }

  accessListView() {
    cy.get(this.listViewButton, { timeout: 60000 }).should('be.visible').click();
  }

  verifyMeleePLPCard() {
    cy.get(this.diamondImageContainer, { timeout: 5000 }).should('be.visible');
    //cy.get(this.descriptionContainer, { timeout: 5000 }).should('be.visible');
    // cy.get(this.stockIdContainer, { timeout: 5000 }).should('be.visible');
    cy.get(this.productDetails, { timeout: 5000 }).should('be.visible');
    cy.get(this.deliveryInfo, { timeout: 5000 }).should('be.visible');
    cy.get(this.priceSection, { timeout: 5000 }).should('be.visible');
    cy.get(this.meleeActions, { timeout: 5000 }).should('be.visible');
  }

  flagVisibilityAssertion() {
    cy.get(this.countryFlag, { timeout: 50000 }).eq(0).should('be.visible');
  }

  verifyPriceLowToHigh() {
    cy.get(this.sortDropdown, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.get(this.sortOption, { timeout: 5000 }).contains('Price low to high').click();

    cy.get(this.priceSection, { timeout: 50000 })
      .eq(0)
      .should('be.visible')
      .invoke('text')
      .then((firstPriceText) => {
        const firstPrice = parseFloat(firstPriceText.replace(/[^\d.]/g, ''));
        cy.get(this.priceSection, { timeout: 50000 })
          .eq(1)
          .should('be.visible')
          .invoke('text')
          .then((secondPriceText) => {
            const secondPrice = parseFloat(secondPriceText.replace(/[^\d.]/g, ''));
            expect(firstPrice).to.be.at.most(secondPrice);
          });
      });
  }

  verifyPriceHighToLow() {
    cy.get(this.sortDropdown, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.get(this.sortOption, { timeout: 5000 }).contains('Price high to low').click();

    cy.get(this.priceSection, { timeout: 50000 })
      .eq(0)
      .should('be.visible')
      .invoke('text')
      .then((firstPriceText) => {
        const firstPrice = parseFloat(firstPriceText.replace(/[^\d.]/g, ''));
        cy.get(this.priceSection, { timeout: 50000 })
          .eq(1)
          .should('be.visible')
          .invoke('text')
          .then((secondPriceText) => {
            const secondPrice = parseFloat(secondPriceText.replace(/[^\d.]/g, ''));
            expect(firstPrice).to.be.at.least(secondPrice);
          });
      });
  }

  verifyCaratsHighToLow() {
    cy.get(this.sortDropdown, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.get(this.sortOption, { timeout: 5000 }).contains('Carat high to low').click();

    cy.get(this.caratSection, { timeout: 50000 })
      .eq(0)
      .should('be.visible')
      .invoke('text')
      .then((firstCaratText) => {
        const firstCarat = parseFloat(firstCaratText.replace(/[^\d.]/g, ''));
        cy.get(this.caratSection, { timeout: 50000 })
          .eq(1)
          .should('be.visible')
          .invoke('text')
          .then((secondCaratText) => {
            const secondCarat = parseFloat(secondCaratText.replace(/[^\d.]/g, ''));
            expect(firstCarat).to.be.at.least(secondCarat);
          });
      });
  }

  verifyCaratsLowToHigh() {
    cy.get(this.sortDropdown, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.get(this.sortOption, { timeout: 5000 }).contains('Carat low to high').click();

    cy.get(this.caratSection, { timeout: 50000 })
      .eq(0)
      .should('be.visible')
      .invoke('text')
      .then((firstCaratText) => {
        const firstCarat = parseFloat(firstCaratText.replace(/[^\d.]/g, ''));
        cy.get(this.caratSection, { timeout: 50000 })
          .eq(1)
          .should('be.visible')
          .invoke('text')
          .then((secondCaratText) => {
            const secondCarat = parseFloat(secondCaratText.replace(/[^\d.]/g, ''));
            expect(firstCarat).to.be.at.most(secondCarat);
          });
      });
  }

  caratsAndPiecesAssertionForMiniCart(carats, pieces) {
    cy.get(this.caratsAndPiecesFieldMiniCart, { timeout: 5000 }).eq(0).should('be.visible').contains(carats);
    cy.get(this.caratsAndPiecesFieldMiniCart, { timeout: 5000 }).eq(1).should('be.visible').contains(pieces);
  }

  caratsAndPiecesAssertionForCartPage(carats) {
    cy.wait(3000);
    cy.get(this.caratsAndPiecesFieldCartPage, { timeout: 10000 })
      .eq(2)
      .scrollIntoView()
      .should('be.visible')
      .invoke('text')
      .then((text) => {
        const normalizedText = text.replace(/\s+/g, ' ').trim();
        expect(normalizedText).to.include(carats);
      });
  }

  editMeleePreference(pieces) {
    cy.get(this.editButton, { timeout: 5000 }).should('be.visible').click();
    this.addMeleePieces(pieces);

    cy.get(this.applyChangesButton, { timeout: 6000 }).should('be.visible').click();
  }

  verifyRequestButtonCart(carats) {
    cy.get(this.editButton, { timeout: 5000 }).should('be.visible').click();
    this.addMeleeCarats(carats);
    cy.get(this.applyChangesButton, { timeout: 60000 }).should('be.visible').contains('Make a request');
  }

  verifyRequestButtonNotVisible(pieces) {
    cy.get(this.editButton, { timeout: 5000 }).should('be.visible').click();
    this.addMeleePieces(pieces);

    cy.get(this.applyChangesButton, { timeout: 6000 })
      .should('be.visible')
      .should('not.contain.text', 'Make a request')
      .click();
  }

  removeMeleeViaShortlistModal() {
    cy.get(this.editButton, { timeout: 5000 }).eq(0).scrollIntoView().should('be.visible').click();
    cy.wait(3000);
    cy.get(this.removeFromCartButton, { timeout: 50000 }).should('be.visible').contains('Remove').dblclick();
    cy.get(this.meleeShortlistToastModal, { timeout: 60000 })
      .should('be.visible')
      .contains('Melee has been removed from the shortlist');
  }

  removeMeleeViaShortlistMenu() {
    cy.get(this.menuButton, { timeout: 5000 }).eq(0).scrollIntoView().click();
    cy.wait(3000);
    cy.get(this.menuShortlistRemoveButton, { timeout: 50000 })
      .should('be.visible')
      .contains('Remove from shortlist')
      .click();

    cy.get(this.emptyShortlistWrapper, { timeout: 5000 }).should('be.visible').contains('This shortlist is empty');
  }
}
export default MeleeProduct;
