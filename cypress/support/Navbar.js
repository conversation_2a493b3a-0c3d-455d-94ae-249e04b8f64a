/// <reference types="cypress" />
class Navbar {
  constructor() {
    // Locators

    this.navBar = '[data-automation-id="category-tab-label"]';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.heading = '#sub-header-section';
    this.jewelryHeading = 'h1';
  }

  visitLabgrownDiamonds() {
    const apiUrl = Cypress.env('apiurl');

    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'platformLabgrownDefaultQuery') {
        req.alias = 'diamondsData';
      }
    });
    cy.get(this.navBar, { timeout: 60000 }).contains('Lab grown diamonds').click();
    cy.url().should('include', '/labgrown/diamond');
    cy.get(this.heading, { timeout: 60000 }).contains('Lab grown diamonds');
    cy.wait('@diamondsData').then((interception) => {
      const requestBody = interception.request.body;
      expect(requestBody.query).to.include('offers_by_query');
      expect(interception.response.statusCode).to.eq(200);
    });
  }

  visitNaturalDiamonds() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.navBar, { timeout: 60000 }).contains('Natural diamonds').click();
    cy.url().should('include', '/natural/diamond');
    cy.get(this.heading, { timeout: 60000 }).contains('Natural diamonds');
  }

  visitGemStones() {
    const apiUrl = Cypress.env('apiurl');

    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'platformGemstoneDefaultQuery') {
        req.alias = 'gemsData';
      }
    });
    cy.get(this.navBar, { timeout: 60000 }).contains('Gemstones').click();
    cy.url().should('include', '/gemstones');
    cy.get(this.heading, { timeout: 60000 }).contains('Gemstones');
    cy.wait('@gemsData').then((interception) => {
      const requestBody = interception.request.body;
      expect(requestBody.query).to.include('GemstoneQuery');
      expect(interception.response.statusCode).to.eq(200);
    });
  }

  visitNaturalMelee() {
    const apiUrl = Cypress.env('apiurl');

    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'platformNaturalMeleeDefaultQuery') {
        req.alias = 'meleeData';
      }
    });
    cy.get(this.navBar, { timeout: 60000 }).contains('Natural melee').click();
    cy.url().should('include', 'natural/melee');
    cy.get(this.heading, { timeout: 60000 }).contains('Natural melee');
    cy.wait('@meleeData').then((interception) => {
      const requestBody = interception.request.body;
      expect(requestBody.query).to.include('MeleeSearchQuery');
      expect(interception.response.statusCode).to.eq(200);
    });
  }

  visitLabGrownMelee() {
    const apiUrl = Cypress.env('apiurl');

    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'platformLabgrownMeleeDefaultQuery') {
        req.alias = 'meleeData';
      }
    });
    cy.get(this.navBar, { timeout: 60000 }).contains('Lab grown melee').click();
    cy.url().should('include', 'labgrown/melee');
    cy.get(this.heading, { timeout: 60000 }).contains('Lab grown melee');
    cy.wait('@meleeData').then((interception) => {
      const requestBody = interception.request.body;
      expect(requestBody.query).to.include('MeleeSearchQuery');
      expect(interception.response.statusCode).to.eq(200);
    });
  }

  visitJewelry() {
    cy.get(this.navBar, { timeout: 60000 }).contains('Jewelry').click();
    cy.url().should('include', '/jewellery');
    cy.intercept('GET', '**/search/undefined/**', (req) => {
      req.redirect('/v2/live/search/jewellery');
    });
    cy.get(this.jewelryHeading, { timeout: 60000 }).contains('Jewelry');
  }
}
export default Navbar;
