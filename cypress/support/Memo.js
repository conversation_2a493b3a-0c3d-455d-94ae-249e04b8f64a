/// <reference types="cypress" />
import { faker } from '@faker-js/faker';
class Memo {
  constructor() {
    // Locators
    this.memoButton = '.togglerContainer:contains("Memo")';
    this.addToMemoButton = '[data-automation-id="add-to-memo-wallet"]';
    this.container = '[data-automation-id="product-container"]';
    this.memoWalletCart = '[data-automation-id="memo-wallet"]';
    this.memoProceedToCheckout = '[data-automation-id="proceed-to-memo-checkout"]';
    this.memoBreadcrum = '.nav_item_title';
    this.memoGuide = '.memo-guides_body';
    this.memoDuration = '[data-automation-id="Memo duration days"]';
    this.saveButton = '[class*="btn-wdc"]';
    this.memoCheckoutBanner = '[data-automation-id="checkout-banner"]';
    this.deliveryButton = '[data-automation-id="continue-to-delivery"]';
    this.memoLimitBar = '.credit-before-bar';
    this.creditBarText = '.credit_widget_sub-title';
    this.creditBarValues = '.credit_widget_title';
    this.goToInvoicesButton = '[class*="dPeROS"]';
    this.customerSupportButton = 'button[mode="memo"]';
    this.memoBadge = '.card--badges';
    this.removeCartButton = '.mcw_cart-item_info_action_link';
    this.memoCartItemText = '.mcw_cart-top_header_right';
    this.memoSummary = '[class*="order-summary__price-label"]';
    this.memoSummaryValue = '.order-summary__price-value';
    this.moreCredit = '.credit_widget_action-area_info';
    this.continueToDeliveryButton = '[data-automation-id="continue-to-delivery"]';
    this.changeDeliveryButton = '[data-automation-id="change-delivery-for-this-order"]';
    this.addNewAddressButton = '[data-automation-id="add-new-address"]';
    this.addressName = '[data-automation-id="Location name (e.g. Hatton Garden)"]';
    this.countryField = '.css-isze0s-control > .css-hlgwow > .css-19bb58m';
    this.addressField = '[data-automation-id="Enter address 1"]';
    this.saveAddressButton = '[class*="sc-exAgwC"]';
    this.addressContainer = '[data-automation-id="address-selector"]';
    this.countryMenu = '[role="option"]';
    this.paymentTerms = '[data-automation-id="continue-to-payment-selection"]';
    this.creditBar = '.credit_widget_bar';
    this.paymentHeader = '[data-automation-id="payment-terms-title"]';
    this.drawerLeft = '#drawer-root_common > div > div.drawer.right';
    this.cartItems = '.mcw_cart-item';
    this.cartButton = '.header__view_cart_text';
    this.cartSelectedButton = '.toggle-btn.toggle-btn-active';
    this.memoWalletTogle = '.toggle-btn-label';
    this.emptyCartWalletText = '.mcw_empty-cart';
    this.yourCartIsEmptyText = '.mcw_text-h4.mcw_text-strong';
    this.yourCartIsEmptyMiniText = '.mcw_empty-cart_sub-text.mcw_text-subdued';
    this.allInOnePricingText = '.cart-non-aip-price_block_label';
    this.addToCart = '[data-automation-id="add-to-cart"]';
    this.addToShortListAndRemoveButton = '.mcw_cart-item_info_action_link';
    this.addToShortList = '[data-automation-id="shortlist-link"]';
    this.shortListIconFilled = '[class*="mcw_cart-item_shortlisted"]';
    this.shortListIcon = '[class*="mcw_cart-item_info_action_link"]';
    this.cartText = '.data_cell _description';
    this.shortListProductText = '.data_cell._description';
    this.buyModeCheckout = '[data-automation-id="proceed-to-memo-checkout"]';
    this.reviewCartPage = 'review_cart_page_left__header--title';
    this.deliveredPriceAmount = '.cart-price-text-strong-default';
    this.totalPriceAmount = '.mcw_text-h4.mcw_text-default';
    this.continueAddToCart = '[data-automation-id="continue-and-add"]';
    this.companySettings = '.settings_title';
    this.currency = '.css-1dimb5e-singleValue';
    this.cityField = '[data-automation-id="Enter city"]';
    this.stateField = '[data-automation-id="Enter State"]';
    this.postalField = '[data-automation-id="Enter postal code"]';
    this.paymentTerms = '[data-automation-id="continue-to-payment-selection"]';
    this.memoProceedToCheckout = '[data-automation-id="proceed-to-memo-checkout"]';
    this.deliveryButton = '[data-automation-id="continue-to-delivery"]';
    this.continueToDeliveryButton = '[data-automation-id="continue-to-delivery"]';
    this.otpCodeField = '[data-automation-id="otp-input-box"]';
    this.confirmButton = '[data-automation-id="confirm-button"]';
    this.paymentTermsChoiceBox = '[data-automation-id="payment-terms-title"]';
    this.reviewMemoOrder = '[data-automation-id="review-place-order"]';
    this.orderReviewCheckboxes = '[data-automation-id="checkbox"]';
    this.placeMemoOrder = '[data-automation-id="place-order"]';
    this.liveChatWrapper = ':nth-child(1) > iframe';

    this.addressData = {
      branchName: 'Test Branch',
      country: 'United Kingdom',
      streetAddress: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      postalCode: faker.location.zipCode()
    };
  }
  visitMemoTab() {
    cy.get(this.memoButton).click();
    this.changeCurrency();
  }
  verifyMemoBreadcrum() {
    this.visitMemoTab();
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(0).click();
    cy.wait(2000);
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(1).click();
    cy.get(this.memoWalletCart).click();
    cy.get(this.drawerLeft).find(this.cartItems, { timeout: 50000 }).should('have.length', 2);

    cy.get(this.memoProceedToCheckout).click();
    cy.get(this.memoBreadcrum).eq(0).contains('Memo wallet & notes');
  }
  verifyHowItWorkText(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const returndays = data[0].memo_duration;
      cy.log(`returndays: ${returndays}`);
      this.verifyMemoBreadcrum();
      cy.get(this.memoGuide).should(
        'have.text',
        `Unreturned stones are auto-invoiced  in ${returndays} days from deliveryReturn fee for Memo items is $20.00 per shipment within the ${returndays}-day periodLate returns incur a $70.00 restocking feeOnce confirmed converted Memo items cannot be returned`
      );
    });
  }
  updateMemoDuration() {
    const randomDuration = Math.floor(Math.random() * 10) + 1; // Generates a random number between 1 and 10
    cy.log(randomDuration);
    cy.get(this.companySettings).contains('Order Settings').click();
    cy.get(this.memoDuration).clear().type(randomDuration.toString());
    cy.writeFile('cypress/fixtures/memoDurations.json', [{ memo_duration: randomDuration }]);
    cy.wait(4000);
    cy.get(this.saveButton).eq(2).click();
  }

  verifyMemoReturnWindow(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const returndays = data[0].memo_duration;
      cy.log(returndays);
      cy.get(this.memoGuide).should(
        'have.text',
        `Unreturned stones are auto-invoiced  in ${returndays} days from deliveryReturn fee for Memo items is $20.00 per shipment within the ${returndays}-day periodLate returns incur a $70.00 restocking feeOnce confirmed converted Memo items cannot be returned`
      );
    });
  }
  memoCreditLimitExceedBanner() {
    cy.get(this.memoCheckoutBanner).contains(
      'Credit limit exceeded — unable to continue with Memo request. No payment terms available Go to your invoices page and pay due invoices now to enable credit payment terms.'
    );
  }
  memoDeliveryOptionDisabled() {
    cy.get(this.deliveryButton).should('be.disabled');
  }
  verifymemoLimitBarColor() {
    cy.get(this.memoLimitBar).should('be.visible');
    cy.get(this.memoLimitBar).should('have.css', 'background-color', 'rgb(185, 28, 28)');
  }
  verifyLimitExceedText() {
    cy.get(this.creditBarText).eq(1).contains('Used before this request');
    cy.get(this.creditBarText).eq(2).contains('Using for this request');
    cy.get(this.creditBarText).eq(3).contains('Available after this request');
    cy.get(this.creditBarValues).eq(0).should('be.visible');
    cy.get(this.creditBarValues).eq(1).should('be.visible');
    cy.get(this.creditBarValues).eq(2).should('be.visible');
  }
  VerifyGoToInvoiceButton() {
    cy.get(this.goToInvoicesButton).should('be.visible');
    cy.get(this.goToInvoicesButton).click();
    cy.url().should('include', 'invoices');
  }
  verifyCustomerSupportButton() {
    cy.get(this.customerSupportButton).contains('Contact support').should('be.visible').click();
    cy.wait(2000);
    cy.get(this.liveChatWrapper, { timeout: 50000 }).should('be.visible');
  }
  verifyMemoBadge() {
    cy.get(this.memoBadge, { timeout: 60000 }).eq(0).should('have.text', 'Memo');
  }
  verifyRemoveButton() {
    this.visitMemoTab();
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(0).click();
    cy.wait(2000);
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(1).click();
    cy.get(this.memoWalletCart).click();
    cy.get(this.removeCartButton).eq(1).contains('Remove').click();
    cy.get(this.memoCartItemText).should('have.text', '1/2 Item');
  }
  verifyMemoSummary() {
    cy.get(this.memoSummary).should('contain.text', 'Pay after invoicing');
    cy.get(this.memoSummaryValue).eq(3).should('be.visible');
    cy.get(this.memoSummary).should('contain.text', 'To pay now');
    cy.get(this.memoSummaryValue).eq(4).should('be.visible');
  }
  verifyNeedMoreCredit() {
    cy.get(this.moreCredit)
      .eq(0)
      .should(
        'have.text',
        'Need more credit?Go to your invoices page and pay due invoices to increase your limit, or get in touch with customer support.'
      );
  }
  verifyDeliveryOptionsBreadcrum() {
    cy.get(this.continueToDeliveryButton, { timeout: 50000 }).click();
    cy.get(this.memoBreadcrum).eq(1).contains('Delivery options');
  }
  verifyAddressButtonForNonCompanyUser() {
    cy.get(this.changeDeliveryButton).click();
    cy.get(this.addNewAddressButton).should('not.exist');
  }
  verifyAddressButtonForCompanyUser() {
    cy.get(this.changeDeliveryButton).click();
    cy.get(this.addNewAddressButton).should('be.visible');
  }
  addDeliveryAddress() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.addNewAddressButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.addressName, { timeout: 50000 }).should('be.enabled').clear().type(this.addressData.branchName);
    cy.get(this.countryField, { timeout: 50000 }).eq(0).click({ force: true }).type(this.addressData.country);
    cy.get(this.countryMenu, { timeout: 50000 }).should('be.visible').contains(this.addressData.country).click();
    cy.get(this.addressField, { timeout: 50000 })
      .should('be.enabled')
      .eq(0)
      .clear()
      .type(this.addressData.streetAddress);
    cy.get(this.cityField, { timeout: 50000 }).should('be.enabled').eq(0).clear().type(this.addressData.city);
    cy.get(this.stateField, { timeout: 50000 }).should('be.enabled').eq(0).clear().type(this.addressData.state);
    cy.get(this.postalField, { timeout: 50000 }).should('be.enabled').eq(0).clear().type(this.addressData.postalCode);
    cy.contains('Save location', { timeout: 50000 }).should('be.visible').should('be.enabled').click();
    cy.get(this.addressContainer, { timeout: 50000 }).should('be.visible').contains(this.addressData.branchName);
  }
  verifyPaymentTermsAfterInvoicing() {
    cy.get(this.paymentTerms).click();
    cy.get(this.memoBreadcrum).eq(2).contains('Payment terms after invoicing');
  }
  verifyCreditBarOnPaymentTerms() {
    cy.get(this.creditBar).should('be.visible');
  }
  verifyPaymentPeriod(fixtureFileName, days, length) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const returndays = data[0].memo_duration;

      let percentage = days === 30 ? data[0].thirtydaypercentage : data[0].sixtydaypercentage;

      percentage = percentage != null ? Number(percentage).toFixed(2) : null;

      console.log(`Updated Percentage: ${percentage}`);
      cy.get(this.paymentHeader).eq(length).click();

      const expectedText = percentage
        ? `Pay ${days} days after ${returndays}-day Memo period +${percentage}%`
        : `Pay ${days} days after ${returndays}-day Memo period `;

      cy.get(this.paymentHeader).eq(length).scrollIntoView().should('contain.text', expectedText);
    });
  }

  verifyMemoCheckout() {
    cy.get(this.memoProceedToCheckout, { timeout: 10000 }).should('be.visible').click();
    cy.get(this.memoBreadcrum).eq(0).contains('Memo wallet & notes');
    cy.get(this.deliveryButton, { timeout: 10000 }).should('be.visible').click();
    cy.get(this.memoBreadcrum).eq(1).contains('Delivery options');
    cy.get(this.paymentTerms, { timeout: 10000 }).contains('Continue to payment terms').click();
    cy.get(this.memoBreadcrum).eq(2).contains('Payment terms after invoicing');
    cy.get(this.paymentTermsChoiceBox, { timeout: 10000 }).contains('Pay 30 days after 7-day Memo period').click();
    cy.get(this.reviewMemoOrder).click();
    cy.get(this.memoBreadcrum).eq(3).contains('Order review');
    cy.get(this.orderReviewCheckboxes, { timeout: 10000 }).eq(0).click();
    cy.get(this.orderReviewCheckboxes, { timeout: 10000 }).eq(1).click();
    cy.get(this.placeMemoOrder, { timeout: 10000 }).eq(0).click();
    cy.wait(3000);
    cy.getOTP(new Date()).then((otpCode) => {
      const otpDigits = otpCode.split('');
      for (let i = 0; i < otpDigits.length; i++) {
        cy.get(this.otpCodeField, { timeout: 150000 }).eq(i).should('be.visible').type(otpDigits[i]);
      }
    });
    cy.get(this.confirmButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.otpCodeField, { timeout: 180000 }).should('not.exist');
  }

  verify60daysAmount(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const percentage = data[0].sixtydaypercentage;

      cy.get(this.memoSummaryValue)
        .eq(0)
        .invoke('text')
        .then((priceValue) => {
          const price = parseFloat(priceValue.replace(/[^\d.-]/g, ''));
          cy.log(price);
          const calculatedValue = (price * `${percentage}`) / 100 + price;
          cy.log(calculatedValue);
          cy.get(this.memoSummaryValue)
            .eq(1)
            .invoke('text')
            .then((secondPriceValue) => {
              const secondPrice = parseFloat(secondPriceValue.replace(/[^\d.-]/g, ''));
              expect(secondPrice).to.be.closeTo(calculatedValue, 0.2);
            });
        });
    });
  }
  verifyMemoDrawerAnimation() {
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(0).click();
    cy.wait(2000);
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(1).click();
    cy.get(this.memoWalletCart).click();
    cy.wait(300);
    cy.get(this.drawerLeft)
      .should('have.class', 'drawer right')
      .and('be.visible')
      .then(($drawer) => {
        const drawerPosition = $drawer.css('transform');
      });
    cy.get(this.drawerLeft).find(this.cartItems, { timeout: 50000 }).should('have.length', 2);
  }
  verifyCartItems() {
    cy.get(this.cartItems).should('have.length', 2);
  }
  validateCartButtonForBuyOption() {
    cy.get(this.cartButton).should('be.visible').click();
    cy.get(this.cartSelectedButton).should('have.text', 'Cart');
    cy.get(this.cartSelectedButton).then(($el) => {
      expect($el).to.have.css('background-color', 'rgb(86, 32, 225)');
    });
  }
  validateMemoWalletButtonForMemoOption() {
    cy.get(this.cartButton).should('be.visible').click();
    cy.get(this.memoWalletTogle).eq(1).should('have.text', 'Memo wallet').click();
    cy.get(this.cartSelectedButton).then(($el) => {
      expect($el).to.have.css('background-color', 'rgb(50, 108, 255)');
    });
  }
  validateEmptyCartText() {
    cy.get(this.emptyCartWalletText).should(
      'have.text',
      'Your cart is emptyWhen you add items in your cart,they will appear here.'
    );
    cy.get(this.yourCartIsEmptyText).then(($css) => {
      expect($css).to.have.text('Your cart is empty');
      expect($css).to.have.css('font-size', '20px');
      expect($css).to.have.css('font-weight', '700');
    });
    cy.get(this.yourCartIsEmptyMiniText).then(($css) => {
      expect($css).to.have.text('When you add items in your cart,they will appear here.');
      expect($css).to.have.css('font-size', '14px');
      expect($css).to.have.css('font-weight', '400');
      expect($css).to.have.css('color', 'rgb(120, 113, 108)');
    });
  }
  validateEmptyMemoWalletText() {
    cy.get(this.emptyCartWalletText).should(
      'have.text',
      'Your Memo wallet is emptyWhen you add items in your Memo wallet,they will appear here.'
    );
    cy.get(this.yourCartIsEmptyText).then(($css) => {
      expect($css).to.have.text('Your Memo wallet is empty');
      expect($css).to.have.css('font-size', '20px');
      expect($css).to.have.css('font-weight', '700');
    });
    cy.get(this.yourCartIsEmptyMiniText).then(($css) => {
      expect($css).to.have.text('When you add items in your Memo wallet,they will appear here.');
      expect($css).to.have.css('font-size', '14px');
      expect($css).to.have.css('font-weight', '400');
      expect($css).to.have.css('color', 'rgb(120, 113, 108)');
    });
  }
  verifyAllinOnePricingDisabledMemoTextVerify(length, text) {
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(0).click();
    cy.wait(2000);
    cy.get(this.addToMemoButton, { timeout: 60000 }).eq(1).click();
    cy.get(this.memoWalletCart).click();
    cy.get(this.memoWalletTogle).eq(`${length}`).should('have.text', `${text}`).click();
    cy.get(this.allInOnePricingText, { timeout: 160000 }).eq(0).should('have.text', 'Item price');
    cy.get(this.allInOnePricingText).eq(1).should('have.text', 'Delivered Price');
  }
  verifyAllinOnePricingDisabledCartTextVerify(length, text, cartlength) {
    cy.get(this.addToCart, { timeout: 60000 }).eq(cartlength).click({ force: true });

    cy.wait(5000);

    cy.document().then((doc) => {
      const contentAreas = Cypress.$('.content-area', doc);
      if (contentAreas.length > 0) {
        cy.get(this.continueAddToCart).click({ force: true });
      } else {
        cy.get(this.memoWalletTogle).eq(length).should('have.text', text);
        cy.get(this.allInOnePricingText, { timeout: 10000 }).eq(0).should('have.text', 'Item price');
        cy.get(this.allInOnePricingText).eq(1).should('have.text', 'Delivered Price');
      }
    });
  }

  verifyAddToWishListAndRemoveButtonExist() {
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Add to Memo Shortlist').click();
    cy.wait(2000);
    cy.get(this.addToShortListAndRemoveButton).eq(1).should('have.text', 'Remove');
  }
  addToMemoShortListButton() {
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Add to Memo Shortlist').click();
  }
  addToCartShortListButton() {
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Add to shortlist').click();
  }
  verifyAddToWishListCount(text, text1) {
    cy.get(this.addToShortList).should('have.text', '1');
    cy.log('shortlist has 1 count');
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', `${text}`);
    cy.log(`verfy has ${text}`);
    cy.get(this.shortListIconFilled)
      .eq(0)
      .then(($color) => {
        expect($color).to.have.css('fill', 'rgb(239, 68, 68)');
      });
    cy.log('color is verified');
    cy.get(this.addToShortListAndRemoveButton).eq(0).click();
    cy.log('clicking on remove from shortlist');
    cy.get(this.addToShortList).should('have.text', '0');
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', `${text1}`);
  }
  verifyAddToWishListCountForBuy(text, text1) {
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Add to shortlist').click();
    this.verifyAddToWishListCount(text, text1);
  }
  verifyRemoveFromShortListTextToAddToShortListForMemo() {
    cy.get(this.memoWalletCart).click();
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Remove from Memo Shortlist').click();
    cy.get(this.addToShortListAndRemoveButton).eq(0).should('have.text', 'Add to Memo Shortlist');
    cy.get(this.shortListIcon)
      .eq(0)
      .then(($color) => {
        expect($color).to.have.css('fill', 'none');
      });
  }

  verifyRemoveFromShortListTextToAddToShortListForBuy() {
    cy.get(this.shortListIcon)
      .eq(0)
      .then(($color) => {
        expect($color).to.have.css('fill', 'rgb(0, 0, 0)');
      });
  }
  verifyCartAndNotesBuyBreadcrum() {
    cy.get(this.buyModeCheckout).click();
    cy.get(this.memoBreadcrum).eq(0).contains('Cart & notes');
  }
  verifyReviewMemoCartText() {
    cy.get(this.verifyReviewMemoCartText).contains('Review Memo wallet & add notes');
  }
  verifyMemoWalletLimit(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const count = data[0].count;
      cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
      cy.get(this.addToMemoButton, { timeout: 60000 }).eq(0).should('have.text', `Add to memo walet (0/${count})`);
    });
  }
  sumOfDeliveredPrice() {
    cy.get(this.drawerLeft).find(this.cartItems, { timeout: 50000 }).should('have.length', 2);
    cy.get(this.memoProceedToCheckout, { timeout: 10000 }).should('not.have.text', 'Please wait...');
    cy.get(this.memoProceedToCheckout, { timeout: 10000 }).should('be.enabled');
    cy.get(this.deliveredPriceAmount).then(($elements) => {
      const price1 = parseFloat(
        $elements
          .eq(1)
          .text()
          .replace(/[^0-9.-]+/g, '')
      );
      cy.log(price1);
      const price3 = parseFloat(
        $elements
          .eq(3)
          .text()
          .replace(/[^0-9.-]+/g, '')
      );

      const totalPrice = parseFloat((price1 + price3).toFixed(2));
      cy.log(`Total Price: ${totalPrice}`);

      cy.get(this.totalPriceAmount)
        .eq(0)
        .invoke('text')
        .then((text) => {
          const comparisonPrice = parseFloat(text.replace(/[^0-9.-]+/g, ''));
          cy.log(`Comparison Price: ${comparisonPrice}`);

          expect(totalPrice).to.equal(comparisonPrice);
        });
    });
  }

  verifyTotalToday() {
    cy.get(this.totalPriceAmount)
      .eq(1)
      .invoke('text')
      .then((text) => {
        const value = parseFloat(text.replace(/[^0-9.-]+/g, ''));
        cy.log(value);
        expect(value).to.eq(0);
      });
  }

  verifyLimitsOnPaymentTermsAfterInvoicing() {
    cy.get(this.creditBarValues)
      .eq(1)
      .invoke('text')
      .then((text) => {
        const totalAmount = parseFloat(text.replace(/[^0-9.-]+/g, ''));
        cy.log(`Total Credit Limit: ${totalAmount}`);
        cy.get(this.creditBarValues)
          .eq(2)
          .invoke('text')
          .then((text) => {
            const usedBeforeRequest = parseFloat(text.replace(/[^0-9.-]+/g, ''));
            cy.log(`Used Before This Request: ${usedBeforeRequest}`);
            cy.get(this.memoSummaryValue)
              .eq(3)
              .invoke('text')
              .then((text) => {
                const thisRequestAmount = parseFloat(text.replace(/[^0-9.-]+/g, ''));
                cy.log(`This Request Amount or Order Amount: ${thisRequestAmount}`);
                const calculatedTotal = parseFloat((usedBeforeRequest + thisRequestAmount).toFixed(2));
                cy.log(`Calculated Total (Used Before + This Request): ${calculatedTotal}`);
                cy.get(this.creditBarValues)
                  .eq(3)
                  .invoke('text')
                  .then((text) => {
                    const expectedTotal = parseFloat(parseFloat(text.replace(/[^0-9.-]+/g, '')).toFixed(2));
                    cy.log(`Expected Total ${expectedTotal}`);
                    expect(calculatedTotal).to.eq(expectedTotal);
                    const difference = parseFloat((totalAmount - expectedTotal).toFixed(2));
                    cy.log(`Difference (Total Amount - Expected Total): ${difference}`);
                    cy.get(this.creditBarValues)
                      .eq(4)
                      .invoke('text')
                      .then((text) => {
                        const expectedDifference = parseFloat(text.replace(/[^0-9.-]+/g, ''));
                        cy.log(`Expected Difference (credit_widget_title eq(4)): ${expectedDifference}`);
                        expect(difference).to.eq(expectedDifference);
                      });
                  });
              });
          });
      });
  }
  changeCurrency() {
    cy.get(this.currency).click();
    cy.contains('US Dollar (USD)').click();
  }
}

export default Memo;
