/// <reference types="cypress" />
const path = require('path');

class Orders {
  constructor() {
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.hamBurgerMenu = '[data-automation-id="web--ham-btn"]';
    this.sideBarButton = '[data-automation-id="sidebar-menu-btn"]';
    this.orderDropDown = '[data-automation-id="select--inp"]';
    this.orderpageTitle = '[data-automation-id="header-title"]';
    this.selectOptionFromDropDown = '[data-automation-id="select-option"]';
    this.searchbox = '[data-automation-id="search-by-certificate-number-or-stockId"]';
    this.clipBoard = '[data-automation-id="copy__to__clipboard"]';
    this.selectAllCheckBox = '[data-automation-id="checkbox"]';
    this.exportButton = 'Export';
    this.trackButton = '[data-automation-id="track-btn"]';
    this.headerTitle = '[data-automation-id="track-item-header-title"]';
    this.meleeCaratsField = ':nth-child(3) > .melee_invoice_description__cell';
    this.meleePiecesField = ':nth-child(4) > .melee_invoice_description__cell';
  }

  searchOrderNumber(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((data) => {
      const orderNumber = data[0]?.ordernumber || data[0]?.certNumber || data.orderNumber;
      console.log(orderNumber);
      return cy
        .get(this.searchbox)
        .eq(1)
        .click()
        .type(orderNumber)
        .get(this.clipBoard, { timeout: 60000 })
        .eq(0)
        .click()
        .assertValueCopiedToClipboard(`${orderNumber}`)
        .then(() => {
          return orderNumber;
        });
    });
  }

  VerifyOrderDiamondPoRequestPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('PO Request').click();
    cy.url().should('include', 'diamonds/customer-orders/open-orders');
  }

  VerifyOrderDiamondActionRequiredPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Action Required').click();
    cy.url().should('include', 'diamonds/customer-orders/pending-orders');
  }

  VerifyOrderDiamondOnItsWayPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('On Its Way').click();
    cy.url().should('include', 'diamonds/customer-orders/on-way');
  }

  VerifyOrderDiamondDeliveredPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Delivered').click();
    cy.url().should('include', 'diamonds/customer-orders/delivered');
  }

  VerifyOrderDiamondSoldOutPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Sold out').click();
    cy.url().should('include', 'diamonds/customer-orders/not-available');
  }

  VerifyOrderDiamondCancelledPage() {
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Cancelled').click();
    cy.url().should('include', 'diamonds/customer-orders/cancelled');
  }

  VerifyAllOrdersMeleePage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.url().should('include', 'melee/customer-orders/all');
  }

  VerifyOrderMeleePoRequestPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('PO Request').click();
    cy.url().should('include', 'melee/customer-orders/open-orders');
  }

  VerifyOrderMeleeActionRequiredPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Action Required').click();
    cy.url().should('include', 'melee/customer-orders/pending-orders');
  }

  VerifyOrderMeleeOnItsWayPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('On Its Way').click();
    cy.url().should('include', 'melee/customer-orders/on-way');
  }

  VerifyOrderMeleeDeliveredPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Delivered').click();
    cy.url().should('include', 'melee/customer-orders/delivered');
  }

  VerifyOrderMeleeSoldOutPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Sold out').click();
    cy.url().should('include', 'melee/customer-orders/not-available');
  }

  VerifyOrderMeleeCancelledPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(1).contains('Melee').click();
    cy.get(this.orderDropDown).contains('Melee');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Cancelled').click();
    cy.url().should('include', 'melee/customer-orders/cancelled');
  }
  VerifyAllOrdersGemStonesPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.url().should('include', 'gemstones/customer-orders/all');
  }

  VerifyOrderGemStonesPoRequestPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('PO Request').click();
    cy.url().should('include', 'gemstones/customer-orders/open-orders');
  }

  VerifyOrderGemStonesActionRequiredPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Action Required').click();
    cy.url().should('include', 'gemstones/customer-orders/pending-orders');
  }

  VerifyOrderGemStonesOnItsWayPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('On Its Way').click();
    cy.url().should('include', 'gemstones/customer-orders/on-way');
  }

  VerifyOrderGemStonesDeliveredPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Delivered').click();
    cy.url().should('include', 'gemstones/customer-orders/delivered');
  }

  VerifyOrderGemStonesSoldOutPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Sold out').click();
    cy.url().should('include', 'gemstones/customer-orders/not-available');
  }

  VerifyOrderGemStonesCancelledPage() {
    cy.get(this.orderDropDown).contains('Diamonds').click();
    cy.wait(2000);
    cy.get(this.selectOptionFromDropDown).eq(2).contains('Gemstones').click({ force: true });
    cy.get(this.orderDropDown).contains('Gemstones');
    cy.get(this.orderDropDown, { timeout: 60000 }).contains('Gemstones');
    cy.get(this.orderDropDown).contains('All').click();
    cy.contains('Cancelled').click();
    cy.url().should('include', 'gemstones/customer-orders/cancelled');
  }

  exportOrders() {
    cy.get(this.clipBoard, { timeout: 60000 }).eq(0).click();
    cy.get(this.selectAllCheckBox, { timeout: 60000 }).eq(0).click();
    cy.contains(this.exportButton).click();
    cy.wait(2000);
    cy.getDownloadedFileName().then((fileName) => {
      const baseName = Cypress._.trimEnd(fileName, path.extname(fileName));
      cy.log('Downloaded file name:', baseName);
      cy.task('convertXlsxToJson', `cypress/downloads/${baseName}.xlsx`).then(() => {
        cy.readFixtureFile(`cypress/fixtures/${baseName}.json`).then((data) => {
          data.forEach((item) => {
            cy.assertValueCopiedToClipboard(item.OrderNumber);
          });
        });
      });
    });
  }
  trackOrder(fixtureFileName) {
    this.searchOrderNumber(fixtureFileName).then((orderNumber) => {
      cy.get(this.trackButton, { timeout: 5000 }).click();
      cy.get(this.headerTitle).should('be.visible').contains(orderNumber);
    });
  }

  verifyMeleeOrderQuantity(carats, pieces) {
    cy.get(this.meleeCaratsField, { timeout: 5000 }).should('be.visible').contains(carats);
    cy.get(this.meleePiecesField, { timeout: 5000 }).should('be.visible').contains(pieces);
  }
}

export default Orders;
