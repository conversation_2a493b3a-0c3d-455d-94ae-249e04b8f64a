/// <reference types="cypress" />
/// <reference types="cypress-downloadfile"/>

class Invoices {
  constructor() {
    // Locators
    this.searchInput = '[data-automation-id="search-input"]';
    this.invoiceTable = '[data-automation-id="invoices-table"]';
    this.invoiceAction = '[data-automation-id="invoice-action"]';
    this.status = '.pill_status_new';
    this.tablerow = '.table__row';
    this.supplierInvoiceStatusRow = '[data-testid="tab-item"]';
    this.payNowButton = '.sc-jOVcOr.bnGEVj';
    this.payNowWithCard = '.fivrnn';
    this.payNowNextButton = '.sc-bdVaJa.kHYcQM.btn.btn-wdc.nextBtn';
    this.payNowHeader = '.bank_details_modal_header--title';
    this.cardNumber = '#Field-numberInput';
    this.expiryDate = '#Field-expiryInput';
    this.ccv = '#Field-cvcInput';
    this.payButton = '.sc-bdVaJa.kHYcQM.btn.btn-wdc.sc-gldTML cnKssR';
    this.paymentConfirmationHeader = '.sc-gqjmRU.ZOoNJ.popup_title';
  }

  searchInvoice(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((invoiceData) => {
      const invoiceno = invoiceData[0].invoice;
      console.log(invoiceno);
      cy.get(this.searchInput, { timeout: 60000 })
        .should('be.visible')
        .and('be.enabled')
        .clear()
        .type(invoiceno)
        .should('have.value', invoiceno);
      cy.wait(3000);
      cy.get(this.invoiceTable, { timeout: 60000 }).should('be.visible').contains(invoiceno, { timeout: 10000 });

      console.log('Search Invoice Number:', invoiceno);
      return cy.wrap(invoiceno);
    });
  }

  viewInvoiceDetails(invoiceno) {
    cy.get(this.invoiceTable, { timeout: 60000 })
      .contains(invoiceno)
      .parentsUntil(this.invoiceTable)
      .find(this.invoiceAction)
      .eq(0)
      .should('be.visible')
      .click();

    cy.contains(invoiceno).should('be.visible');
  }

  downloadAndViewInvoice(fixtureFileName) {
    this.searchInvoice(fixtureFileName).then((invoiceno) => {
      cy.get(this.invoiceTable, { timeout: 60000 })
        .wait(3000)
        .contains(invoiceno)
        .parentsUntil(this.invoiceTable)
        .find(this.invoiceAction)
        .eq(1)
        .should('be.visible')
        .invoke('removeAttr', 'target')
        .invoke('removeAttr', 'rel')
        .then((downloadButton) => {
          const href = downloadButton.attr('href');
          cy.downloadFile(href, 'cypress/downloads', 'invoice.pdf').then(() => {
            cy.verifyInvoicePDF(invoiceno);
          });
        });
      this.viewInvoiceDetails(invoiceno);
    });
  }
  verifyStatusOfInvoice(statuses) {
    cy.get(this.status)
      .eq(0)
      .then(($status) => {
        const text = $status.text().trim();
        expect(statuses).to.include(text);
      });
  }

  accessStatusTab(subTab, url) {
    cy.wait(1000);
    cy.get(this.supplierInvoiceStatusRow, { timeout: 10000 }).should('be.visible').contains(subTab).click();
    cy.url({ timeout: 10000 }).should('include', url);
  }
  payInvoiceViaStripe() {
    cy.get(this.payNowButton).contains('Pay now').click();
    cy.get(this.payNowNextButton).click();
    cy.get(this.payNowHeader).contains('Pay now with card');
    cy.wait(300000);
    cy.get(this.payButton).click();
    cy.get(this.paymentConfirmationHeader).contains('Payment Confirmation');
    cy.contains('Thank you for your payment!');
  }
}

export default Invoices;
