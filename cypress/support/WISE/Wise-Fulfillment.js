/// <reference types="cypress" />

class WiseFulfillment {
  constructor() {
    // Login locators
    this.username = '#username';
    this.password = '#password';
    this.loginButton = '#kc-login';
    this.upload = '[data-testid="dropzone"] input[type="file"]';
    this.createWiseRequestButton = 'button:contains("Create WISE Request")';
    this.suppliersMultiselect = '#wise-suppliers-multiselect';
    this.suppliersOption = '#wise-suppliers-multiselect-option-0';
    this.submitButton = ':nth-child(2) > .css-6nr17y > .MuiButtonBase-root';
    this.successDialog = '.MuiDialogContent-root';
    this.successMessage = '.MuiDialogContent-root:contains("Request Generated Successfully")';
    this.requestNumberLabel = '.MuiDialogContent-root > :nth-child(3)';
    this.supplierNameLabel = '.MuiDialogContent-root > :nth-child(4)';
    this.stoneCountLabel = '.MuiDialogContent-root > :nth-child(5)';
    this.requestCreatedAtLabel = '.MuiDialogContent-root > :nth-child(6)';
    this.requestNumber = null;
  }

  visitFulfillmentPage() {
    cy.visit('https://wise-admin-lfg.dev.nivodaapi.net/fulfilment/READY_TO_COLLECT');
  }

  searchWiseRequest() {
    cy.readFile('cypress/fixtures/wise-request-number.json').then((data) => {
      const requestNumber = data.requestNumber;
      cy.get('.MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputSizeSmall.css-pryw3u').type(requestNumber);
      cy.get('.css-120nera > .MuiButton-root').eq(0).click();
      cy.get('.MuiTableRow-root > :nth-child(1) > .MuiTypography-root').contains(requestNumber);
    });
  }

  tagAndCollect() {
    cy.get(':nth-child(5) > .MuiStack-root > div > .MuiButtonBase-root')
      .should('be.visible')
      .should('have.text', 'Tag & Collect')
      .click();
    cy.get('.MuiSnackbar-root > .MuiPaper-root').should('be.visible');
    //   cy.get('.css-1x2z133').should('not.have',this.requestNumber)
  }

  gradeWiseStonesFirstStep() {
    cy.get('[href="/fulfilment/TO_GRADE"]').click();
    this.searchWiseRequest();
    cy.get(':nth-child(5) > .MuiStack-root > div > .MuiButtonBase-root').click();
    cy.get('.MuiSnackbar-root > .MuiPaper-root').should('be.visible');
    cy.get('.css-1neaw17 > .MuiTypography-subtitle1').should('be.visible').contains('Step 1: Select CVD/HPHT');
    cy.get('#labgrown-method').should('be.visible').click();
    cy.get('[data-testid="menu-item-HPHT"]').should('be.visible').click();
    cy.wait(3000);
    cy.get('[style="padding: 12px 32px;"]').should('be.enabled').click();
  }

  gradeWiseStonesSecondStep() {
    cy.get('#stone-type-select').should('be.visible').click();
    cy.get('[data-testid="menu-item-LABORATORY_GROWN"]')
      .should('be.visible')
      .contains('Laboratory grown diamond')
      .click();
    cy.get('#shape-select').click().type('Round');
    cy.contains('Round').click();
    cy.get('#cut-select').click();
    cy.get('[data-testid="menu-item-BRILLIANT"]').click();
    cy.get('#carat').type('1.50000');
    cy.wait(3000);
    cy.get('[style="padding: 12px 32px;"]').eq(1).should('be.enabled').click();
  }

  gradeWiseStonesThirdStep() {
    cy.get('[style="padding: 12px 20px;"]').should('be.enabled').contains('Fetch Measurements').click();
  }
}

export default WiseFulfillment;
