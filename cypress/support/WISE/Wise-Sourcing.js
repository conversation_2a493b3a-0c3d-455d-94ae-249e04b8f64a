/// <reference types="cypress" />

class WiseSourcing {
  constructor() {
    // Login locators
    this.username = '#username';
    this.password = '#password';
    this.loginButton = '#kc-login';
    this.upload = '[data-testid="dropzone"] input[type="file"]';
    this.createWiseRequestButton = 'button:contains("Create WISE Request")';
    this.suppliersMultiselect = '#wise-suppliers-multiselect';
    this.suppliersOption = '#wise-suppliers-multiselect-option-0';
    this.submitButton = ':nth-child(2) > .css-6nr17y > .MuiButtonBase-root';
    this.successDialog = '.MuiDialogContent-root';
    this.successMessage = '.MuiDialogContent-root:contains("Request Generated Successfully")';
    this.requestNumberLabel = '.MuiDialogContent-root > :nth-child(3)';
    this.supplierNameLabel = '.MuiDialogContent-root > :nth-child(4)';
    this.stoneCountLabel = '.MuiDialogContent-root > :nth-child(5)';
    this.requestCreatedAtLabel = '.MuiDialogContent-root > :nth-child(6)';
    this.requestNumber = null;
  }

  visitPage() {
    cy.visit('http://wise-admin-lfg.dev.nivodaapi.net/');
  }

  loginUsingUi(emailFilePath) {
    cy.fixture(emailFilePath).then((emailData) => {
      const email = emailData[0].email;
      cy.get(this.username).should('be.visible').type(email);
      cy.get(this.password).should('be.visible').type('Nivoda123');
      cy.get(this.loginButton).should('be.visible').click();
      cy.url().should('include', '/wise-sourcing', { timeout: 50000 });
    });
  }

  createWiseRequest() {
    cy.contains('Create WISE Request').should('be.visible');
    cy.get(this.suppliersMultiselect).should('be.visible').click();
    cy.get(this.suppliersOption).should('be.visible').click();
    this.uploadFile('Sourcing Files/Express/pear-repl-req.csv', 'Sourcing Files/Express/pear-sup-file.csv');
    this.verifySuccessDialog();
    this.extractRequestNumber();
  }

  uploadFile(inputFilePath1, inputFilePath2) {
    cy.get(this.upload).eq(1).selectFile(inputFilePath1, { force: true });
    cy.get(this.upload).eq(1).selectFile(inputFilePath2, { force: true });
    cy.get(this.submitButton).should('be.visible').click();
    this.verifySuccessDialog();
    this.extractRequestNumber();
  }

  verifySuccessDialog() {
    cy.get(this.successDialog).should('be.visible');
    cy.get(this.successMessage).should('be.visible');
    cy.get(this.requestNumberLabel).should('be.visible').and('contain', 'WISE Request Number');
    cy.get(this.supplierNameLabel).should('be.visible').and('contain', 'Supplier Name');
    cy.get(this.stoneCountLabel).should('be.visible').and('contain', 'Stone Count');
    cy.get(this.requestCreatedAtLabel).should('be.visible').and('contain', 'Request Created At');
  }

  extractRequestNumber() {
    cy.get(this.requestNumberLabel)
      .invoke('text')
      .then((text) => {
        const requestNumberMatch = text.match(/WIS-\d{4}-\d{6}/);
        if (requestNumberMatch) {
          this.requestNumber = requestNumberMatch[0];
          cy.log(`WISE Request Number extracted: ${this.requestNumber}`);
          cy.writeFile('cypress/fixtures/wise-request-number.json', { requestNumber: this.requestNumber });
        } else {
          cy.log(`Could not extract WISE Request Number from text: ${text}`);
          this.requestNumber = null;
        }
      });
  }
}

export default WiseSourcing;
