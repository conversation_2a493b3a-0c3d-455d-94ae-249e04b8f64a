/// <reference types="cypress" />

class ShowroomSharingOptions {
  constructor() {
    // Locators
    this.ShareButton = 'button[aria-haspopup="menu"][class*="rt-IconButton"]';
    this.sharingButtonList = 'button.rt-Button';
    this.container = '.sc-etwtAo';
    this.title = '.sc-jXQZqI.cdcofL';
    this.diamondDetailBody = '.sc-dphlzf. aqhAH';
  }
  verifyShareButton() {
    cy.get(this.ShareButton, { timeout: 60000 }).eq(0).should('be.visible');
  }
  verifyWhatsappSharingOption() {
    this.verifyShareButton();
    cy.get(this.ShareButton, { timeout: 60000 }).eq(0).click();
    cy.get(this.sharingButtonList, { timeout: 60000 }).contains('WhatsApp').should('be.visible');
  }
  verifyShareButtonInProduct() {
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.title, { timeout: 60000 }).eq(0).click();
    cy.get(this.diamondDetailBody, { timeout: 10000 }).invoke('text').should('not.be.empty');
    this.verifyShareButton();
    this.verifyWhatsappSharingOption();
  }
  verifyEmailSharingOption() {
    this.verifyShareButton();
    cy.get(this.ShareButton, { timeout: 60000 }).eq(0).click();
    cy.get(this.sharingButtonList, { timeout: 60000 }).contains('Email').should('be.visible');
  }
  verifyEmailSharingOptionInProduct() {
    cy.get(this.ShareButton, { timeout: 60000 }).eq(0).click();
    cy.get(this.title, { timeout: 60000 }).eq(0).click();
    this.verifyEmailSharingOption();
  }
}

export default ShowroomSharingOptions;
