/// <reference types="cypress" />
class ShowroomLogin {
  constructor() {
    this.email = '[data-automation-id="login-email-input"]';
    this.password = '[data-automation-id="login-password-input"]';
    this.submitButton = '[data-automation-id="login-submit"]';
    this.forgotPassword = '[data-automation-id="forget-passwordinput"]';
    this.welcomeText = 'div.rt-Flex';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.forgetPasswordEmail = '#forget-password-email';
    this.profileIcon = '[data-automation-id="profile-avatar-button"]';
    this.settingProfileButton = '[data-automation-id="Settings-profile-btn"]';
    this.consultationButton = '[data-automation-id="Consultations-profile-btn"]';
    this.closeProfileIcon = '[data-automation-id="close-profile-button"';
    this.expandButton = 'button.rt-IconButton';
    this.sideBarMenu = 'button[role="tab"]';
    this.showRoomCheckOutSettingBox = '.rt-Box';
    this.changeCheckOutPopUp = '.rt-Text';
    this.body = '.sc-cBOTKl.dOagOV';
    this.confirmButton = '.rt-BaseButton';
    this.removeCartPopUp = '.sc-csSMhA.dfXoPc';
    this.logoutButton = '[data-automation-id="Logout-profile-btn"]';
  }

  visitPage() {
    cy.visit(Cypress.env('showRoomUrl'));
  }

  loginUsingUi(emailFilePath) {
    cy.fixture(emailFilePath).then((emailData) => {
      const email = emailData[0].email;
      Cypress.env('loginEmail', email);
      cy.emptyShowroomCartItems();
      cy.emptyShowroomShortListItems();
      cy.emptyCartItems();
      cy.get(this.email).type(email);
      cy.get(this.password).type('Nivoda123');
      cy.get(this.submitButton).contains('Log in').click();
      cy.get(this.submitButton, { timeout: 180000 }).should('not.exist');
    });
  }

  loginAsCustomerAssertion() {
    cy.url().should('include', '/live');
    cy.contains('span', 'Welcome').should('be.visible');
    cy.contains(
      "Thank you for visiting our showroom! We're here to help you find the perfect piece of jewelry."
    ).should('be.visible');
  }
  resetPass(paramEmailField) {
    cy.get(this.forgotPassword).click();
    cy.get(this.spinner, { timeout: 60000 }).should('not.exist');
    cy.get(this.forgetPasswordEmail).type(paramEmailField);
    cy.get(this.resetPasswordButton).click();
  }
  accessSettings(sideBar, url) {
    cy.get(this.profileIcon).click();
    cy.get(this.settingProfileButton).contains('Settings').click();
    cy.get(this.expandButton).eq(1).click();
    cy.get(this.sideBarMenu).contains(sideBar).click();
    cy.url().should('include', url);
  }
}
export default ShowroomLogin;
