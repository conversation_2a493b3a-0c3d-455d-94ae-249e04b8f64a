import 'cypress-file-upload';
import 'cypress-downloadfile/lib/downloadFileCommand';
import <PERSON> from 'papaparse';

Cypress.Commands.add('readFixtureFile', (filePath) => {
  const updatedPath = filePath.startsWith('cypress/fixtures') ? filePath : `cypress/fixtures/${filePath}`;

  return cy.readFile(updatedPath);
});

Cypress.Commands.add('getDownloadedFileName', () => {
  return cy.task('getDownloadedFileName');
});
Cypress.Commands.add('assertValueCopiedToClipboard', (value) => {
  cy.window().then((win) => {
    win.navigator.clipboard.readText().then((text) => {
      expect(text).to.eq(value);
    });
  });
});

Cypress.Commands.add('verifyInvoicePDF', (invoiceNumber) => {
  const pdfFilePath = 'cypress/downloads/invoice.pdf';
  cy.task('readPdf', pdfFilePath).then((pdfContent) => {
    expect(pdfContent).to.include(invoiceNumber);
  });
});

Cypress.Commands.add('stubWindowOpen', () => {
  cy.window().then((win) => {
    cy.stub(win, 'open').as('windowOpen');
  });
});

Cypress.Commands.add('getOTP', (expectedTimestamp, maxRetries = 5, interval = 1000) => {
  const fetchOTP = (retries) => {
    return cy
      .task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT code, "createdAt"
        FROM "CodeConfirms"
        ORDER BY "createdAt" DESC
        LIMIT 1`
      })
      .then((result) => {
        const latestOTP = result.rows[0];
        const latestTimestamp = new Date(latestOTP.createdAt).getTime();
        const expectedTime = new Date(expectedTimestamp).getTime();

        if (latestTimestamp >= expectedTime || retries === 0) {
          cy.log('Fetched OTP:', latestOTP.code);
          return cy.wrap(latestOTP.code);
        } else {
          cy.log('Retrying to fetch OTP...');
          return cy.wait(interval).then(() => fetchOTP(retries - 1));
        }
      });
  };

  return fetchOTP(maxRetries);
});
Cypress.Commands.add('updateStockNumbers', (filePath, outputFilePath) => {
  cy.readFixtureFile(filePath, 'utf8').then((csvContent) => {
    const parsedCsv = Papa.parse(csvContent, { header: true });
    parsedCsv.data.forEach((row) => {
      row['Stock #'] = `Stock-${Math.floor(Math.random() * 1000000)}`;
    });
    const updatedCsv = Papa.unparse(parsedCsv.data);
    cy.writeFile(outputFilePath, updatedCsv, 'utf8');
  });
});
Cypress.Commands.add('emptyMemoWalletItems', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      DELETE FROM "MemoWallets"
      USING "Users"
      WHERE "MemoWallets"."UserId" = "Users".id
      AND "Users".email = '${email}';
    `
  });
});

Cypress.Commands.add('emptyShortListMemoWalletItems', () => {
  const email = Cypress.env('loginEmail');
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      DELETE FROM "ShortlistItems"
      USING "Users"
      WHERE "ShortlistItems"."UserId" = "Users".id
      AND "Users".email = '${email}'
      AND "ShortlistItems".is_memo = TRUE;
    `
  });
});

Cypress.Commands.add('emptyShortListCartItems', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `delete from "ShortlistItems"
    USING "Users"
      WHERE "ShortlistItems"."UserId" = "Users".id
      AND "Users".email = '${email}';`
  });
});
Cypress.Commands.add('emptyCartItems', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
     DELETE FROM "CartItems"
      USING "Users"
      WHERE "CartItems"."UserId" = "Users".id
      AND "Users".email = '${email}';
    `
  });
});

Cypress.Commands.add('spaceCodeTagging', (fixtureFileName) => {
  cy.fixture(fixtureFileName).then((body) => {
    const spaceCodeUrl = Cypress.env('spaceCodeApiUrl');
    cy.request({
      method: 'POST',
      url: spaceCodeUrl,
      body: body,
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((response) => {
      expect(response.body.status).to.eq(true);
      expect(response.body.message).to.eq('Tags set successfully');

      cy.log(JSON.stringify(response.body));
    });
  });
});
Cypress.Commands.add('getFormattedDate', () => {
  const date = new Date();
  const day = date.toLocaleString('en-GB', { day: '2-digit' });
  const month = date.toLocaleString('en-GB', { month: 'short' });
  const year = date.getFullYear();
  const formattedDate = `${day} ${month}, ${year}`;
  return cy.wrap(formattedDate);
});

Cypress.Commands.add('verifyOrderMethodIsCfm', () => {
  cy.fixture('ordernumber.json').then((data) => {
    const orderNumber = data.orderNumber;

    cy.log('Checking order number:', orderNumber);

    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
        SELECT order_method
        FROM "OrderItems"
        WHERE "order_number" = '${orderNumber}'
      `
    }).then((result) => {
      cy.log('DB Result:', result);
      const ordermethod = result.rows?.[0]?.order_method;
      expect(ordermethod, `Expected order_method for ${orderNumber}`).to.eq('cfm');
    });
  });
});

Cypress.Commands.add('getMeleeSrData', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT order_request_number
          FROM "SpecialRequests" sr
          WHERE NOT EXISTS (SELECT 1
                            FROM "OrderRequests" or2
                            where sr.order_request_number = or2.order_request_number)
            AND sr."closedAt" IS NULL
            AND sr.status='OPEN'
          ORDER BY sr."createdAt" DESC 
          LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/meleeSrData.json', result1.rows);
  });
});
Cypress.Commands.add('getcertnumber', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT "NaturalCertificates"."certNumber"  , "NaturalCertificates"."id" 
      FROM "Diamonds"
      INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
      WHERE "Diamonds"."OrderItemId" IS NULL 
      AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
      AND "Diamonds"."mine_of_origin" IS NOT NULL
      AND  "Diamonds"."availability" = 'AVAILABLE'
      AND "Diamonds"."CompanyId" Not IN (
          SELECT "SupplierId" FROM "VolumeDiscounts"
      )
      limit 1`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/cert.json', result1.rows);
  });
});
Cypress.Commands.add('getdiamondshortlistcerts', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND nc.verified = 'true'
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondshortlistcerts.json', result1.rows);
  });
});
Cypress.Commands.add('getdiamondshortlistcerts1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber",d.id AS  "diamondId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND nc.verified = 'true'
        ORDER BY d."supplierStockId"
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondshortlistcerts1.json', result1.rows);
  });
});
Cypress.Commands.add('getgemstonecert', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (nc."certNumber")
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Companies" c ON c.id=cs."CompanyId"
               INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                          FROM "CartItems") si ON d."id" = si."GemstoneId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND u."default_currency" = 'USD'
        AND c.supplier_status='LIVE'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND d.availability = 'AVAILABLE'
        AND d."dollarValue" <500
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemstonecert.json', result1.rows);
  });
});
Cypress.Commands.add('getnaturaldiamondcert', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                 LEFT JOIN "CFMOrderRequests" cr ON nc.id = cr."ProductId" 
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "CartItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'NaturalCertificate'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d."availability" = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND nc.verified = 'true'
          AND cs.accept_returns = true
          AND qc."CertificateId" IS NULL
          AND cr."ProductId" IS NULL
          ORDER BY RANDOM()
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/naturaldiamondcert.json', result1.rows);
  });
});
Cypress.Commands.add('getlabdiamondcert', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH filtered_data AS (SELECT lc."certNumber"
                       FROM "Users" u
                                INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                                INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                                INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                                LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "labId"
                                           FROM "CartItems") si ON d."id" = si."labId"
                       WHERE u."role" = 'SUPPLIER'
                         AND d."OrderItemId" IS NULL
                         AND d."CertificateType" = 'LabgrownCertificate'
                         AND u."default_currency" = 'USD'
                         AND cs."cert_details_enabled" = true
                         AND u."verifyStatus" = '4'
                         AND si."labId" IS NULL
                         AND lc.verified = 'true'
                         AND d.availability = 'AVAILABLE'
                       LIMIT 100
                        )
                        SELECT "certNumber"
                        FROM filtered_data
                        ORDER BY RANDOM()
                        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/labdiamondcert.json', result1.rows);
  });
});
Cypress.Commands.add('getdiamondstockid', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."NivodaStockId"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                          FROM "ShortlistItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'NaturalCertificate'
        AND d."HoldId" IS NULL
        AND d.availability='AVAILABLE'
        AND d.brown = 0
        AND d.green = 0
        AND d.milky = 0
        AND d."eyeClean" = 100
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified= 'true'
        ORDER BY nc."certNumber"
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondstockid.json', result1.rows);
  });
});
Cypress.Commands.add('getremovediamondstockid', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."NivodaStockId"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                          FROM "ShortlistItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'NaturalCertificate'
        AND d.availability='AVAILABLE'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified= 'true'
        AND d.brown=1
        ORDER BY nc."certNumber"
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/removediamondstockid.json', result1.rows);
  });
});
Cypress.Commands.add('getmeleeshortlistcerts', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT "NivodaStockId"
        FROM (SELECT DISTINCT mc."supplierStockId" AS "NivodaStockId", cs."createdAt"
              FROM "Users" u
                       INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                       INNER JOIN "MeleeCollections" mc ON cs."CompanyId" = mc."CompanyId"
                       LEFT JOIN (SELECT "OfferId",
                                         substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                                  FROM "ShortlistItems") si ON mc."id" = si."MeleeId"
              WHERE mc."OrderItemId" IS NULL
                AND mc."IsParentMeleeCollection" IS NULL
                AND mc."ParentMeleeCollectionId" IS NULL
                AND mc."type" = 'NATURAL'
                AND mc."pieces" IS NULL
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND mc."source" IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
                AND mc."status" = 'LIVE'
                AND mc."OrderItemId" IS NULL
              ORDER BY cs."createdAt" DESC) subquery
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/meleeshortlistcerts.json', result1.rows);
  });
});
Cypress.Commands.add('getSearchGemstone', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u."email" AS "email" , MIN(oi."order_number") AS "ordernumber" 
      FROM "Users" u
      INNER JOIN "Orders" o ON u."id" = o."UserId"
      INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId" 
      WHERE u."role" = 'CUSTOMER' 
      AND u."default_currency" = 'USD'  
      AND u."verifyStatus" = '4' 
      AND u."address_verified" = 'true'
      AND oi."ProductType" = 'Gemstone' 
      AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
      GROUP BY u."email", u."role", u.id, u.default_currency
      HAVING COUNT(u."email") = 1
      ORDER BY MIN(oi.order_number) ASC
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/SearchGemstone.json', result1.rows);
  });
});
Cypress.Commands.add('getSearchMelee', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u."email" AS "email" , MIN(oi."order_number") AS "ordernumber" 
      FROM "Users" u
      INNER JOIN "Orders" o ON u."id" = o."UserId"
      INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId" 
      WHERE u."role" = 'CUSTOMER' 
      AND u."default_currency" = 'USD'  
      AND u."verifyStatus" = '4' 
      AND u."address_verified" = 'true'
      AND oi."ProductType" = 'Melee' 
      AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
      GROUP BY u."email", u."role", u.id, u.default_currency
      LIMIT 1;`
  }).then((result1) => {
    const modifiedResult = result1.rows.map((row) => ({
      ...row,
      ordernumber: row.ordernumber.replace(/-1$/, '')
    }));
    cy.writeFile('cypress/fixtures/SearchMelee.json', modifiedResult);
  });
});
Cypress.Commands.add('getSearchdiamond', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u."email" AS "email", MIN(oi."order_number") AS "ordernumber"
        FROM "Users" u
                 INNER JOIN "Orders" o ON u."id" = o."UserId"
                 INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId"
        WHERE u."role" = 'CUSTOMER'
          AND u."default_currency" = 'USD'
          AND u."verifyStatus" = '4'
          AND u."address_verified" = 'true'
          AND oi."ProductType" = 'Diamond'
          AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
        GROUP BY u."email", u."role", u.id, u.default_currency
        HAVING COUNT(u."email") = 1
        ORDER BY MIN(oi.order_number) ASC
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/Searchdiamond.json', result1.rows);
  });
});
Cypress.Commands.add('getdiamondonhold', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH shortlisted_diamonds
         AS (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "DiamondId"
             FROM "ShortlistItems"),
            filtered_diamonds AS (SELECT d."CertificateId", d."NivodaStockId", nc."certNumber"
                                  FROM "Diamonds" d
                                            JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                                            JOIN "CompanySettings" cs ON d."CompanyId" = cs."CompanyId"
                                            JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                                  WHERE u."role" = 'SUPPLIER'
                                    AND u."default_currency" = 'USD'
                                    AND u."verifyStatus" = '4'
                                    AND cs."accept_holds" = 'true'
                                    AND cs."cert_details_enabled" = 'true'
                                    AND d."OrderItemId" IS NULL
                                    AND d."HoldId" IS NULL
                                    AND d."CertificateType" = 'NaturalCertificate'
                                    AND d."availability" = 'AVAILABLE'
                                    AND d."brown" = 0
                                    AND d."green" = 0
                                    AND d."milky" = 0
                                    AND d."eyeClean" = 100
                                    AND nc."ImageId" IS NOT NULL
                                    AND nc."VideoId" IS NOT NULL
                                    AND nc."verified" = 'true'
                                    AND d."id" NOT IN (SELECT "DiamondId" FROM shortlisted_diamonds))
        SELECT "certNumber", "NivodaStockId"
        FROM filtered_diamonds
        ORDER BY random()
        OFFSET 200 LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondonhold.json', result1.rows);
  });
});
Cypress.Commands.add('getnaturaldiamondholdrequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."CertificateId",cs.self_serve_holds
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "OrderRequests" o ON nc."certNumber" = o."certNumber"
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND o."certNumber" IS NULl
              AND cs.self_serve_holds IS NULL
              LIMIT 1;`
  }).then((result) => {
    const data = result.rows[0];
    if (data) {
      cy.writeFile('cypress/fixtures/naturaldiamondholdrequest.json', [data]);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
      });
    }
  });
});
Cypress.Commands.add('getnaturaldiamondinforequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT DISTINCT nc."certNumber", d."CertificateId"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "OrderRequests" o ON nc."certNumber" = o."certNumber"
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND o."certNumber" IS NULL
              ORDER BY d."CertificateId"
              LIMIT 1;`
  }).then((result) => {
    const data = result.rows[0];
    if (data) {
      cy.writeFile('cypress/fixtures/naturaldiamondinforequest.json', [data]);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
      });
    }
  });
});
Cypress.Commands.add('getnaturaldiamondbuyrequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."CertificateId"
      FROM "Diamonds" d
      JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
      JOIN "CompanySettings" cs ON d."CompanyId" = cs."CompanyId"
      LEFT JOIN "OrderRequests" o ON o."certNumber" = nc."certNumber"
      WHERE cs."cert_details_enabled" = TRUE
        AND o."certNumber" IS NULL
        AND d."CertificateId" IS NOT NULL
        AND d."CompanyId" IN (
          SELECT DISTINCT "CompanyId"
          FROM "Users"
          WHERE "role" = 'SUPPLIER'
            AND "verifyStatus" = '4'
        )
      LIMIT 1;
    `
  }).then((result) => {
    const data = result.rows[0];
    if (data) {
      cy.writeFile('cypress/fixtures/naturaldiamondbuyrequest.json', [data]);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `DELETE FROM "Diamonds" WHERE "CertificateId" = '${data.CertificateId}';`
      });
    }
  });
});
Cypress.Commands.add('getlabgrowndiamondrequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber" , d."CertificateId"
              FROM "Users" u
              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
              INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
              INNER JOIN "LabgrownCertificates" nc ON d."CertificateId" = nc."id"
              WHERE u."role" = 'SUPPLIER'
                AND d.availability = 'NOT_AVAILABLE'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND nc.verified= 'true'
              LIMIT 1;`
  }).then((result) => {
    const data = result.rows[0];
    if (data) {
      cy.writeFile('cypress/fixtures/labgrowndiamondrequest.json', [data]);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
      });
    }
  });
});
Cypress.Commands.add('getdiamondgroupdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."NivodaStockId"
          FROM "Users" u
                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                   INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                   INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                   LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                              FROM "ShortlistItems") si ON d."id" = si."DiamondId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND d.availability='AVAILABLE'
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND cs."accept_holds" = true
            AND u."verifyStatus" = '4'
            AND nc.verified= 'true'
            AND si."OfferId" IS NULL
          ORDER BY d."NivodaStockId"
          LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondgroupdata.json', result1.rows);
  });
});
Cypress.Commands.add('getmeleegroupdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT DISTINCT d."supplierStockId" AS "NivodaStockId", d."createdAt"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                 LEFT JOIN (SELECT "OfferId",
                                   substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                            FROM "ShortlistItems") si ON d."id" = si."MeleeId"
        WHERE d."OrderItemId" IS NULL
          AND d."IsParentMeleeCollection" IS NULL
          AND d."ParentMeleeCollectionId" IS NULL
          AND d."type" = 'NATURAL'
          AND d.pieces IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.status = 'LIVE'
          AND d.source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
          AND d."createdAt" > (NOW() - INTERVAL '500 DAY')
        ORDER BY d."createdAt" DESC, d."supplierStockId" DESC
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/meleegroupdata.json', result1.rows);
  });
});
Cypress.Commands.add('getgemsgroupdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber",
          CONCAT(
                  INITCAP(nc."gemType"), ' ',
                  LOWER(nc.color), ' ',
                  LOWER(nc.shape), ' ',
                  nc.carats
          ) AS "description"
            FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Companies" c ON c.id=cs."CompanyId"
                      INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                                FROM "ShortlistItems") si ON d."id" = si."GemId"
            WHERE u."role" = 'SUPPLIER'
              AND d."OrderItemId" IS NULL
              AND d."HoldId" IS NULL
              AND u."default_currency" = 'USD'
              AND cs."cert_details_enabled" = true
              AND u."verifyStatus" = '4'
              AND c.supplier_status='LIVE'
              AND si."OfferId" IS NULL
              AND d.availability = 'AVAILABLE'
              order by nc."certNumber"
            LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemsgroupdata.json', result1.rows);
  });
});
Cypress.Commands.add('getgemsupfrontdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                            FROM "ShortlistItems") si ON d."id" = si."GemId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."HoldId" IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND c.supplier_status = 'LIVE'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
        order by d."CompanyId" desc
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemsupfrontdata.json', result1.rows);
  });
});
Cypress.Commands.add('getgemsshortlistcert', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH random_selection AS (SELECT nc."certNumber"
                          FROM "Users" u
                                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                                   INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                                   INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                                   INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                                   LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemId"
                                              FROM "ShortlistItems") si ON d."id" = si."GemId"
                          WHERE u."role" = 'SUPPLIER'
                            AND d."OrderItemId" IS NULL
                            AND d."HoldId" IS NULL
                            AND u."default_currency" = 'USD'
                            AND cs."cert_details_enabled" = true
                            AND u."verifyStatus" = '4'
                            AND c.supplier_status = 'LIVE'
                            AND si."GemId" IS NULL
                            AND d.availability = 'AVAILABLE')
                            SELECT "certNumber"
                            FROM random_selection
                            ORDER BY RANDOM()
                            LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemsshortlistcert.json', result1.rows);
  });
});
Cypress.Commands.add('getaddtocartgemsdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH shortlisted_gems AS (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemId"
                          FROM "ShortlistItems")
              SELECT nc."certNumber",
                    CONCAT(INITCAP(nc."gemType"), ' ', LOWER(nc.color), ' ', LOWER(nc.shape), ' ', nc.carats) AS "description"
              FROM "Users" u
                      JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      JOIN "Companies" c ON c.id = cs."CompanyId" AND c.supplier_status = 'LIVE'
                      JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                      JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN shortlisted_gems si ON d."id" = si."GemId"
              WHERE u."role" = 'SUPPLIER'
                AND u."default_currency" = 'USD'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND d."OrderItemId" IS NULL
                AND d."HoldId" IS NULL
                AND d.availability = 'AVAILABLE'
                AND si."GemId" IS NULL
              ORDER BY nc."certNumber" DESC
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/addtocartgemsdata.json', result1.rows);
  });
});
Cypress.Commands.add('getremovegemsdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT MAX(nc."certNumber") AS "certNumber",
        CONCAT(
                INITCAP(nc."gemType"), ' ',
                LOWER(nc.color), ' ',
                LOWER(nc.shape), ' ',
                nc.carats
        )                    AS "description"
 FROM "Users" u
          INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
          INNER JOIN "Companies" c ON c.id=cs."CompanyId"
          INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
          INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
          LEFT JOIN (SELECT "OfferId",
                            substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                     FROM "ShortlistItems") si ON d."id" = si."GemId"
 WHERE u."role" = 'SUPPLIER'
   AND d."OrderItemId" IS NULL
   AND d."HoldId" IS NULL
   AND u."default_currency" = 'USD'
   AND cs."cert_details_enabled" = true
   AND u."verifyStatus" = '4'
   AND c.supplier_status='LIVE'
   AND si."OfferId" IS NULL
   AND d.availability = 'AVAILABLE'
 GROUP BY INITCAP(nc."gemType"),
          LOWER(nc.color),
          LOWER(nc.shape),
          nc.carats
 LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/removegemsdata.json', result1.rows);
  });
});
Cypress.Commands.add('getremovemeleedata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT (d."supplierStockId") AS "NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                            FROM "ShortlistItems") si ON d."id" = si."MeleeId"
        WHERE d."OrderItemId" IS NULL
          AND d."IsParentMeleeCollection" is NULL
          AND d."ParentMeleeCollectionId" is NULL
          AND d."type" = 'NATURAL'
          AND d.pieces IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.status = 'LIVE'
          AND source in ('CONSIGNMENT', 'SOURCE_TO_ORDER')
        ORDER BY cs."CompanyId"
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/removemeleedata.json', result1.rows);
  });
});
Cypress.Commands.add('getdiamondpairdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."DiamondId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND nc.verified = 'true'
                AND cs."accept_returns" = true
              ORDER BY nc."certNumber" DESC
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/diamondpairdata.json', result1.rows);
  });
});
Cypress.Commands.add('getlabdiamondpairdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT lc."certNumber"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "labId"
                          FROM "CartItems") si ON d."id" = si."labId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'LabgrownCertificate'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND lc.verified= 'true'
        and d.availability = 'AVAILABLE'
        ORDER BY lc."certNumber" ASC
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/labdiamondpairdata.json', result1.rows);
  });
});
Cypress.Commands.add('getshortlistlabdiamond', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT lc."certNumber", d."NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "labId"
                            FROM "CartItems") si ON d."id" = si."labId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'LabgrownCertificate'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND lc.verified = 'true'
          AND d.availability = 'AVAILABLE'
          ORDER BY cs."CompanyId" DESC
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/shortlistlabdiamond.json', result1.rows);
  });
});
Cypress.Commands.add('getshortlistlabdiamond1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH random_diamond AS (SELECT d."id"
        FROM "Diamonds" d
                 TABLESAMPLE SYSTEM (1)
        WHERE d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE')
        SELECT lc."certNumber", d."NivodaStockId"
        FROM random_diamond rd
        JOIN "Diamonds" d ON d."id" = rd."id"
        JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
        JOIN "Users" u ON u."CompanyId" = d."CompanyId"
        JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
        LEFT JOIN (SELECT substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND cs."accept_holds" = true
        AND u."verifyStatus" = '4'
        AND lc.verified = 'true'
        AND si."DiamondId" IS NULL
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/shortlistlabdiamond1.json', result1.rows);
  });
});
Cypress.Commands.add('getshortlistlabdiamondpair', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT lc."certNumber", d."NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND lc.verified= 'true'
          AND si."OfferId" IS NULL
        ORDER BY lc."certNumber"
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/shortlistlabdiamondpair.json', result1.rows);
  });
});
Cypress.Commands.add('getmultiplestonepair', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH filtered_data AS (SELECT d."CertificateId"
        FROM "Users" u
                 JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT DISTINCT (substring("OfferId" FROM position('/' IN "OfferId") + 1))::uuid AS diamond_id
                            FROM "CartItems") si ON d."CertificateId" = si.diamond_id
        WHERE u."role" = 'SUPPLIER'
          AND u."default_currency" = 'USD'
          AND u."verifyStatus" = '4'
          AND cs."cert_details_enabled" = true
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'LabgrownCertificate'
          AND d."availability" = 'AVAILABLE'
          AND lc."verified" = 'true'
          AND si.diamond_id IS NULL
        LIMIT 1000 
        )
        SELECT lc."certNumber"
        FROM "LabgrownCertificates" lc
        JOIN filtered_data fd ON lc."id" = fd."CertificateId"
        ORDER BY RANDOM()
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/multiplestonepair.json', result1.rows);
  });
});
Cypress.Commands.add('getinvoicedata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u.email AS email, i.invoice_number as invoice
        FROM "Users" u
                 INNER JOIN "Orders" o ON u.id = o."UserId"
                 INNER JOIN "Companies" c ON c.id = u."CompanyId"
                 INNER JOIN "OrderItems" oi ON o.id = oi."OrderId"
                 INNER JOIN "InvoiceOrderItems" ioi ON oi.id = ioi."OrderItemId"
                 INNER JOIN "Invoices" i ON ioi."InvoiceId" = i.id
        WHERE i.type = 'sell'
          AND i.status='PAID'
          AND i.invoice_sent IS NOT NULL
          AND i.invoiced_at IS NOT NULL
        GROUP BY u.email, i.invoice_number, i."createdAt", i.status, i.type
        ORDER BY i."createdAt" DESC
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/invoicedata.json', result1.rows);
  });
});
Cypress.Commands.add('getaccepttherequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d."CertificateId"
                FROM "Diamonds" d
                JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                WHERE d."OrderItemId" IS NULL
                  AND d."CertificateType" = 'NaturalCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND d."FbnId" IS NULL
                  AND u."id" IN (
                    SELECT DISTINCT u2."id"
                    FROM "Diamonds" d2
                    JOIN "CompanySettings" cs2 ON cs2."CompanyId" = d2."CompanyId"
                    JOIN "Users" u2 ON u2."CompanyId" = cs2."CompanyId"
                    WHERE d2."OrderItemId" IS NULL
                      AND d2."CertificateType" = 'NaturalCertificate'
                      AND d2."availability" = 'AVAILABLE'
                      AND d2."FbnId" IS null
                  )
                  AND NOT EXISTS (
                    SELECT 1
                    FROM "ExpressRequestItems" eri
                    WHERE eri."CertificateId" = d."CertificateId"
                  )
                  LIMIT 3 OFFSET 40;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/accepttherequest.json', result1.rows);
  });
});
Cypress.Commands.add('getcreditnotestone', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
                  FROM "NaturalCertificates" nc
                          INNER JOIN "Diamonds" d ON d."CertificateId" = nc."id"
                          INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                          INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                          LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                  WHERE u."role" = 'SUPPLIER'
                    AND u."default_currency" = 'INR'
                    AND u."verifyStatus" = '4'
                    AND d."OrderItemId" IS NULL
                    AND d."CertificateType" = 'NaturalCertificate'
                    AND d.brown = 0
                    AND d.green = 0
                    AND d.milky = 0
                    AND d."eyeClean" = 100
                    AND d."availability" = 'AVAILABLE'
                    AND nc.verified = 'true'
                    AND qc."CertificateId" IS NULL
                    AND cs."cert_details_enabled" = true
                    AND cs.self_serve_po IS NULL
                    AND cs.accept_returns = true
                    AND u.geo_country = 'IN'
                  ORDER BY cs."CompanyId"
                  LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/creditnotestone.json', result1.rows);
  });
});
Cypress.Commands.add('getcreateinvoice', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (nc."certNumber"), nc.id
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND nc."ImageId" Is Not Null
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND u."geo_country" <> 'TH'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY nc."certNumber"
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/createinvoice.json', result1.rows);
  });
});
Cypress.Commands.add('setupGradingFilesFetcherService', () => {
  const defaultTimeout = 30000;

  cy.readFile('cypress/fixtures/wise-request-number.json', { timeout: defaultTimeout })
    .should('exist')
    .then((data) => {
      cy.wrap(data).should('have.property', 'requestNumber');
      const currentRequestNumber = data.requestNumber.trim();
      cy.wrap(currentRequestNumber).should('not.be.empty');

      const servicePath = '/Users/<USER>/Desktop/grading-files-fetcher-service';
      const stagingPath = `${servicePath}/staging`;

      cy.log(`Setting up grading files fetcher service for request: ${currentRequestNumber}`);

      // First, find the existing folder in staging directory
      cy.exec(`ls -1 ${stagingPath}`, { timeout: defaultTimeout }).then((result) => {
        const existingFolders = result.stdout
          .trim()
          .split('\n')
          .filter((folder) => folder.startsWith('WIS-'));

        if (existingFolders.length === 0) {
          throw new Error('No WISE request folders found in staging directory');
        }

        const oldFolderName = existingFolders[0]; // Take the first WISE folder
        const oldFolderPath = `${stagingPath}/${oldFolderName}`;
        const newFolderPath = `${stagingPath}/${currentRequestNumber}`;

        cy.log(`Renaming folder from ${oldFolderName} to ${currentRequestNumber}`);

        // Rename the folder
        cy.exec(`mv "${oldFolderPath}" "${newFolderPath}"`, { timeout: defaultTimeout }).then(() => {
          cy.log(`Successfully renamed folder to ${currentRequestNumber}`);

          // Find and rename the txt file inside the renamed folder
          cy.exec(`find ${newFolderPath} -maxdepth 1 -name "*.txt" -type f`, { timeout: defaultTimeout }).then(
            (txtResult) => {
              const txtFiles = txtResult.stdout
                .trim()
                .split('\n')
                .filter((file) => file.length > 0);

              if (txtFiles.length === 0) {
                throw new Error('No txt files found in the request folder');
              }

              const oldTxtFile = txtFiles[0]; // Take the first txt file
              const newTxtFile = `${newFolderPath}/${currentRequestNumber}.txt`;

              cy.log(`Renaming txt file to ${currentRequestNumber}.txt`);

              // Rename the txt file
              cy.exec(`mv "${oldTxtFile}" "${newTxtFile}"`, { timeout: defaultTimeout }).then(() => {
                cy.log(`Successfully renamed txt file to ${currentRequestNumber}.txt`);

                // Start the server in background using spawn
                cy.log('Starting grading-files-fetcher-service server...');
                cy.exec(`osascript -e 'tell app "Terminal" to do script "cd ${servicePath} && npm start"'`, {
                  timeout: 5000,
                  failOnNonZeroExit: false
                }).then(() => {
                  cy.log('Server start command executed in new terminal');

                  // Wait for server to fully start
                  cy.wait(15000);

                  // Verify server is running by checking if it responds with retries
                  const checkServer = (retries = 3) => {
                    if (retries === 0) {
                      cy.log('Server verification failed after retries, but continuing with test');
                      return;
                    }

                    cy.request({
                      url: 'http://localhost:3000',
                      failOnStatusCode: false,
                      timeout: 5000
                    }).then((response) => {
                      if (response.status === 200) {
                        cy.log('Server is running successfully');
                      } else {
                        cy.log(`Server not ready, retrying... (${retries} attempts left)`);
                        cy.wait(3000).then(() => checkServer(retries - 1));
                      }
                    });
                  };

                  checkServer();
                });
              });
            }
          );
        });
      });
    });
});

Cypress.Commands.add('getnivodaexpressstone', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT lc."certNumber"
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        inner join "FbnItems" fi on fi.id = d."FbnId"
                  AND d."CertificateType" =  'LabgrownCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND cs."cert_details_enabled" = true
                  AND d."FbnId" is not null
                  AND oi."FbnType" = 'INTERNAL'
                  AND d."OrderItemId" is null
                  AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                  AND fi.internal_status = 'ACTIVE'
                  AND cs.self_serve_po IS NULL
                LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/nivodaexpressstone.json', result1.rows);
  });
});
Cypress.Commands.add('getgemsdata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (nc."certNumber"), c.name
          FROM "Users" u
                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                   INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                   INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                   INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                   LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                              FROM "CartItems") si ON d."id" = si."GemstoneId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND u."default_currency" = 'USD'
            AND c.supplier_status = 'LIVE'
            AND cs."cert_details_enabled" = true
            AND u."verifyStatus" = '4'
            AND si."OfferId" IS NULL
            AND d.availability = 'AVAILABLE'
            AND d."dollarValue" < 500
          ORDER BY c.name
          LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemsdata.json', result1.rows);
  });
});
Cypress.Commands.add('getincentivepayaipuser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u.email, cf."ACCOUNT_LIMIT_CONSUMED",
        '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                  INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                  INNER JOIN "Companies" c ON c.id = u."CompanyId"
                  LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'ip_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = true
          AND cf."MARKET_PAY_ACTIVE" = false
          AND cf."MPF_CHARGED" = true
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.subtype = 'PLATFORM_AND_CFM'
          AND u."default_currency" = 'USD'
          AND ck.kyc_verified = false
          AND c."createdAt" < '2023-06-16'
        ORDER BY u.email
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/incentivepayaipuser.json', result1.rows);
  });
});
Cypress.Commands.add('getmultipleaddressstone1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber", d.id AS  "diamondId"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'INR'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND nc.verified = 'true'
                AND qc."CertificateId" IS NULL
                AND geo_country = 'IN'
                AND cs.self_serve_po IS NULL
                ORDER BY NC."certNumber"
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/multipleaddressstone1.json', result1.rows);
  });
});
Cypress.Commands.add('getmultipleaddressstone2', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'INR'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND nc.verified = 'true'
                AND qc."CertificateId" IS NULL
                AND geo_country = 'IN'
                AND cs.self_serve_po IS NULL
                ORDER BY NC."certNumber" DESC
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/multipleaddressstone2.json', result1.rows);
  });
});
Cypress.Commands.add('getgemsdata1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT "certNumber", name
                FROM (SELECT DISTINCT nc."certNumber", c.name, RANDOM() as rand
                      FROM "Users" u
                              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                              INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                              INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                              INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                              LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemstoneId"
                                          FROM "CartItems") si ON d."id" = si."GemstoneId"
                      WHERE u."role" = 'SUPPLIER'
                        AND d."OrderItemId" IS NULL
                        AND u."default_currency" = 'USD'
                        AND c.supplier_status = 'LIVE'
                        AND cs."cert_details_enabled" = true
                        AND u."verifyStatus" = '4'
                        AND si."GemstoneId" IS NULL
                        AND d.availability = 'AVAILABLE'
                        AND d."dollarValue" < 500) AS subquery
                ORDER BY rand
                LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/gemsdata1.json', result1.rows);
  });
});
Cypress.Commands.add('getupfrontpayorder', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT lc."certNumber",
                d."CertificateId",
                c."name"
          FROM "Users" u
                  INNER JOIN
              "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                  INNER JOIN
              "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                  INNER JOIN
              "Companies" c ON u."CompanyId" = c."id"
                  INNER JOIN
              "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                  LEFT JOIN (SELECT "OfferId",
                                    substring("OfferId" FROM position('/' in "OfferId") + 1)::uuid AS "labId"
                              FROM "CartItems") si ON d."id" = si."labId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND d."CertificateType" = 'LabgrownCertificate'
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND cs."accept_holds" = 'true'
            AND u."verifyStatus" = '4'
            AND si."OfferId" IS NULL
            AND lc."verified" = 'true'
            AND d."availability" = 'AVAILABLE'
            AND c."name" NOT LIKE 'xxxx%'
          LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/upfrontpayorder.json', result1.rows);
  });
});
Cypress.Commands.add('getmeleeaddtocart', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH ranked_data AS (SELECT mc."supplierStockId" AS "NivodaStockId",
                            ROW_NUMBER() OVER () AS rn
                     FROM "Users" u
                              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                              INNER JOIN "MeleeCollections" mc ON cs."CompanyId" = mc."CompanyId"
                              LEFT JOIN (SELECT DISTINCT (substring("OfferId" FROM position('/' IN "OfferId") + 1))::uuid AS "MeleeId"
                                         FROM "CartItems") ci ON mc."id" = ci."MeleeId"
                     WHERE mc."OrderItemId" IS NULL
                       AND mc."IsParentMeleeCollection" IS NULL
                       AND mc."ParentMeleeCollectionId" IS NULL
                       AND mc.type='NATURAL'
                       AND cs."cert_details_enabled" = true
                       AND u."verifyStatus" = '4'
                       AND ci."MeleeId" IS NULL
                       AND mc.location  = '729b2fda-b46d-45f3-ad91-1124d6465064'
                       AND mc."source" IN ('CONSIGNMENT')
                       AND mc."status" = 'LIVE'),
     random_row AS (SELECT "NivodaStockId"
                    FROM ranked_data
                    WHERE rn = (SELECT floor(random() * count(*) + 1) FROM ranked_data))
SELECT "NivodaStockId"
FROM random_row;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/meleeaddtocart.json', result1.rows);
  });
});
Cypress.Commands.add('getincentivepaylegacyuser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u.email,
        '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                  INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                  INNER JOIN "Companies" c ON c.id = u."CompanyId"
                  LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'ip_payment'
          AND cf."ALL_IN_PRICING" = false
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = true
          AND cf."MARKET_PAY_ACTIVE"= false
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country IS NOT NULL
          AND u.subtype = 'PLATFORM_AND_CFM'
          AND ck.kyc_verified = false
        ORDER BY u.email DESC
        LIMIT 1`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/incentivepaylegacyuser.json', result1.rows);
  });
});
Cypress.Commands.add('getincentivepayorderaip', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (nc."certNumber"), d."CompanyId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY d."CompanyId"
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/incentivepayorderaip.json', result1.rows);
  });
});
Cypress.Commands.add('getcreateinvoice1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (nc."certNumber"), nc.id , c.name
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND u."geo_country" <> 'TH'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY nc."certNumber" DESC
        LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/createinvoice1.json', result1.rows);
  });
});
Cypress.Commands.add('getPayInvoice1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT DISTINCT ON (i."invoice_number") 
        i."invoice_number" AS invoice, 
        u.email AS email,
        c."name" AS name,
        i."createdAt",
        i."invoice_sent"
      FROM "Invoices" i
      INNER JOIN "Companies" cs ON i."to" = cs."id"
      INNER JOIN "Users" u ON cs."id" = u."CompanyId"
      INNER JOIN "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN "CompanyFinanceSettings" cf ON u."CompanyId" = cf."CompanyId"
      INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
      WHERE 
        u."verifyStatus" = 4
        AND u."role" = 'CUSTOMER'
        AND u."status" = '4'
        AND u."address_verified" = true
        AND u."LocationId" IS NOT NULL
        AND u."geo_country" IS NOT NULL
        AND ck."kyc_verified" = true
        AND i."invoice_sent" IS NULL
        AND i."createdAt" > '2024-01-01 23:28:06.363+05:00'
        AND (
          i."invoice_number" LIKE 'US%' 
          OR i."invoice_number" LIKE 'Uk%'
        )
      ORDER BY i."invoice_number"
      LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/PayInvoice1.json', result1.rows);
  });
});

Cypress.Commands.add('getPayInvoice', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH CompanyDetails AS (SELECT u.email, c."name"
                        FROM "CompanyFinanceSettings" cf
                                 INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                                 INNER JOIN "Companies" c ON u."CompanyId" = c.id
                        WHERE cf."ADVANCE_PAYMENT" = false
                          AND cf."DISABLE_CHECKOUT" = false
                          AND cf."ALL_IN_PRICING" = true
                          AND cf."ACCOUNT_LIMIT" > 20000
                          AND cf."CREDIT_LIMIT" > 20000
                          AND cf."IP_ACTIVE" = false
                          AND u."verifyStatus" = 4
                          AND u."role" = 'CUSTOMER'
                          AND u."status" = '4'
                          AND u."address_verified" = true
                          AND u."LocationId" IS NOT NULL
                          AND u."geo_country" IS NOT NULL
                          AND u."subtype" = 'PLATFORM_AND_CFM')
SELECT i."invoice_number" AS invoice,
       u.email            AS email,
       cd."name"          AS "name"
FROM "Invoices" i
         INNER JOIN "Companies" cs ON i."to" = cs."id"
         INNER JOIN "Users" u ON cs."id" = u."CompanyId"
         INNER JOIN CompanyDetails cd ON u.email = cd.email
WHERE i."status" is NULL
  AND i."invoice_sent" is NULL
GROUP BY i."invoice_number", u.email, cd."name"
order by i.invoice_number desc
LIMIT 1;
          `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/PayInvoice.json', result1.rows);
  });
});
Cypress.Commands.add('getPayPartialInvoice', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT DISTINCT ON (i."invoice_number") 
              i."invoice_number" AS invoice, 
              u.email AS email,
              c."name" AS name,
              i."createdAt"
            FROM "Invoices" i
            INNER JOIN "Companies" cs ON i."to" = cs."id"
            INNER JOIN "Users" u ON cs."id" = u."CompanyId"
            INNER JOIN "Companies" c ON u."CompanyId" = c.id 
            INNER JOIN "CompanyFinanceSettings" cf ON u."CompanyId" = cf."CompanyId"
            INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE 
              u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = true
              AND u."LocationId" IS NOT NULL
              AND u."geo_country" IS NOT NULL
              AND ck."kyc_verified" = true
              AND (i."status" = 'PARTIAL_PAID' OR i."status" IS NULL)
              and i."type" <> 'CREDIT_NOTE'
              and i."invoice_number" NOT LIKE 'PF%'

            ORDER BY 
              i."invoice_number"
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/PayPartialInvoice.json', result1.rows);
  });
});
Cypress.Commands.add('getcreditNoteDiamond', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT nc."certNumber"
            FROM "NaturalCertificates" nc
                    INNER JOIN "Diamonds" d ON d."CertificateId" = nc."id"
                    INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                    INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                    LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
            WHERE u."role" = 'SUPPLIER'
              AND u."default_currency" = 'INR'
              AND u."verifyStatus" = '4'
              AND d."OrderItemId" IS NULL
              AND d."CertificateType" = 'NaturalCertificate'
              AND d.brown = 0
              AND d.green = 0
              AND d.milky = 0
              AND d."eyeClean" = 100
              AND d."availability" = 'AVAILABLE'
              AND nc.verified = 'true'
              AND qc."CertificateId" IS NULL
              AND cs."cert_details_enabled" = true
              AND cs.self_serve_po IS NULL
              AND cs.accept_returns = true
              AND u.geo_country = 'IN'
            ORDER BY cs."CompanyId" DESC
            LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/creditNoteDiamond.json', result1.rows);
  });
});
Cypress.Commands.add('getcreditNoteGems', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `WITH cart_gemstones AS (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemstoneId"
                        FROM "CartItems")
                SELECT DISTINCT nc."certNumber"
                FROM "Users" u
                        JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                        JOIN "Companies" c ON c.id = cs."CompanyId" AND c.supplier_status = 'LIVE'
                        JOIN "Gemstones" d
                              ON cs."CompanyId" = d."CompanyId" AND d."OrderItemId" IS NULL AND d.availability = 'AVAILABLE'
                        JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                        LEFT JOIN cart_gemstones si ON d."id" = si."GemstoneId"
                WHERE u."role" = 'SUPPLIER'
                  AND u."default_currency" = 'INR'
                  AND u."verifyStatus" = '4'
                  AND cs."cert_details_enabled" = true
                  AND si."GemstoneId" IS NULL
                  AND cs."self_serve_po" IS NULL
                LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/creditNoteGems.json', result1.rows);
  });
});
Cypress.Commands.add('getexpressCheckoutStone1', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (lc."certNumber")
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        INNER JOIN "FbnItems" fi on fi.id = d."FbnId"
                    AND d."CertificateType" = 'LabgrownCertificate'
                    AND d."availability" = 'AVAILABLE'
                    AND cs.nivoda_express_supplier = true
                    AND cs."cert_details_enabled" = true
                    AND d."FbnId" is not null
                    AND oi."FbnType" = 'INTERNAL'
                    AND d."OrderItemId" is null
                    AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                    AND fi.internal_status = 'ACTIVE'
                    AND cs.self_serve_po IS NULL
                ORDER BY lc."certNumber" ASC
                LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/expressCheckoutStone1.json', result1.rows);
  });
});
Cypress.Commands.add('getexpressCheckoutStone2', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT distinct (lc."certNumber")
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        INNER JOIN "FbnItems" fi on fi.id = d."FbnId"
                    AND d."CertificateType" = 'LabgrownCertificate'
                    AND d."availability" = 'AVAILABLE'
                    AND cs.nivoda_express_supplier = true
                    AND cs."cert_details_enabled" = true
                    AND d."FbnId" is not null
                    AND oi."FbnType" = 'INTERNAL'
                    AND d."OrderItemId" is null
                    AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                    AND fi.internal_status = 'ACTIVE'
                    AND cs.self_serve_po IS NULL
                ORDER BY lc."certNumber" DESC
                LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/expressCheckoutStone2.json', result1.rows);
  });
});
Cypress.Commands.add('getmemouser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_stone_checkout_limit as count
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
        AND u.geo_country IS NOT NULL
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND u.email NOT IN ('<EMAIL>','<EMAIL>')
        and cfs."CREDIT_LIMIT" > 10000
        order by u."email"
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/LoginasMemoEnabledUser.json', result1.rows);
  });
});
Cypress.Commands.add('getLoginasMemoEnabledUserCompanyOwner', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_enabled , u."OwnerOfCompanyId" , cfs."CREDIT_LIMIT" , ROUND((cfs."CR_60_PER" - cfs."CR_30_PER") * 100, 2) AS "percentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
        inner join 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."geo_country" is not null
        AND u."OwnerOfCompanyId" IS Not NULL
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        and cfs."CREDIT_LIMIT" > 10000
        LIMIT 1;`
  }).then((result1) => {
    cy.log('DB Result:', result1);
    return cy.writeFile('cypress/fixtures/LoginasMemoEnabledUserCompanyOwner.json', result1.rows);
  });
});

Cypress.Commands.add('getLoginasMemoEnabledUserNonCompanyUser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name", 
        cs.memo_enabled, 
        u."OwnerOfCompanyId", 
        cfs."CREDIT_LIMIT", 
        ROUND((cfs."CR_60_PER") * 100, 2) AS "sixtydaypercentage", 
        ROUND((cfs."CR_30_PER") * 100, 2) AS "thirtydaypercentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs ON c.id = cfs."CompanyId" 
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
        AND u.geo_country IS NOT NULL
        AND u."OwnerOfCompanyId" IS NULL
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = TRUE
        AND cfs."CREDIT_LIMIT" > 10000
        AND u."email" <> '<EMAIL>'
        order by u."email" desc
      LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/LoginasMemoEnabledUserNonCompanyUser.json', result1.rows);
  });
});

Cypress.Commands.add('getMultipleAddressUser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u."email"                                                                              AS "email",
                    c.name                                                                                 AS "name",
                    cfs."ACCOUNT_LIMIT_CONSUMED",
                    '$' || TO_CHAR(cfs."ACCOUNT_LIMIT" - cfs."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit",
                    CONCAT_WS(', ', l1."address1",l1."address2", CONCAT(l1."postalCode", ' ', l1."city"), l1."country")  AS "location_1",
                    CONCAT_WS(', ', l2."address1",l2."address2", CONCAT(l2."postalCode", ' ', l2."city"), l2."country")  AS "location_2"
              FROM "Users" u
                      INNER JOIN
                  "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "CompanyFinanceSettings" cfs on cfs."CompanyId" = u."CompanyId"
                      INNER join "Companies" c on u."CompanyId" = c.id
                      INNER JOIN
                  (SELECT "CompanyId"
                    FROM "Locations"
                    GROUP BY "CompanyId"
                    HAVING COUNT(*) = 2) lc ON u."CompanyId" = lc."CompanyId"
                      INNER JOIN
                  "Locations" l1 ON u."CompanyId" = l1."CompanyId"
                      INNER JOIN
                  "Locations" l2 ON u."CompanyId" = l2."CompanyId"
                      AND l1."id" <> l2."id"
                      AND (l1."address1" <> l2."address1" OR l1."postalCode" <> l2."postalCode" OR l1."city" <> l2."city" OR
                            l1."country" <> l2."country")
              WHERE u."role" = 'CUSTOMER'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = true
                AND cs."display_supplier_name" = true
                AND cfs."CREDIT_NOTE_APPLICATION" = true
                AND (cfs."ACCOUNT_LIMIT" - cfs."ACCOUNT_LIMIT_CONSUMED") > 10000
              ORDER BY c.name desc
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/multipleaddressuser.json', result1.rows);
  });
});

Cypress.Commands.add('getTotalOvderDueData', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
        WITH UserCurrencyCounts AS (
    SELECT 
        "to" AS CompanyId,
        COUNT(DISTINCT currency) AS currency_count
    FROM 
        "Invoices"
    WHERE 
        settled = FALSE
        AND due_date IS NOT NULL
        AND due_date < NOW()
        AND invoice_sent IS NOT NULL
    GROUP BY 
        "to"
)
SELECT 
    u.email,
    TO_CHAR(
        TRUNC(
            SUM(
                CASE
                    WHEN uc.currency_count > 2 THEN
                        CASE
                            WHEN i.status = 'PARTIAL_PAID'
                                THEN (i.total_in_local_currency / i.exchange_rate) - i.settled_amount
                            WHEN i.status IS NULL THEN i.total_in_local_currency / i.exchange_rate
                            ELSE 0
                        END
                    ELSE
                        CASE
                            WHEN i.status = 'PARTIAL_PAID'
                                THEN i.total_in_local_currency - i.settled_amount
                            WHEN i.status IS NULL THEN i.total_in_local_currency
                            ELSE 0
                        END
                END
            ), 2 -- Set precision to 2 decimal places
        ), 'FM999,999,999.00' -- Format to two decimal places
    ) AS total_overdue_amount
FROM 
    "Invoices" i
    INNER JOIN "Users" u ON i."to" = u."CompanyId"
    INNER JOIN UserCurrencyCounts uc ON i."to" = uc.CompanyId
WHERE 
    (i.status = 'PARTIAL_PAID' OR i.status IS NULL)
    AND i.settled = FALSE
    AND i.due_date IS NOT NULL
    AND i.due_date < NOW()
    AND invoice_sent IS NOT NULL
    AND u."verifyStatus" = '4'
    AND u."role" = 'CUSTOMER'

GROUP BY 
    u.email, uc.currency_count
HAVING 
    uc.currency_count >= 2
 LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/totalOverdue.json', result1.rows);
  });
});

Cypress.Commands.add('getCreditUser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
            SELECT u.email,
       c."name",
       cf."ACCOUNT_LIMIT_CONSUMED",
       '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                INNER join "Companies" c on U."CompanyId" = c.id
                INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."CREDIT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = false
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = true
          AND u."LocationId" IS NOT NULL
          AND u."geo_country" IS NOT NULL
          AND u."subtype" = 'PLATFORM_AND_CFM'
          AND ck."kyc_verified" = true
        LIMIT 1;
        `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/credituser.json', result1.rows);
  });
});

Cypress.Commands.add('getaddtocartmeleedata', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT d."supplierStockId" AS "NivodaStockId"
              FROM "Users" u
                      JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                      LEFT JOIN (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "MeleeId"
                                  FROM "ShortlistItems") si ON d."id" = si."MeleeId"
              WHERE d."OrderItemId" IS NULL
                AND d."IsParentMeleeCollection" IS NULL
                AND d."ParentMeleeCollectionId" IS NULL
                AND d."type" = 'NATURAL'
                AND d.pieces IS NULL
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."MeleeId" IS NULL
                AND d.status = 'LIVE'
                AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
              ORDER BY RANDOM()
              LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/addtocartmeleedata.json', result1.rows);
  });
});

Cypress.Commands.add('getCompanyCfs', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u.email,
                  c."name"                                                                             AS name,
                  c.website,
                  cf."ACCOUNT_LIMIT_CONSUMED",
                  '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
            FROM "CompanyFinanceSettings" cf
                    INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                    INNER JOIN "Companies" c ON u."CompanyId" = c.id
                    INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE cf."ADVANCE_PAYMENT" = false
              AND u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = true
              AND u."subtype" = 'PLATFORM_AND_CFM'
              AND ck."kyc_verified" = true
              AND c."name" NOT LIKE 'xxxx%'
            ORDER BY c."name" DESC
            LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/getCompanyData.json', result1.rows);
  });
});

Cypress.Commands.add('getCustomerMeleeRequest', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT order_request_number
          FROM "SpecialRequests" sr
          inner join "Users" u on sr.requester_id = u.id
          where u.email = '${email}'
            AND sr."closedAt" IS NULL
            AND sr.status = 'OPEN'
            AND customer_order_number IS NULL
            AND assigned_to IS NULL
            AND "assignedAt" IS NULL
          ORDER BY sr."createdAt" DESC
          LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/customerMeleeSrdata.json', result1.rows);
  });
});

Cypress.Commands.add('getCancelledMeleeRequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT order_request_number
            FROM "SpecialRequests" sr
              WHERE sr.status = 'CANCELLED'
            order by "createdAt" desc
            LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/cancelledMeleeSrdata.json', result1.rows);
  });
});

Cypress.Commands.add('getInProgressMeleeRequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT order_request_number
      FROM "SpecialRequests" sr
        WHERE sr.status = 'IN_PROGRESS'
      order by "createdAt" desc
      LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/inProgressMeleeSrdata.json', result1.rows);
  });
});

Cypress.Commands.add('getQuotedMeleeRequest', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT order_request_number
      FROM "SpecialRequests" sr
        WHERE sr.status = 'QUOTE_SENT'
      order by "createdAt" desc
      LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/quotedsMeleeSrdata.json', result1.rows);
  });
});
Cypress.Commands.add('getMeleeStockId', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT d."supplierStockId" AS "NivodaStockId"
          FROM "Users" u
                  JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                  JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                  LEFT JOIN (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "MeleeId"
                              FROM "CartItems") si ON d."id" = si."MeleeId"
          WHERE d."OrderItemId" IS NULL
            AND d."IsParentMeleeCollection" IS NULL
            AND d."ParentMeleeCollectionId" IS NULL
            AND d."type" = 'NATURAL'
            AND d.pieces IS NULL
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND u."verifyStatus" = '4'
            AND si."MeleeId" IS NULL
            AND d.status = 'LIVE'
            AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
          ORDER BY RANDOM()
          LIMIT 1;`
  }).then((result) => {
    cy.writeFile('cypress/fixtures/meleeStoneData.json', result.rows);
  });
});
Cypress.Commands.add('getlabGrownMeleeStockId', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT d."supplierStockId" AS "NivodaStockId"
          FROM "Users" u
                  JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                  JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                  LEFT JOIN (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "MeleeId"
                              FROM "CartItems") si ON d."id" = si."MeleeId"
          WHERE d."OrderItemId" IS NULL
            AND d."IsParentMeleeCollection" IS NULL
            AND d."ParentMeleeCollectionId" IS NULL
            AND d."type" = 'LABGROWN'
            AND d.pieces IS NULL
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND u."verifyStatus" = '4'
            AND si."MeleeId" IS NULL
            AND d.status = 'LIVE'
            AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
          ORDER BY RANDOM()
          LIMIT 1;`
  }).then((result) => {
    cy.writeFile('cypress/fixtures/labGrownMeleeStoneData.json', result.rows);
  });
});

Cypress.Commands.add('getOTPDisabledUser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT u."email" AS "email"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        INNER JOIN "Companies" c ON u."CompanyId" = c.id
        WHERE u."role" = 'CUSTOMER'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          AND u.subtype = 'PLATFORM_AND_CFM'
          AND c.code_disabled IS NOT NULL
          AND cs."display_supplier_name" = true
        LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/otpDisabledUser.json', result1.rows);
  });
});

Cypress.Commands.add('getHKUser', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
     SELECT u.email,
       c."name",
       cf."ACCOUNT_LIMIT_CONSUMED",
       '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                INNER join "Companies" c on U."CompanyId" = c.id
                                JOIN "CompanySettings" cs ON cs."CompanyId" = c."id"

                INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
              AND u.default_currency='HKD'
        AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 10000
          AND cf."CREDIT_LIMIT" > 10000
          AND cs.nivoda_express_supplier = true
          AND cf."IP_ACTIVE" = false
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = true
          AND ck."kyc_verified" = true
        LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/getHKuser.json', result1.rows);
  });
});

Cypress.Commands.add('getKriyaCustomer', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
     SELECT u.email,
       kriya_company_identifier,
       cf."DEFAULT_CHECKOUT_OPTION",
       CASE
           WHEN cf."INVOICE_CURRENCY" = 'EUR' THEN
               REPLACE(
                       REPLACE(
                               REPLACE(
                                       TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2),
                                               'FM999G999G990D00'),
                                       ',', 'X'
                               ),
                               '.', ','
                       ),
                       'X', '.'
               ) || ' €'
           WHEN cf."INVOICE_CURRENCY" = 'GBP' THEN
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM£999,999,990.00')
           WHEN cf."INVOICE_CURRENCY" = 'USD' THEN
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM$999,999,990.00')
           ELSE
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM999,999,990.00')
           END AS "Total_Account_Limit"
          FROM "CompanyFinanceSettings" cf
                  INNER JOIN "Companies" c ON cf."CompanyId" = c.id
                  INNER JOIN "Users" u ON cf."CompanyId" = u."CompanyId"
          WHERE "MARKET_PAY_ACTIVE" = TRUE
            AND "MARKET_PAY_ELIGIBLE" = TRUE
            AND kriya_company_identifier IS NOT NULL
            AND "MARKET_PAY_LIMIT" > 1000
            AND cf."DISABLE_CHECKOUT" = FALSE
            AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
            AND ("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED") > 30000
            AND cf."ALL_IN_PRICING" = TRUE
            AND cf."DISABLE_CHECKOUT" = FALSE
            AND c.code_disabled IS NULL
            AND u."verifyStatus" = 4
            AND u."role" = 'CUSTOMER'
            AND u."status" = '4'
            AND u.subtype = 'PLATFORM_AND_CFM'
            AND u."address_verified" = 'true'
          ORDER BY u.email DESC
          LIMIT 1;
    `
    })
    .then((result1) => {
      cy.writeFile('cypress/fixtures/kriyaUser.json', result1.rows);
    });
});

Cypress.Commands.add('getUpfrontUser', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT u.email, c."name", u.id, u.subtype
            FROM "CompanyFinanceSettings" cf
                    INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                    INNER JOIN "Companies" c ON c.id = u."CompanyId"
                    LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE cf."ADVANCE_PAYMENT" = true
              AND cf."DISABLE_CHECKOUT" = false
              AND cf."DEFAULT_CHECKOUT_OPTION" = 'up_front_payment'
              AND cf."ALL_IN_PRICING" = true
              AND c.code_disabled IS NULL
              AND u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = 'true'
              AND u."LocationId" IS NOT NULL
              AND u.geo_country IS NOT NULL
              AND ck.kyc_verified = true
            ORDER BY RANDOM()
            LIMIT 1;`
    })
    .then((result1) => {
      cy.writeFile('cypress/fixtures/upfrontaipuser.json', result1.rows);
    });
});

// ------------------------- API QUERIES ------------------------
Cypress.Commands.add('getNaturalDiamondData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
      SELECT nc."certNumber", d.id AS "diamondId", nc.id AS "certId"
      FROM "Users" u
        INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
        INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
        LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
        LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                   FROM "ShortlistItems"
                   UNION
                   SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                   FROM "CartItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."availability" = 'AVAILABLE'
        AND d.brown = 0
        AND d.green = 0
        AND d.milky = 0
        AND d."eyeClean" = 100
        AND d."HoldId" IS NULL
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified = 'true'
        AND nc."VideoId" is not null
                    AND  (u."default_currency" = 'INR' AND d."CertificateType" = 'NaturalCertificate' AND geo_country = 'IN'
                  AND cs.self_serve_po IS NULL AND qc."CertificateId" IS NULL)
      ORDER BY d."supplierStockId", nc."certNumber"
      LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});

Cypress.Commands.add('getLabDiamondData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
      SELECT lc."certNumber", d.id AS "diamondId"
      FROM "Users" u
        INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
        INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
        LEFT JOIN "Qcs" qc ON lc.id = qc."CertificateId"
        LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                   FROM "ShortlistItems"
                   UNION
                   SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                   FROM "CartItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."availability" = 'AVAILABLE'
        AND d.brown = 0
        AND d.green = 0
        AND d.milky = 0
        AND d."eyeClean" = 100
        AND d."HoldId" IS NULL
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND lc.verified = 'true'
        AND (u."default_currency" = 'INR' AND d."CertificateType" = 'LabgrownCertificate' AND geo_country = 'IN'
        AND cs.self_serve_po IS NULL AND qc."CertificateId" IS NULL)
      ORDER BY d."supplierStockId", lc."certNumber"
      LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});

Cypress.Commands.add('getGemstonesData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
     SELECT DISTINCT (nc."certNumber"), d.id, nc.id As "certId"
        FROM "Users" u
                INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "ShortlistItems"
                            UNION
                            SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."HoldId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND cs."cert_details_enabled" = true
          AND nc."VideoId" IS NOT NULL
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty; // Ensure we got data
      return cy.wrap(result.rows); // Return the data for further use
    });
});

Cypress.Commands.add('getCuratedGemstonesData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
     SELECT DISTINCT (nc."certNumber"), d.id, nc.id As "certId"
      FROM "Users" u
              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
              INNER JOIN "Companies" c ON c.id = cs."CompanyId"
              INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
              INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
              LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                          FROM "ShortlistItems"
                          UNION
                          SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                          FROM "CartItems") si ON d."id" = si."GemstoneId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND nc.nivoda_curated = true
        AND c.supplier_status = 'LIVE'
        AND cs."cert_details_enabled" = true
        AND nc."VideoId" IS NOT NULL
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND d.availability = 'AVAILABLE'
      LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty; // Ensure we got data
      return cy.wrap(result.rows); // Return the data for further use
    });
});

Cypress.Commands.add('getMeleeAPIData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
      SELECT d."supplierStockId" AS "NivodaStockId", d.id
      FROM "Users" u
        JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
        LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                   FROM "ShortlistItems"
                   UNION
                   SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                   FROM "CartItems") si ON d."id" = si."MeleeId"
      WHERE d."OrderItemId" IS NULL
        AND d."IsParentMeleeCollection" IS NULL
        AND d."ParentMeleeCollectionId" IS NULL
        AND d."type" = 'NATURAL'
        AND d.pieces IS NULL
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."MeleeId" IS NULL
        AND d.status = 'LIVE'
        AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
      LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});

Cypress.Commands.add('getLabMeleeAPIData', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
      SELECT d."supplierStockId" AS "NivodaStockId", d.id
      FROM "Users" u
        JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
        LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                   FROM "ShortlistItems"
                   UNION
                   SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                   FROM "CartItems") si ON d."id" = si."MeleeId"
      WHERE d."OrderItemId" IS NULL
        AND d."IsParentMeleeCollection" IS NULL
        AND d."ParentMeleeCollectionId" IS NULL
        AND d."type" = 'LABGROWN'
        AND d.pieces IS NULL
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."MeleeId" IS NULL
        AND d.status = 'LIVE'
        AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
      LIMIT 1;
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});

Cypress.Commands.add('getLatestInvoice', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT invoice_number as invoice
        FROM "Invoices"
        ORDER BY "createdAt" DESC
        LIMIT 1`
  }).then((result) => {
    cy.writeFile('cypress/fixtures/LatestInvoice.json', result.rows);
  });
});
Cypress.Commands.add('getJewellerySku', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `select * from "Jewellery" where status = 'CREATED'
      limit 1`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/jewellerySku.json', result1.rows);
  });
});
Cypress.Commands.add('getNivodaCuratedStone', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT "certNumber" 
      FROM "GemCertificates" gc
      INNER JOIN "Gemstones" g ON g."CertificateId" = gc.id
      WHERE gc."nivoda_curated" = true
        AND g.availability = 'AVAILABLE'
        AND g."OrderItemId" IS NULL
      LIMIT 1;
    `
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/getNivodaCuratedStone.json', result1.rows);
  });
});

Cypress.Commands.add('getExpressStone', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
     SELECT lc."certNumber",
       d.id AS "diamondId"
    FROM "LabgrownCertificates" lc
         INNER JOIN
     "Diamonds" d ON d."CertificateId" = lc."id"
         INNER JOIN
     "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
         INNER JOIN
     "Users" u ON u."CompanyId" = cs."CompanyId"
         INNER JOIN
     "OrderItems" oi ON oi."ProductId" = d.id
         INNER JOIN
     "FbnItems" fi ON fi.id = d."FbnId"
    WHERE d."CertificateType" = 'LabgrownCertificate'
      AND d."availability" = 'AVAILABLE'
      AND cs.nivoda_express_supplier = true
      AND cs."cert_details_enabled" = true
      AND d."FbnId" IS NOT NULL
      AND oi."FbnType" = 'INTERNAL'
      AND d."OrderItemId" IS NULL
      AND fi.internal_status = 'ACTIVE'
      AND cs.self_serve_po IS NULL
      AND NOT EXISTS (SELECT 1
                      FROM "ShortlistItems" si
                      WHERE substring(si."OfferId" from position('/' in si."OfferId") + 1)::uuid = d.id)
      AND NOT EXISTS (SELECT 1
                      FROM "CartItems" ci
                      WHERE substring(ci."OfferId" from position('/' in ci."OfferId") + 1)::uuid = d.id);
    `
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});

Cypress.Commands.add('getMemoStone', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT COALESCE(n."certNumber"::varchar, l."certNumber"::varchar) AS "certNumber", d.id  as "diamondId"
            FROM "public"."Diamonds" AS d
                    LEFT JOIN "public"."NaturalCertificates" AS n ON d."CertificateId" = n."id"
                    LEFT JOIN "public"."LabgrownCertificates" AS l ON d."CertificateId" = l."id"
                    LEFT JOIN "public"."Companies" AS c ON d."CompanyId" = c."id"
                    LEFT JOIN "public"."CompanySettings" AS cs ON c."id" = cs."CompanyId"
                    LEFT JOIN "Locations" AS loc ON d.location = loc.id
                    LEFT JOIN "FbnItems" AS f ON d."FbnId" = f.id
                    LEFT JOIN "public"."SupplierMemoEligibility" AS s ON d."CertificateId" = s."CertificateId"
            WHERE d."OrderItemId" IS NULL
              AND d."availability" = 'AVAILABLE'
              AND (c."dummy_account" IS NULL OR c."dummy_account" IS NOT TRUE)
              AND (
                (s."eligibility" = true AND cs.memo_supplier IS NOT NULL AND cs.memo_stock_outside_express IS NOT NULL AND
                d."FbnId" IS NULL)
                    OR
                (f.consignment_closure_date IS NOT NULL AND cs.memo_supplier IS NOT NULL AND
                f.consignment_closure_date::date > date(current_date + interval '15' day) AND f.inventory_type IN ('MEMO', 'FBN'))
                    OR
                (d."valueForMemo" IS NOT NULL AND d."valueForMemo" >= d."valueWithDiscount" AND
                cs.memo_listing_method = 'MEMO_PRICE_FEED' AND cs.memo_supplier IS NOT NULL)
                )
              AND loc.country = 'US'
            LIMIT 1;`
    })
    .then((result) => {
      expect(result.rows).to.not.be.empty;
      return cy.wrap(result.rows);
    });
});
Cypress.Commands.add('getmeleeQuoteSrData', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT u.email, sr.order_request_number,
       or2.status
      FROM "SpecialRequests" sr
              INNER JOIN "OrderRequests" or2
          ON sr.order_request_number = or2.order_request_number
                  INNER JOIN "Users" u
                          ON sr.requester_id=u.id
      WHERE sr."closedAt" IS NULL
        AND sr.status = 'QUOTE_SENT'
        AND or2.status = 'PENDING_CONFIRMATION'
      ORDER BY sr."createdAt" DESC
      LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/meleeQuoteSrData.json', result1.rows);
  });
});

Cypress.Commands.add('getSupplierWithInventory', (companyId, inventoryType) => {
  cy.log('Auth Token:', Cypress.env('authToken'));

  const query = `
    query ($company_id: ID!) {
      get_feed_shares_by_company(company_id: $company_id) {
        supplier {
          name
          inventory_type
          expected_stones
        }
      }
    }
  `;

  const variables = { company_id: companyId };

  return cy
    .request({
      method: 'POST',
      url: Cypress.env('adminApiUrl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        query,
        variables
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      if (response.body.errors) {
        throw new Error(`GraphQL Error: ${JSON.stringify(response.body.errors, null, 2)}`);
      }

      const feedShares = response.body?.data?.get_feed_shares_by_company ?? [];
      const supplier = feedShares
        .map((s) => s.supplier)
        .find((s) => s && s.expected_stones > 1000 && s.inventory_type?.includes(inventoryType));

      const supplierName = supplier?.name || null;
      return cy.wrap(supplierName);
    });
});
Cypress.Commands.add('getCompanyWithManyFeeds', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT c."id", c."name"
      FROM "Companies" c
      LEFT JOIN "FeedShares" fs ON fs."CustomerId" = c.id
      WHERE c.status > 1
      GROUP BY c.id
      HAVING COUNT(fs.id) > 1000
      LIMIT 1;
    `
  }).then((result) => {
    cy.writeFile('cypress/fixtures/companyWithFeeds.json', result.rows);
  });
});
Cypress.Commands.add('getdiamondonhold', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      WITH shortlisted_diamonds
         AS (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "DiamondId"
             FROM "ShortlistItems"),
            filtered_diamonds AS (SELECT d."CertificateId", d."NivodaStockId", nc."certNumber"
                                  FROM "Diamonds" d
                                            JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                                            JOIN "CompanySettings" cs ON d."CompanyId" = cs."CompanyId"
                                            JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                                  WHERE u."role" = 'SUPPLIER'
                                    AND u."default_currency" = 'USD'
                                    AND u."verifyStatus" = '4'
                                    AND cs."accept_holds" = 'true'
                                    AND cs."cert_details_enabled" = 'true'
                                    AND d."OrderItemId" IS NULL
                                    AND d."HoldId" IS NULL
                                    AND d."CertificateType" = 'NaturalCertificate'
                                    AND d."availability" = 'AVAILABLE'
                                    AND d."brown" = 0
                                    AND d."green" = 0
                                    AND d."milky" = 0
                                    AND d."eyeClean" = 100
                                    AND nc."ImageId" IS NOT NULL
                                    AND nc."VideoId" IS NOT NULL
                                    AND nc."verified" = 'true'
                                    AND d."id" NOT IN (SELECT "DiamondId" FROM shortlisted_diamonds))
        SELECT "certNumber", "NivodaStockId"
        FROM filtered_diamonds
        ORDER BY random()
        OFFSET 200 LIMIT 1;
    `
  }).then((result) => {
    cy.writeFile('cypress/fixtures/diamondonhold.json', result.rows);
  });
});

Cypress.Commands.add('updateDecert', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      UPDATE public."GemCertificates" 
      SET delisted_cert = true 
      WHERE "certNumber" = '45789';
    `
  }).then((result) => {
    cy.writeFile('cypress/fixtures/diamondonhold.json', result.rows);
  });
});

Cypress.Commands.add('verifyLiveChatIframe', (iframeSelector) => {
  cy.get(iframeSelector, { timeout: 10000 }).then(($iframe) => {
    if ($iframe.length > 0) {
      cy.wrap($iframe)
        .its('0.contentDocument')
        .should('exist')
        .then((doc) => {
          cy.wrap(doc).find('img[alt="Company logo"]', { timeout: 10000 }).should('exist');
          cy.wrap(doc).find('textarea[placeholder="Type a message"]', { timeout: 10000 }).should('be.visible');
          cy.wrap(doc).find('figure[data-garden-id="avatars.avatar"]', { timeout: 10000 }).should('exist');
        });
    } else {
      cy.get('iframe', { timeout: 5000 }).then(($iframes) => {
        if ($iframes.length > 0) {
          cy.wrap($iframes.first())
            .its('0.contentDocument')
            .should('exist')
            .then((doc) => {
              cy.wrap(doc).find('img[alt="Company logo"]', { timeout: 10000 }).should('exist');
              cy.wrap(doc)
                .find('textarea[placeholder="Type a message"]', {
                  timeout: 10000
                })
                .should('be.visible');
              cy.wrap(doc)
                .find('figure[data-garden-id="avatars.avatar"]', {
                  timeout: 10000
                })
                .should('exist');
            });
        } else {
          cy.log('Help action completed successfully - chat interface may be loading or not available');
        }
      });
    }
  });
});
Cypress.Commands.add('emptyShowroomCartItems', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
     DELETE FROM "CFMCartItems"
      USING "Users"
      WHERE "CFMCartItems"."UserId" = "Users".id
      AND "Users".email = '${email}';
    `
  });
});
Cypress.Commands.add('fetchUsersByCompanyIdExceptSameEmail', (inputFile, outputFile) => {
  cy.readFile(`cypress/fixtures/${inputFile}`).then((data) => {
    const { CompanyId, email } = data[0];

    const query = `
      SELECT "email", "CompanyId" 
      FROM "Users" 
      WHERE "CompanyId" = '${CompanyId}' 
        AND "email" <> '${email}'
        Limit 1
    `;

    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: query
    }).then((result) => {
      cy.log('DB Result:', result);
      cy.writeFile(`cypress/fixtures/${outputFile}`, result.rows);
    });
  });
});
Cypress.Commands.add('emptyShowroomShortListItems', () => {
  const email = Cypress.env('loginEmail');

  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `delete from "Favourites"
    USING "Users"
      WHERE "Favourites"."UserId" = "Users".id
      AND "Users".email = '${email}';`
  });
});
