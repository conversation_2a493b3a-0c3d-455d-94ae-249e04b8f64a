/// <reference types="cypress" />
class Menu {
  constructor() {
    // Locators
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.hamBurgerMenu = '[data-automation-id="web--ham-btn"]';
    this.sideBarButton = '[data-automation-id="sidebar-menu-btn"]';
    this.supplierHamBurgerMenu = '.MuiButton-root.css-hc0lcc';
    this.supplierSideBarButton = '.MuiListItem-root.MuiListItem-gutters.css-1iewp06';
    this.supplierHamBurgerMenu = '.MuiButton-root.css-hc0lcc';
    this.supplierSideBarButton = '.MuiListItem-root.MuiListItem-gutters.css-1iewp06';
    this.orderpageTitle = '[data-automation-id="header-title"]';
    this.orderDropDown = '[data-automation-id="select--inp"]';
    this.logoutButton = '[data-automation-id="acc-logout"]';
    this.pageRoot = '#root';
    this.BuyerDashboard = '.link-button.link-hover-bg[target="_blank"]';
    this.supplierDashboard = '.link-button.link-hover-bg';
    this.pswdClick = 'a[href="/settings/password"]';
    this.companyClick = 'a[href="/settings/company"]';
    this.userClick = 'a[href="/settings/user-management"]';
    this.locationClick = 'a[href="/settings/locations"]';
  }

  visitMenu() {
    cy.get(this.hamBurgerMenu, { timeout: 90000 }).click({ force: true });
  }

  visitShortList() {
    const shortlistRegex = /Shortlist|Favoris|Elenco breve|Lista de favoritos|お気に入り/;

    cy.get(this.sideBarButton)
      .contains(shortlistRegex, { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .click()
      .wait(300);

    cy.url().should('include', '/shortlist');
  }

  visitOrdersPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Orders').click({ force: true });
    cy.url().should('include', 'diamonds/customer-orders/all');
    cy.get(this.orderpageTitle, { timeout: 60000 }).invoke('text').should('eq', 'Orders');
  }

  VerifyLogout() {
    cy.get(this.logoutButton).contains('Log out').click({ force: true });
  }

  visitSettingsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Settings').click({ force: true });
    cy.contains('Personal Settings');
    cy.url().should('include', '/settings/personal');
  }

  visitInvoicesPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Invoices').click({ force: true });
    cy.contains('Invoices');
    cy.url().should('include', '/invoices');
  }

  visitFinancesPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Finances').click({ force: true });
    cy.contains('Finances');
    cy.url().should('include', '/finances');
  }

  visitRequestPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Requests').click({ force: true });
    cy.contains('Requests');
    cy.url().should('include', '/live/requests');
  }

  visitHoldsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Holds').click({ force: true });
    cy.contains('Holds');
    cy.url().should('include', '/customer-holds/all');
  }

  visitFeedCenterPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Feed Center').click({ force: true });
    cy.contains('Feed Center');
    cy.url().should('include', '/feed-center');
  }

  visitFeedSetupPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Feed Setup').click({ force: true });
    cy.url().should('include', '/live/self-serve');
  }

  accessSupplierTabs(sidebarTab, url) {
    cy.get(this.hamBurgerMenu, { timeout: 90000 }).eq(0).should('be.visible').click({ force: true });
    cy.wait(500);
    cy.get(this.supplierSideBarButton, { timeout: 90000 })
      .contains(sidebarTab)
      .scrollIntoView()
      .should('be.visible')
      .click();
    cy.url({ timeout: 10000 }).should('include', url);
    cy.get(this.pageRoot).should('not.include.text', 'Something went wrong');
  }

  visitBuyerDashboardPage() {
    cy.get(this.BuyerDashboard).contains('Buyer Dashboard').should('be.visible').click();
  }

  visitSupplierDashboardPage() {
    cy.get(this.supplierDashboard).contains('Supplier Dashboard').should('be.visible').click();
  }
  allFilterMenu() {
    cy.contains('button', 'All filters', { timeout: 2000 }).click();
  }

  applyFilters() {
    cy.contains('button', 'Apply filters', { timeout: 2000 }).click();
  }

  visitSupplierSettingsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).contains('Settings').click();
    cy.url().should('include', '/settings/personal');
  }

  visitSupplierUserPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.userClick).click();
    cy.url().should('include', '/settings/user-management');
  }

  visitSupplierLocationPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.locationClick).click();
    cy.url().should('include', '/settings/locations');
  }

  visitSupplierCompanySettingsPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.companyClick).click();
    cy.url().should('include', '/settings/company');
  }

  visitSupplierPasswordSettingsPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.pswdClick).click();
    cy.url().should('include', '/settings/password');
  }
}
export default Menu;
