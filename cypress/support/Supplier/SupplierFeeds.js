/// <reference types="cypress" />
class SupplierFeeds {
  constructor() {
    this.feedText = '.MuiTypography-root';
    this.feedCenterTable = '.MuiStack-root';
    this.feedSharePopupHeading = '.css-1tutcis > .MuiTypography-h6';
    this.successDialogue = '.MuiSnackbar-message > .MuiTypography-root';
  }
  clickModifyButton() {
    cy.get(this.feedCenterTable, { timeout: 5000 }).contains('button', 'Modify').click();
  }
  verifyFeedShareSettingPopupIsDisplayed() {
    cy.get(this.feedSharePopupHeading).should('be.visible').and('contain.text', 'Feed share settings');
  }
  verifyCheckboxIsChecked(labelText) {
    cy.contains('label', labelText, { timeout: 3000 }).find('input[type="checkbox"]').should('be.checked');
  }
  ensureCheckboxIsChecked(labelText) {
    cy.contains('label', labelText, { timeout: 3000 })
      .find('input[type="checkbox"]')
      .then(($checkbox) => {
        if (!$checkbox.is(':checked')) {
          cy.wrap($checkbox).click({ force: true });
        }
      });
  }
  saveChanges(labelText) {
    cy.contains('button', 'Save changes').click();
  }
  verifySuccessMessage(labelText) {
    cy.get(this.successDialogue).should('have.text', 'Feed share settings updated successfully.');
    cy.reload();
    cy.wait(5000);
  }
  verifyCustomerCount(status = 'active') {
    cy.get(this.feedText, { timeout: 10000 }).should(($el) => {
      const text = $el.text();
      const match = text.match(/Your feed is active for (\d+) customers/);
      expect(match, 'Match should not be null').to.not.be.null;

      const customerCount = parseInt(match[1], 10);

      if (status === 'active') {
        expect(customerCount, 'Expected active customers to be > 0').to.be.greaterThan(0);
      } else if (status === 'revoked') {
        expect(customerCount, 'Expected revoked customers to be 0').to.equal(0);
      }
    });
  }
}

export default SupplierFeeds;
