/// <reference types="cypress" />
class Calculator {
  constructor() {
    this.selectors = {
      calculatorWidget: '[data-testid="price-calculator-widget"]',
      dropdown: '[data-automation-id="select--input"]',
      carat: '#size',
      discountInput: '#discount',
      pricePerCarat: '#ppc',
      totalPrice: '#total',
      plusButton:
        '.MuiButtonBase-root.MuiIconButton-root.MuiIconButton-colorSecondary.MuiIconButton-sizeSmall.css-1lmxzy2',
      resetButton: '[data-testid="reset-button"]',
      menuItemSelector: '.MuiTypography-root.MuiTypography-body1.css-9u13l8',
      hamburgerMenu: 'button[aria-label="open drawer"]'
    };
    this.analyticsLabel = 'Analytics';
  }

  openMenu() {
    cy.get(this.selectors.hamburgerMenu, { timeout: 60000 }).should('be.visible').click();
  }

  navigateTo(label) {
    this.openMenu();

    if (label === 'My Pricing Insights' || label === 'Market Insights') {
      cy.contains(this.selectors.menuItemSelector, this.analyticsLabel, { timeout: 60000 })
        .scrollIntoView()
        .should('be.visible')
        .click({ force: true });
    }

    // Ensure menu item is visible and click it
    cy.contains(this.selectors.menuItemSelector, label, { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .click({ force: true });
    this.verifyCalculatorVisible();
  }

  calculatorIcon() {
    cy.get(this.selectors.calculatorWidget, { timeout: 60000 }).should('be.visible').click();
  }

  selectCustomDropdown(dropdownSelector, optionText, index = 0) {
    cy.get(dropdownSelector).eq(index).click();
    cy.contains('li', optionText).click();
  }

  fillDiamondDetails(shape, color, clarity, carat, discount) {
    if (shape) this.selectCustomDropdown(this.selectors.dropdown, shape, 0);
    if (color) this.selectCustomDropdown(this.selectors.dropdown, color, 1);
    if (clarity) this.selectCustomDropdown(this.selectors.dropdown, clarity, 2);
    if (carat) cy.get(this.selectors.carat).type(carat);
    if (discount) cy.get(this.selectors.discountInput).clear().type(discount);
  }

  getPricePerCarat() {
    return cy.get(this.selectors.pricePerCarat).invoke('val');
  }

  getTotalPrice() {
    return cy.get(this.selectors.totalPrice).invoke('val');
  }

  enterDiscount(discount) {
    cy.get(this.selectors.discountInput).clear().type(discount);
  }

  clickIncreaseDiscount() {
    cy.get(this.selectors.plusButton).eq(1).click();
  }

  clickReset() {
    cy.get(this.selectors.resetButton).click();
  }

  verifyCalculatorVisible() {
    cy.get(this.selectors.calculatorWidget, { timeout: 60000 }).should('be.visible');
  }
  getDiscountedPricePerCarat() {
    return cy.get(this.selectors.discountInput).invoke('val');
  }

  verifyNegativeDiscount() {
    this.calculatorIcon();
    this.clickReset(); // Reset before applying discount
    // Fill diamond details initially (No discount applied yet)
    this.fillDiamondDetails('Round', 'D', 'VS1', '1.5', ' ');
    // Get original Price Per Carat and Total Price before discount
    this.getPricePerCarat().then((originalPPC) => {
      this.getTotalPrice().then((originalTotal) => {
        const basePPC = Number(originalPPC);
        const baseTotal = Number(originalTotal);
        const discountPercent = Number(10);
        // Apply negative discount
        this.enterDiscount(discountPercent.toString());
        // Calculate expected discounted values
        const discountFactor = 1 - discountPercent / 100;
        const expectedDiscountedPPC = basePPC * discountFactor;
        const expectedDiscountedTotal = expectedDiscountedPPC * Number(1.5);
        // Validate the discounted values
        this.getPricePerCarat().then((newPPC) => {
          expect(Number(newPPC)).to.be.closeTo(expectedDiscountedPPC, 1);
        });
        this.getTotalPrice().then((newTotal) => {
          expect(Number(newTotal)).to.be.closeTo(expectedDiscountedTotal, 1);
        });
      });
    });
  }

  verifyPositiveDiscount() {
    this.calculatorIcon();
    this.clickReset(); // Ensure reset before calculations
    // Fill diamond details with NO discount initially
    this.fillDiamondDetails('Round', 'D', 'VS1', '1.5', ' ');
    this.getPricePerCarat().then((originalPPC) => {
      const basePPC = Number(originalPPC);
      this.clickIncreaseDiscount();
      this.getDiscountedPricePerCarat().then((discountText) => {
        const expectedDiscountedPPC = basePPC * (1 + discountText / 100);
        const expectedDiscountedTotal = expectedDiscountedPPC * Number(1.5);
        this.getPricePerCarat().should('contain', expectedDiscountedPPC);
        this.getTotalPrice().should('contain', expectedDiscountedTotal);
      });
    });
  }
}

export default Calculator;
