/// <reference types="cypress" />

class Menu {
  constructor() {
    // Locators
    this.sidebarMenu = '[data-automation-id="admin-right-sidebar"]';
    this.selfConfirmHoldsToggle = '[data-automation-id="set-as-default-location-on-edit-location"]';
    this.refreshButton = '[data-automation-id="refresh-btn"]';
    this.spinner = '.fade-in';
    this.row = '[data-automation-id="table-row"]';
    this.holdUpdatesBtn = '.view-hold-updates-btn';
  }

  visitHoldRequest() {
    cy.get(this.sidebarMenu)
      .trigger('mouseover')
      .contains('Enquiries')
      .scrollIntoView()
      .click()
      .then(() => {
        cy.contains('Hold Request').should('be.visible').scrollIntoView().click();
      });
    cy.url().should('include', '/hold-requests');
  }
  visitEnquiriesInfoPage() {
    cy.get(this.sidebarMenu)
      .trigger('mouseover')
      .contains('Enquiries')
      .scrollIntoView()
      .click()
      .then(() => {
        cy.contains('Unanswered Info').should('be.visible').scrollIntoView().click();
      });
    cy.url().should('include', '/unanswered');
  }

  assertCertOnHoldsPage(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const certData = data[0]?.certNumber;
      const checkForCertificate = () => {
        cy.get(this.row, { timeout: 120000 }).should('be.visible');
        cy.get(this.holdUpdatesBtn, { timeout: 160000 }).should('be.visible').contains('View');

        return cy.get(this.row).then(($rows) => {
          const isCertFound = [...$rows].some((row) => row.innerText.includes(certData));
          return isCertFound;
        });
      };
      cy.reload();

      checkForCertificate().then((isCertFound) => {
        if (!isCertFound) {
          cy.log('Certificate not found, clicking toggle and retrying.');
          cy.get(this.selfConfirmHoldsToggle, { timeout: 60000 }).should('be.visible').click();
          cy.get(this.row, { timeout: 120000 }).should('be.visible');

          checkForCertificate().then((isCertFoundAfterToggle) => {
            expect(isCertFoundAfterToggle).to.be.true;
          });
        } else {
          expect(isCertFound).to.be.true;
        }
      });
    });
  }

  assertCertOnEnquiriesInfoPage(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const certData = data[0]?.certNumber;
      cy.reload();
      if (certData) {
        cy.contains(certData, { timeout: 60000 });
      }
    });
  }
}

export default Menu;
