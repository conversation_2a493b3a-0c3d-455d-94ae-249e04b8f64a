class Kyc {
  constructor() {
    // Locators
    this.spinner = '.fade-in';
    this.firstRowColumn = '[data-automation-id="table-column"]';
    this.businessLicenseName = '[data-automation-id="Enter Business License"]';
    this.companyLegalName = '[data-automation-id="Enter Company Legal Name"]';
    this.calendarPicker = '.react-date-picker__wrapper';
    this.datePicker = '.react-calendar__month-view__days > :nth-child(5)';
    this.dropDownField = '.settings__input';
    this.applicantName = '[data-automation-id="Enter Applicant\'s Name"]';
    this.countryDropDown = '.form-control.select.super_saver__input';
    this.directorName = '[data-automation-id="Enter Director\'s Name"]';
    this.uboName = '[data-automation-id="Enter UBO name"]';
    this.riskRatingReason = '[data-automation-id="Enter Risk Rating Reason"]';
    this.additionalComments = '[data-automation-id="Enter comments"]';
    this.approveButton = '.sc-iWdsyN';
    this.approveReason = '[data-automation-id="Enter Reason"]';
    this.proceedButton = '.yes-button';
    this.inputFileButton = 'input[type=file]';
    this.kycRow = ':nth-child(2) > .table__row';
    this.approvedPopUp = '.go3958317564 > div';
  }

  // Accessing the KYC-Pending Dashbaord
  accessKycPendingCompanyDashboard() {
    cy.intercept('POST', Cypress.env('adminApiUrl'), (req) => {
      if (
        req.body.query &&
        req.body.query.includes('company_kyc_checks_by_status') &&
        req.body.variables &&
        req.body.variables.status === 'KYC PENDING'
      ) {
        req.alias = 'kycPendingQuery';
      }
    }).as('kycPendingRequest');
    cy.visit('admin/kyc-checks/companies/kyc_pending');
    cy.wait('@kycPendingRequest', { timeout: 30000 }).then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
    });
    cy.url().should('include', '/companies/kyc_pending');
    cy.get(this.spinner, { timeout: 30000 }).should('not.be.visible');
    cy.get(this.firstRowColumn, { timeout: 30000 }).should('be.visible');
  }

  // Remaining in the same tab - otherwise it opens it in a new tab
  remainInSameKycTab() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.window().then((win) => {
      cy.stub(win, 'open').callsFake((url) => {
        if (url) {
          win.location.href = url;
        }
      });
    });
  }

  // Accessing the KYC Details Dashboard for a single company
  accessKycDetailsDashboard() {
    cy.get(this.kycRow, { timeout: 60000 }).should('be.visible').click();
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
  }

  // Filling Business Details
  fillBusinessKycDetails(outputFilePath) {
    cy.get(this.businessLicenseName, { timeout: 70000 }).type('Test-Data123');
    cy.get(this.inputFileButton).eq(0).attachFile(outputFilePath);
    cy.get(this.companyLegalName).type('Test Company');
    cy.get(this.calendarPicker).eq(0).click();
    cy.get(this.datePicker).click();
    cy.get(this.dropDownField).eq(1).select('Yes');
    cy.get(this.dropDownField).eq(2).select('Brick & Mortar Store');
    cy.get(this.dropDownField).eq(3).select('Private Limited Company');
    cy.get(this.dropDownField).eq(4).select('VAT Not Registered');
  }

  // Filling Individual Details
  fillIndividualDetails(outputFilePath) {
    cy.get(this.applicantName).clear().type('James Morris');
    cy.get(this.calendarPicker).eq(1).click();
    cy.get(this.datePicker).eq(1).click();
    cy.get(this.dropDownField).eq(5).select('Owner');
    cy.get(this.countryDropDown).eq(0).select('United States');
    cy.get(this.dropDownField).eq(6).select('Yes');
    cy.get(this.directorName).clear().type('Barron Trump');
    cy.get(this.calendarPicker).eq(2).click();
    cy.get(this.datePicker).eq(2).click();
    cy.get(this.countryDropDown).eq(1).select('Australia');
    cy.get(this.dropDownField).eq(7).select('Director');
    cy.get(this.inputFileButton).eq(4).attachFile(outputFilePath);
    cy.get(this.inputFileButton).eq(5).attachFile(outputFilePath);
  }

  // Filling UBO Details
  fillUboDetails() {
    cy.get(this.uboName).clear().type('Jonathan Edwards');
    cy.get(this.calendarPicker).eq(3).click();
    cy.get(this.datePicker).eq(3).click();
    cy.get(this.countryDropDown).eq(2).select('United Kingdom');
  }

  // Performing KYC Assessment
  performKycAssessment() {
    cy.get(this.dropDownField).eq(8).select('Low');
    cy.get(this.riskRatingReason).clear().type('Good');
    cy.get(this.additionalComments).clear().type('Low Risk Customer.');
    cy.contains('button', 'Approve').click();
    cy.get(this.approveReason).type('For testing purposes.');
    cy.get(this.proceedButton).click();
    cy.get(this.approvedPopUp, { timeout: 30000 })
      .should('be.visible')
      .and('contain.text', 'KYC Approved Successfully');
  }
}

export default Kyc;
