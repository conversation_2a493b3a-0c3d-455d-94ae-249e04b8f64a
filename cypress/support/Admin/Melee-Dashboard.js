class MeleeDashboard {
  constructor() {
    this.searchInput = '[data-testid="input_box_order_filter__search_input"]';
    this.tableColumn = ':nth-child(1) > .table__row > :nth-child(1) > [class^="sc-"]';
    this.checkboxMedia = '[data-automation-id="checkbox"]';
    this.actionButton = '.order_action__btn.confirmed_ready_to_collect';
    this.cityDropdown = '#react-select-4--value > .Select-value';
    this.cityMenu = '.Select-menu-outer';
    this.updateButton = '.admin_order__action--update';
    this.successMessage = '[class^="sc-"] > span';
    this.filterButton = '[data-testid="menu-action-apply-filters"]';
    this.applyMenu = '[data-testid="menu-action-apply"]';
    this.supplierFilterInputBox = '.menu_item__supplierId .Select--single';
    this.selectMenuOption = '.Select-menu';
    this.sendReportButton = '[data-testid="melee-reference-report-page-menu"]';
    this.sendButton = '[data-testid="menu-action-apply"]';
    this.confirmationModal = '[data-automation-id="pop-msg"]';
    this.closeConfirmationModal = '[data-automation-id="close-btn"]';
    this.closeMenuButton = '[data-testid="menu-action-close"]';
    this.referenceReportRows = '.table__body_row  .table__row';
    this.referenceReportColumns = '[data-automation-id="table-column"]';
    this.tableCheckBox = '[data-automation-id="table_checkbox"]';
    this.activeSelection = '.isActive';
    this.counterPill = '.filterColumns__inputs__meleeCount';
  }

  searchOrder(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then(({ orderNumber }) => {
      cy.get(this.searchInput, { timeout: 10000 }).should('be.enabled').clear().type(`${orderNumber}{enter}`);
    });
  }

  viewAndConfirmOrder() {
    cy.get(this.tableColumn, { timeout: 10000 }).click();
    cy.get(this.checkboxMedia, { timeout: 5000 }).eq(1).click();
    cy.get(this.actionButton, { timeout: 5000 }).should('contain.text', `Confirm + RTC`).click();
    cy.get('body').then(($body) => {
      if ($body.find(this.updateButton).length > 0) {
        cy.get(this.cityDropdown, { timeout: 5000 }).click();
        cy.get(this.cityMenu, { timeout: 5000 }).contains('Mumbai').click();
        cy.get(this.updateButton, { timeout: 5000 }).contains('Update').click();
        cy.get(this.successMessage, { timeout: 10000 })
          .should('be.visible')
          .and('contain.text', 'updated successfully');
      }
    });
  }

  applySupplierFilterReferenceReport(supplierName) {
    cy.get(this.activeSelection, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.filterButton, { timeout: 2000 }).eq(0).click();
    cy.get(this.supplierFilterInputBox).click().type(`${supplierName}{enter}`);
    cy.get(this.applyMenu).should('be.visible').click();
    cy.get(this.counterPill, { timeout: 5000 }).should('be.visible').contains('Showing');
  }

  sendReferenceReport() {
    cy.get(this.sendReportButton).should('be.visible').and('contain.text', 'Send report').click();
    cy.get(this.sendButton, { timeout: 1000 }).should('be.visible').and('contain.text', 'Send').click();
    cy.get(this.confirmationModal).should('contain.text', 'Email sent');
    cy.get(this.closeConfirmationModal).should('be.visible').click();
    cy.get(this.closeMenuButton).should('be.visible').click();
  }

  selectReferenceReport(valueToSearch) {
    let flag = false;

    cy.get(this.referenceReportRows).each(($row) => {
      if (flag) return;

      cy.wrap($row)
        .find(this.referenceReportColumns)
        .eq(11)
        .invoke('text')
        .then((text) => {
          if (text.trim() === valueToSearch && !flag) {
            flag = true;

            cy.wrap($row)
              .find(this.tableCheckBox)
              .should('be.visible')
              .click()
              .should('have.attr', 'aria-checked', 'true');
          }
        });
    });
  }

  checkDuplicateReferenceNumber() {
    const referenceNumbers = [];

    cy.get(this.referenceReportRows)
      .should('be.visible')
      .each(($row) => {
        cy.wrap($row).within(() => {
          cy.get(this.referenceReportColumns)
            .eq(11)
            .invoke('text')
            .then((text) => {
              const trimmed = text.trim();
              if (trimmed && trimmed !== '-') {
                referenceNumbers.push(trimmed);
              }
            });
        });
      });

    cy.then(() => {
      const duplicates = referenceNumbers.filter((value, index, self) => self.indexOf(value) !== index);
      expect(duplicates, `Duplicate reference numbers found: ${duplicates.length}`).to.be.empty;
    });
  }
}

export default MeleeDashboard;
