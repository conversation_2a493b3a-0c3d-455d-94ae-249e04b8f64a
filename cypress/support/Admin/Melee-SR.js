/// <reference types="cypress" />

class MeleeSR {
  constructor() {
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.tableHeader = '[data-automation-id="table-row"]';
    this.tableColumn = '[data-automation-id="table-column"]';
    this.newRequestBtn = '[class^="spr-topSec__section"]';
    this.searchField = '[data-automation-id="search-input"]';
    this.searchIcon = '.search-btn';
    this.companyNameInputField = '#companyName-input-field';
    this.selectCompanyField = '.Select-placeholder';
    this.selectLocationField = '.Select-placeholder';
    this.options = '.Select-option';
    this.shape = '[data-testid="special_request_shape"]';
    this.color = '[data-testid="special_request_color"]';
    this.cut = '[data-testid="special_request_cut"]';
    this.clarity = '[data-testid="special_request_clarity--component"]';
    this.nextButton = '[data-testid="special_request--step2"]';
    this.colorButton = '[data-testid="special_request_color--component"] > [class^="sc-"] > :nth-child(1)';
    this.meleeSrNo = '.table__row > :nth-child(1) > a';
    this.backBtn = '.meleeSprDetails__goBack';
    this.viewDetailsBtn = '[data-automation-id="view-details-button"]';
    this.filterBtn = '[data-testid="menu-action-apply-filters"]';
    this.editBtn = 'svg[width="24"][height="24"][color="black"]';
    this.step2NextButton = '[data-testid="special_request--confirmDetails"]';
    this.inputFields = '[data-automation-id="placeholder"]';
    this.title = '._title';
    this.saveBtn = '[data-testid="special_request_confirmDetails"]';
    this.addDetailsBtn = '.addDetails';
    this.quotationInputFields = '[data-automation-id="placeholder"]';
    this.calculatedMargin = '.profitPercentage';
    this.addQuoteSaveBtn = '[data-testid="special_reques--addQuote"]';
    this.closeBtn = '[data-automation-id="close-btn"]';
    this.marginError = '.profitPercentError';
    this.actionBtn = '.actBtn';
    this.statusContainer = '.status__container';
    this.sendQuoteBtn = '[data-automation-id="send-button"]';
    this.inputLocation = '.input__location_id > .Select > .Select-control > .Select-arrow-zone';
    this.inputCompanyField = '[style="width: 70%;"] > .Select > .Select-control > .Select-arrow-zone';
    this.memoInputField = '[data-testid="amc_input_memo_number"]';
    this.parcelsInput = '.input__parcel > .Select > .Select-control > .Select-arrow-zone';
    this.seieveAmountField1 = ':nth-child(1) > :nth-child(2) > [data-testid="amc_input_custom_sieve_size"]';
    this.seieveAmountField2 = ':nth-child(2) > :nth-child(2) > [data-testid="amc_input_custom_sieve_size"]';
    this.addMeleeInventoryButton = '[data-testid="amc_add_melee_button"]';
    this.feedSuccess = '.feed_success';
    this.inputStockIDField = '.form-control';
    this.searchStock = '.stockId > [class^="sc-"]';
    this.closeIcon = '.close';
    this.createOrderButton = '.actBtn__container > :nth-child(1)';
    this.goToCreateOrderBtn = '.createOrder__actBtn__container > [class^="sc-"]';
    this.selectCompanyOrderField = '[data-testid="customer_company_select"]';
    this.createOrderMelee = '[data-testid="create-order-1"]';
    this.orderText = '#orderNumber > span';
    this.applyFiltersButton = '[data-testid="menu-action-apply-filters"]';
    this.supplierDropdown =
      '.menu_item__supplier_id > .menu_item__filter > .Select > .Select-control > .Select-arrow-zone';
    this.applyButton = '[data-testid="menu-action-apply"]';
    this.ellipsisMenu = '[data-automation-id="ellipsismenu"]';
    this.menuItem = '.at_menu__item';
    this.statusDropdown = '#react-select-15--value-item';
    this.saveButton = '.action-btn';
    this.copyClipboard = '.copy_clipboard';
    this.closeButton = '[data-automation-id="close-btn"]';
    this.isDisabled = '.is-disabled';
    this.statusFilterDropdown =
      '.menu_item__status > .menu_item__filter > .Select > .Select-control > .Select-arrow-zone';
    this.stoneInfoContainer = '.stoneInfo__container';
    this.orderNumberLink = '[style="display: flex; align-items: start; gap: 4px;"] > a';
    this.showQuotesField = '.showQuotes';
    this.rejectedArrow = '.arrowRejected';
    this.companyFilterDropdown =
      '.menu_item__customer_id > .menu_item__filter [style="margin-top: -14px;"] .Select-control .Select-arrow-zone';
    this.inventoryCarats = ':nth-child(1) > .form-group > :nth-child(2) > .form-control';
    this.selectInventoryButton = '.createAndLink';
    this.sidebarSelector = '[data-automation-id="admin-right-sidebar"]';
    this.applyChangesButton = '[data-automation-id="apply-changes-btn"]';
    this.messageWrapper = '.go2072408551.notification-wrapper';
    this.meleeSRConfirmToast = '.new-toaster-title';
    this.companyFieldOnCreateOrder = '[data-testid="create-search-company-name-input"]';
    this.addInventoryPageFields = '.amc__main_row_label';
    this.addInventoryDropdownField = '.Select-value-label';
    this.addInventoryInputField = '.form-group';
    this.inventoryId = '.stockIdvalue';
  }

  verifyMeleeSrFields() {
    cy.get(this.tableHeader, { timeout: 5000 })
      .should('be.visible')
      .should('contain.text', 'SR number')
      .should('contain.text', 'Request date')
      .should('contain.text', 'Source')
      .should('contain.text', 'Company')
      .should('contain.text', 'Type')
      .should('contain.text', 'Melee Details')
      .should('contain.text', 'Size & Qty')
      .should('contain.text', 'Order number')
      .should('contain.text', 'Assignee')
      .should('contain.text', 'Actions');
    cy.get(this.tableColumn, { timeout: 50000 }).should('contain.text', 'View Details');
  }

  createRequestVisibilityAssertion() {
    cy.get(this.newRequestBtn, { timeout: 5000 }).should('be.visible').contains('Create new request');
  }

  searchSR(fixtureFileName) {
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.fixture(fixtureFileName).then((data) => {
      const requestNumber = data[0].order_request_number;
      cy.intercept('https://px.ads.linkedin.com/wa/', { statusCode: 403 }).as('blockLinkedInAd');

      cy.contains('NATURAL', { timeout: 50000 });
      cy.intercept('POST', adminApiUrl, (req) => {
        if (req.operationName === 'getCompanyAdmins') {
        }
      }).as('getCompanyAdmins');
      cy.intercept('POST', adminApiUrl, (req) => {
        if (req.operationName === 'getMeleespecialRequest') {
        }
      }).as('getMeleespecialRequest');
      cy.get(this.searchField, { timeout: 5000 }).should('be.visible').clear().type(`${requestNumber}`);
      cy.wait(2000);
      cy.get(this.searchIcon, { timeout: 5000 }).eq(0).should('be.visible');
      cy.get(this.searchIcon, { timeout: 5000 }).eq(0).click();
      cy.wait('@getCompanyAdmins').its('response.statusCode').should('eq', 200);
      cy.wait('@getMeleespecialRequest').its('response.statusCode').should('eq', 200);
      cy.get(this.tableHeader, { timeout: 5000 }).eq(2).should('be.visible').should('contain.text', requestNumber);
    });
  }

  searchInvalidSR(requestNumber) {
    cy.get(this.searchField, { timeout: 5000 }).should('be.visible').clear().type(`${requestNumber}{enter}`);
    cy.wait(2000);
    cy.get(this.searchIcon, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.contains('No melee special request found');
  }

  createSrRequest(fixtureFileName, shape, clarity, cut) {
    cy.fixture(fixtureFileName).then((data) => {
      const compName = data[0].name;
      console.log(compName);
      cy.get(this.newRequestBtn, { timeout: 5000 }).should('be.visible').contains('Create new request').click();
      cy.get(this.companyNameInputField, { timeout: 5000 }).should('be.visible').type(compName);
      cy.get(this.selectCompanyField, { timeout: 50000 }).eq(0).should('be.visible').click();
      cy.get(this.options, { timeout: 50000 }).eq(0).should('be.visible').contains(compName).click();
      cy.get(this.selectCompanyField, { timeout: 5000 }).should('be.visible').click();
      cy.get(this.options, { timeout: 50000 }).should('be.visible').eq(0).click();
      cy.get(this.shape, { timeout: 5000 }).should('be.visible').contains(shape).click();
      cy.get(this.colorButton).contains('D').click();
      cy.get(this.clarity, { timeout: 5000 }).should('be.visible').contains(clarity).click();
      cy.get(this.cut, { timeout: 5000 }).should('be.visible').contains(cut).click();
      cy.get(this.nextButton).contains('Next').click();
    });
  }

  verifyMeleeDeatilsPageThroughSrNumber() {
    cy.get(this.meleeSrNo, { timeout: 5000 }).should('be.visible').click();
    cy.contains('Special Request Details');
    cy.url({ timeout: 10000 }).should('include', 'admin/special-requests/melee/details/SR');
    cy.get(this.backBtn, { timeout: 5000 }).should('be.visible').click();
  }

  verifyMeleeDeatilsPageThroughViewDeatils() {
    cy.get(this.viewDetailsBtn).should('contain.text', 'View Details').click();
    cy.contains('Special Request Details');
    cy.url({ timeout: 10000 }).should('include', 'admin/special-requests/melee/details/SR');
  }

  verifyFilterOptions() {
    cy.contains('Type').click();
    cy.get(this.filterBtn, { timeout: 5000 }).should('be.visible').click({ force: true });
    cy.contains('Customer');
    cy.contains('Request Date');
    cy.contains('Assignee');
    cy.contains('Source');
  }

  verifyCustomerListUnderFiltrs() {
    cy.get(this.companyFilterDropdown, { timeout: 50000 }).eq(0).should('be.visible').click();
    cy.get(this.options, { timeout: 50000 }).should('be.visible');
  }

  editMeleeDeatils() {
    cy.get(this.editBtn, { timeout: 5000 }).first().should('be.visible').click();
    cy.get(this.nextButton, { timeout: 5000 }).contains('Next').click();
    cy.contains('Quantity & Size').scrollIntoView();
    cy.get(this.inputFields, { timeout: 5000 }).eq(0).dblclick({ force: true }).type('12', { force: true });
    cy.get(this.inputFields, { timeout: 5000 }).eq(1).dblclick({ force: true }).type('12', { force: true });
    cy.get(this.inputFields, { timeout: 5000 }).eq(2).dblclick({ force: true }).type('12', { force: true });
    cy.get(this.step2NextButton, { timeout: 5000 }).contains('Next').click();
    cy.get(this.saveBtn, { timeout: 5000 }).contains('Save').click();
    // cy.get(this.title).contains('Melee special request parcels updated');
  }

  addQuotationData(size1, size2, carats, costPrice, sellPrice) {
    cy.get(this.addDetailsBtn, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.get(this.quotationInputFields, { timeout: 5000 }).eq(1).click().clear().type(size1, { delay: 500 });
    cy.get(this.quotationInputFields, { timeout: 5000 }).eq(2).click().clear().type(size2, { delay: 500 });
    cy.get(this.quotationInputFields, { timeout: 5000 }).eq(4).click().clear().type(carats, { delay: 500 });
    cy.get('div[style*="margin-top: -16px"]')
      .should('contain.text', 'Location')
      .within(() => {
        cy.get(this.selectLocationField).should('be.visible').click();
      });
    cy.get(this.options, { timeout: 5000 }).eq(0).click();
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(5)
      .dblclick({ force: true })
      .clear()
      .type(costPrice, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(6)
      .dblclick({ force: true })
      .clear()
      .type(sellPrice, { force: true });
  }

  updateQuotationData(size1, size2, carats, costPrice, sellPrice) {
    cy.get(this.addDetailsBtn, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(1)
      .dblclick({ force: true })
      .clear()
      .type(size1, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(2)
      .dblclick({ force: true })
      .clear()
      .type(size2, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 }).eq(4).click({ force: true }).type(carats);
    cy.get('div[style*="margin-top: -16px"]')
      .should('contain.text', 'Location')
      .within(() => {
        cy.get(this.selectLocationField).should('be.visible').click();
      });
    cy.get(this.options, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(5)
      .dblclick({ force: true })
      .clear()
      .type(costPrice, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(6)
      .dblclick({ force: true })
      .clear()
      .type(sellPrice, { force: true });
  }

  positiveMarginAssertion() {
    cy.get(this.calculatedMargin).contains('Margin').scrollIntoView().contains('25.00%');
    cy.get(this.addQuoteSaveBtn, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.closeBtn, { timeout: 5000 }).should('be.visible').click({ force: true });
  }
  negativeMarginAssertion() {
    cy.get(this.marginError)
      .contains('Margin')
      .scrollIntoView()
      .contains('-87.50%')
      .should('have.css', 'color', 'rgb(255, 0, 0)');
  }

  selectInventoryAssertions() {
    cy.get(this.addDetailsBtn, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.contains('Select Inventory');
    cy.contains(
      'Add a quote by searching for an existing inventory on platform. Quote added can be shared later with the customer'
    );
    cy.contains('Step 1');
    cy.contains('Only Active, Live inventory will be shown');
    cy.get(this.actionBtn, { timeout: 5000 }).should('be.visible');
  }

  addQuotationWithMissingData() {
    cy.get(this.addDetailsBtn, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(1)
      .dblclick({ force: true })
      .clear()
      .type('12', { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(2)
      .dblclick({ force: true })
      .clear()
      .type('12', { force: true });
    cy.get('div[style*="margin-top: -16px"]')
      .should('contain.text', 'Location')
      .within(() => {
        cy.get(this.selectLocationField).should('be.visible').click();
      });

    cy.get(this.options, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.get(this.addQuoteSaveBtn, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.closeBtn, { timeout: 5000 }).should('be.visible').click({ force: true });
  }

  disabledStatusAssertion() {
    cy.wait(5000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .eq(-1)
      .contains('OPEN')
      .scrollIntoView({
        offset: { top: -70, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.options).contains('QUOTE READY').should('be.visible').and('have.class', 'is-disabled');
  }

  changeStatusAndSendQuote() {
    cy.get(this.addQuoteSaveBtn, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.closeBtn, { timeout: 5000 }).should('be.visible').click({ force: true });
    cy.wait(5000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.options).contains('QUOTE READY').should('be.visible').click();
    cy.get(this.actionBtn, { timeout: 5000 })
      .contains('Send to customer')
      .scrollIntoView({
        offset: { bottom: -80, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .click();
    cy.get(this.sendQuoteBtn, { timeout: 5000 }).should('be.visible').contains('Send').scrollIntoView().click();
    cy.get(this.closeBtn, { timeout: 5000 }).should('be.visible').click();
    cy.wait(5000);
    cy.get(this.statusContainer, { timeout: 10000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('contain.text', 'PENDING CONFIRMATION');
  }

  addAndSaveModal() {
    cy.get(this.addQuoteSaveBtn, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.closeBtn, { timeout: 5000 }).should('be.visible').click({ force: true });
  }

  acceptQuote() {
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.wait(4000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.intercept('POST', adminApiUrl, (req) => {
      if (req.operationName === 'modifyMeleeSrQuote') {
      }
    }).as('modifyMeleeSrQuote');
    cy.get(this.options, { timeout: 5000 })
      .contains('ACCEPTED')
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.wait('@modifyMeleeSrQuote').its('response.statusCode').should('eq', 200);
  }

  createInventory() {
    cy.contains('Create Inventory').click();
    cy.get(this.inputCompanyField, { timeout: 5000 })
      .eq(0)
      .click()
      .then(() => {
        cy.get(this.options, { timeout: 5000 }, { timeout: 50000 }).first().should('be.visible').click();
      });
    cy.get(this.inputLocation, { timeout: 5000 })
      .click()
      .then(() => {
        cy.get(this.options, { timeout: 5000 }, { timeout: 50000 }).first().should('be.visible').click();
      });
    cy.get(this.memoInputField, { timeout: 5000 }).clear().type('11');
    cy.get(this.parcelsInput, { timeout: 5000 })
      .click()
      .then(() => {
        cy.get(this.options, { timeout: 5000 }, { timeout: 50000 }).first().should('be.visible').click();
      });
    cy.get(this.seieveAmountField1, { timeout: 5000 }).clear().type('1');
    cy.get(this.seieveAmountField2, { timeout: 5000 }).clear().type('1');
    cy.get(this.addMeleeInventoryButton, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.feedSuccess, { timeout: 50000 }).click();
    cy.go(-1);
  }

  makeStockLive() {
    cy.visit('admin/melee/page/repository');
    cy.get(this.applyFiltersButton, { timeout: 50000 }).eq(0).should('be.visible').click();
    cy.get(this.supplierDropdown, { timeout: 50000 }).click();
    cy.get(this.options, { timeout: 5000 }).eq(0).click();
    cy.get(this.statusFilterDropdown)
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.options, { timeout: 5000 }).contains('Inactive').click();
    cy.get(this.applyButton, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.ellipsisMenu, { timeout: 5000 }).eq(2).should('be.visible').click();
    cy.get(this.menuItem, { timeout: 5000 }).should('be.visible').eq(0).click();
    cy.get(this.statusDropdown, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.options, { timeout: 5000 }, { timeout: 5000 }).should('be.visible').contains('Active').click();
    cy.get(this.saveButton, { timeout: 5000 }).should('be.visible').contains('Save').click();
    cy.get(this.copyClipboard, { timeout: 5000 })
      .click()
      .then(() => {
        cy.get(this.copyClipboard, { timeout: 5000 })
          .invoke('text')
          .then((fullText) => {
            const stockNumber = fullText.match(/AID-\d{13}/)?.[0];
            cy.wrap(stockNumber).as('copiedStockNumber');
          });
      });
    cy.get(this.closeButton, { timeout: 5000 }).click();
    cy.visit('admin/special-requests/melee');
  }

  selectInventory() {
    cy.get(this.selectInventoryButton, { timeout: 5000 }).eq(1).contains('Select Inventory').click();
    cy.get('@copiedStockNumber').then((stockNumber) => {
      cy.get(this.inputStockIDField, { timeout: 5000 }).clear().type(stockNumber);
    });
    cy.get(this.searchStock, { timeout: 5000 }).click();
    cy.contains('Save').click();
    cy.get(this.closeIcon, { timeout: 5000 }).click();
  }

  verifySelectedInventory() {
    cy.get('@copiedStockNumber').then((stockNumber) => {
      cy.get(this.inventoryId, { timeout: 5000 }).contains(stockNumber);
    });
  }

  createOrder(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const compName = data[0].name;

      cy.get(this.createOrderButton, { timeout: 5000 }).click();
      cy.get(this.goToCreateOrderBtn, { timeout: 5000 }).eq(1).should('be.visible').click();
      cy.get(this.companyFieldOnCreateOrder).clear().type(compName).press(Cypress.Keyboard.Keys.TAB);
      cy.get(this.selectCompanyOrderField, { timeout: 5000 }).should('be.visible').select(1);
      cy.get(this.createOrderMelee, { timeout: 5000 }).should('be.visible').click();
      cy.get(this.orderText, { timeout: 50000 }).should('be.visible').contains('Melee Order created!');
    });
  }

  quoteStatusDisabledAssertion() {
    cy.wait(4000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .within(() => {
        cy.get(this.isDisabled).should('exist').and('be.visible');
      });
  }

  orderNumberVisibleAssertionOnMeleeSr() {
    cy.get(this.orderNumberLink).should('be.visible').should('contain.text', 'NIV-');
  }

  orderNumberNavigationAssertionOnMeleeSr() {
    cy.get(this.orderNumberLink).should('be.visible').should('contain.text', 'NIV-').click();
    cy.url().should('include', 'admin/melee/orders/');
  }

  editQuotationData(size1, size2, carats, costPrice, sellPrice) {
    cy.get(this.editBtn, { timeout: 5000 }).eq(1).should('be.visible').click();
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(1)
      .dblclick({ force: true })
      .clear()
      .type(size1, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(2)
      .dblclick({ force: true })
      .clear()
      .type(size2, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 }).eq(3).dblclick({ force: true }).type(carats, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(5)
      .dblclick({ force: true })
      .clear()
      .type(costPrice, { force: true });
    cy.get(this.quotationInputFields, { timeout: 5000 })
      .eq(6)
      .dblclick({ force: true })
      .clear()
      .type(sellPrice, { force: true });
    this.addAndSaveModal();
    cy.get(this.stoneInfoContainer).should('contain.text', costPrice);
  }

  quoteStatusesAssertion() {
    const expectedStatuses = [
      { text: 'OPEN' },
      { text: 'INTERNAL FOLLOW UP' },
      { text: 'QUOTE READY' },
      { text: 'ACCEPTED' },
      { text: 'REJECTED' }
    ];
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .contains('OPEN')
      .scrollIntoView({
        offset: { top: -70, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();

    expectedStatuses.forEach(({ text }) => {
      cy.get(this.options)
        .contains(text)
        .scrollIntoView({
          offset: { top: -70, left: 0 },
          easing: 'linear',
          duration: 500
        })
        .should('be.visible');
    });
  }

  sendToCustomerAssertion() {
    cy.get(this.actionBtn, { timeout: 5000 }).contains('Send to customer').should('be.disabled');
    cy.wait(3000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.options).contains('QUOTE READY').should('be.visible').click();
    cy.get(this.actionBtn, { timeout: 5000 })
      .contains('Send to customer')
      .scrollIntoView({
        offset: { bottom: -80, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.enabled');
  }

  createOrderButtonAssertion() {
    cy.get(this.createOrderButton, { timeout: 5000 })
      .scrollIntoView({
        offset: { bottom: -80, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.disabled');
    this.acceptQuote();
    cy.get(this.createOrderButton, { timeout: 5000 })
      .scrollIntoView({
        offset: { bottom: -80, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.enabled');
  }

  collapsibleSliderAssertion() {
    cy.wait(3000);
    cy.get(this.statusContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.options, { timeout: 5000 })
      .contains('WITHDRAWN')
      .scrollIntoView({
        offset: { top: -60, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .should('be.visible')
      .click();
    cy.get(this.showQuotesField, { timeout: 5000 }).should('be.visible').contains('Show Rejected / Withdrawn');
    cy.get(this.rejectedArrow, { timeout: 5000 }).should('be.visible').click();
  }

  verifyActiveTab(tab) {
    cy.get(`#${tab}`, { timeout: 5000 }).should('be.visible').should('have.class', 'isActive');
  }

  verifyStatusTabs() {
    const defaultTimeout = 5000;
    const tabs = ['#Open', '#In-Progress', '#Int\\.\\ Follow-Up', '#Quoted', '#Accepted', '#Done', '#Archived'];

    cy.get(this.sidebarSelector, { timeout: defaultTimeout }).trigger('mouseover');
    cy.get('body').trigger('mousemove', { clientX: 0, clientY: 0 }).dblclick();
    tabs.forEach((tab) => {
      cy.get(tab, { timeout: defaultTimeout }).should('be.visible');
    });
  }

  verifySRCreationWhenQuantityExceeds() {
    cy.get(this.applyChangesButton, { timeout: 6000 }).should('be.visible').click();
    cy.get(this.nextButton).contains('Next').click();
    cy.get(this.step2NextButton, { timeout: 5000 }).contains('Next').click();
    cy.get(this.saveBtn, { timeout: 5000 }).contains('Submit request').click();
    cy.get(this.messageWrapper).contains('Melee has been removed from the cart');
    cy.get(this.meleeSRConfirmToast, { timeout: 5000 }).should('be.visible').contains('Request submitted');
  }

  assertDropdownFieldValue(labelText, expectedValue) {
    cy.contains('label', labelText)
      .parent()
      .next()
      .find(this.addInventoryDropdownField)
      .should('have.text', expectedValue);
  }

  assertInputFields(labelText, expectedValue, occurrence = 0) {
    cy.get(this.addInventoryPageFields)
      .filter((_, el) => Cypress.$(el).text().trim().startsWith(labelText))
      .eq(occurrence)
      .closest(this.addInventoryInputField)
      .find('input')
      .should('have.value', expectedValue);
  }

  assertCreateInventoryPopulatedFields(
    meleeType,
    quoteLocation,
    meleeColor,
    meleeShape,
    meleeInventoryType,
    meleeCut,
    meleeClarity,
    quoteMmSize,
    quoteCarats,
    quotePieces,
    quotePricePerCarat,
    quoteSellPricePerCarat
  ) {
    cy.get(this.selectInventoryButton, { timeout: 5000 }).eq(0).should('be.visible').click();
    cy.url().should('include', 'admin/melee/page/add-inventory');

    this.assertDropdownFieldValue('Category', meleeType);
    this.assertDropdownFieldValue('Location', quoteLocation);
    this.assertDropdownFieldValue('Color', meleeColor);
    this.assertDropdownFieldValue('Clarity', meleeClarity);
    this.assertDropdownFieldValue('Cut', meleeCut);
    this.assertDropdownFieldValue('Shape', meleeShape);
    this.assertDropdownFieldValue('Type', meleeInventoryType);
    this.assertInputFields('Custom mm size', quoteMmSize, 0);
    this.assertInputFields('Custom mm size', quoteMmSize, 1);
    this.assertInputFields('Cost Price Per Carat', quotePricePerCarat);
    this.assertInputFields('Sell Price Per Carat', quoteSellPricePerCarat);
    this.assertInputFields('Total Carats', quoteCarats);
    this.assertInputFields('QC accepted stones', quotePieces);
  }
}
export default MeleeSR;
