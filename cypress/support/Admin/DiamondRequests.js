/// <reference types="cypress" />

class DiamondRequests {
  constructor() {
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.sidebarMenu = '[data-automation-id="admin-right-sidebar"]';
    this.buyRequestButton = '[class^="sc-"] > .btn.btn-wdc.action_btn';
    this.filterButton = '[data-automation-id="filter-btn"]';
    this.filterInputField = '[data-automation-id="input_box_"]';
    this.filterradioButton = '[data-target-name="RADIO"] > span';
    this.filterApplyButton = '[data-automation-id="apply-filter-btn"]';
    this.tableRow = '[data-automation-id="gia-cert__number_hover"]';
    this.makeRequestButton = '.btn.btn-wdc.req_btn';
    this.assignBuyerField = '.ast__container';
    this.searchBuyerField = '[data-automation-id="input_box_"]';
    this.selectBuyerContainer = '.select-container';
    this.assignButton = '.footer-action_btn--continue';
    this.priceInputField = '[data-automation-id="placeholder"]';
    this.companyListItem = '.custom_list__item';
    this.companyLocationField = '.form-control.select_override';
    this.addStoneToNivoda = '.left-section [class^="sc-"].btn.btn-wdc.action_btn';
    this.successModal = '.message';
    this.closeModalButton = '[class^="sc-"] > .btn.btn-wdc.modal_action-btn';
    this.buyRequestTab = '[data-action-id="BUY"]';
    this.holdRequestTab = '[data-action-id="HOLD"]';
    this.infoRequestTab = '[data-action-id="INFO"]';
    this.addStoneToPlatform = '.gbc-container--right > [class^="sc-"].btn.btn-wdc.action_btn';
  }

  VerifyGIAPurchaseOrderRequest(dataFileName) {
    cy.readFixtureFile(dataFileName).then((data) => {
      const diamondRequestNumber = data[0].certNumber;
      cy.log(`${diamondRequestNumber}`);
      cy.get(this.sidebarMenu).trigger('mouseover');
      cy.get(this.sidebarMenu).contains('Diamond Requests').scrollIntoView().invoke('show').click();
      cy.wait(1000);
      cy.get(this.buyRequestButton, { timeout: 90000 }).should('be.visible');
      cy.get(this.buyRequestButton, { timeout: 90000 }).eq(0).trigger('mouseover');
      cy.get(this.filterButton).click({ force: true });
      cy.get(this.filterInputField).click().type(`${diamondRequestNumber}`);
      cy.get(this.filterradioButton).click({ force: true });
      cy.get(this.filterApplyButton).eq(0).click();
      cy.get(this.tableRow, { timeout: 60000 })
        .eq(0)
        .should('have.text', `${diamondRequestNumber}GIA ${diamondRequestNumber}`);
    });
  }

  visitBuyRequestTab() {
    cy.get(this.buyRequestTab).contains('Buy').click().should('have.class', 'active');
  }

  visitHoldRequestTab() {
    cy.get(this.holdRequestTab).contains('Hold').click().should('have.class', 'active');
  }

  visitInfoRequestTab() {
    cy.get(this.infoRequestTab).contains('Info').click().should('have.class', 'active');
  }

  addDiamondAndPerformAction(dataFileName) {
    const defaultTimeout = 60000;
    cy.readFixtureFile(dataFileName).then((data) => {
      const companyData = data[0].name;
      cy.get(this.assignBuyerField, { timeout: defaultTimeout }).click();
      cy.get(this.searchBuyerField, { timeout: defaultTimeout }).click().type('Andre');
      cy.get(this.selectBuyerContainer, { timeout: defaultTimeout }).contains('Andre').click();
      cy.get(this.assignButton, { timeout: defaultTimeout }).click();
      cy.get(this.buyRequestButton, { timeout: defaultTimeout }).click();
      cy.get(this.priceInputField, { timeout: defaultTimeout }).eq(0).type('40');
      cy.get(this.priceInputField, { timeout: defaultTimeout }).eq(2).clear().type('-40');
      cy.get(this.searchBuyerField, { timeout: defaultTimeout }).eq(0).clear().type(`${companyData}`);

      // Wait for the company list to load and then click with force
      cy.get(this.companyListItem, { timeout: defaultTimeout })
        .contains(`${companyData}`)
        .should('be.visible')
        .click({ force: true });

      // Verify the company was selected
      cy.get(this.searchBuyerField, { timeout: defaultTimeout }).eq(0).should('have.value', companyData);
      cy.get(this.companyLocationField, { timeout: defaultTimeout })
        .eq(0)
        .then(($select) => {
          const firstOptionValue = $select.find('option').eq(1).val();
          cy.wrap($select).select(firstOptionValue);
        });
      cy.url().then((url) => {
        if (url.includes('buy')) {
          cy.get(this.addStoneToPlatform, { timeout: defaultTimeout }).contains('Nivoda').click();
          cy.get(this.successModal, { timeout: defaultTimeout }).should('be.visible');
          cy.get(this.closeModalButton, { timeout: defaultTimeout }).click();
          cy.get(this.addStoneToNivoda, { timeout: defaultTimeout }).contains('Create order').click();
          cy.get(this.successModal, { timeout: defaultTimeout })
            .should('be.visible')
            .contains('Order created successfully');
        } else if (url.includes('hold')) {
          cy.get(this.addStoneToNivoda, { timeout: defaultTimeout })
            .contains('Add stone to Nivoda and create hold')
            .click();
          cy.get(this.successModal, { timeout: defaultTimeout })
            .should('be.visible')
            .contains('The stone has been successfully added');
          cy.get(this.closeModalButton).click();
        } else {
          cy.get(this.addStoneToNivoda, { timeout: defaultTimeout })
            .contains('Add stone to Nivoda and request Information')
            .click();
          cy.get(this.successModal, { timeout: defaultTimeout })
            .should('be.visible')
            .contains('The stone has been successfully added');
          cy.get(this.closeModalButton, { timeout: defaultTimeout }).click();
        }
      });
    });
  }
}

export default DiamondRequests;
