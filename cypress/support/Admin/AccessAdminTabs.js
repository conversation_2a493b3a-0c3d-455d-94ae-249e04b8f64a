/// <reference types="cypress" />

class AccessAdminTabs {
  constructor() {
    // Locators
    this.sidebarMenu = '[data-automation-id="admin-right-sidebar"]';
    this.spinner = '.fade-in';
    this.certificateNumber = '[data-automation-id="certificate-number"]';
    this.title = 'span.wdc-nav--title';
    this.statusRow = '.top-row__status-row';
    this.viewDetails = '.btn-wdc.btn-sm';
  }

  accessTabs(mainTab, sidebarTab, subTab, url) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.sidebarMenu).trigger('mouseover');

    if (mainTab) {
      cy.get(this.sidebarMenu)
        .contains('span', new RegExp(`^${mainTab}$`))
        .scrollIntoView()
        .invoke('show')
        .click();
    }
    if (sidebarTab) {
      cy.get(this.title).contains(sidebarTab).scrollIntoView().invoke('show').click({ force: true });
    }
    if (subTab) {
      cy.contains(subTab, { timeout: 38000 })
        .should(($el) => {
          expect($el).to.have.css('color').not.eq('rgb(140, 140, 140)');
        })
        .invoke('show')
        .click({ force: true });
    }
    cy.url({ timeout: 10000 }).should('include', url);
    cy.contains('Something went wrong. Please contact support.').should('not.exist');
  }

  viewDetailsClick() {
    cy.get(this.viewDetails).eq(0).click();
    cy.contains('Something went wrong. Please contact support.').should('not.exist');
  }

  accessTabsCustom(mainTab, sidebarTab, subTab, url) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.sidebarMenu).trigger('mouseover');
    cy.get(this.sidebarMenu, { timeout: 60000 })
      .contains(mainTab, { timeout: 60000 }, { matchCase: false })
      .scrollIntoView()
      .invoke('show')
      .should('be.visible', { timeout: 10000 })
      .should('not.be.disabled')
      .click({ force: true });
    cy.get(this.sidebarMenu, { timeout: 60000 })
      .contains(sidebarTab, { timeout: 60000 }, { matchCase: false })
      .scrollIntoView()
      .invoke('show')
      .should('be.visible', { timeout: 10000 })
      .should('not.be.disabled')
      .click({ force: true });
    cy.get('body').click(0, 0);
    cy.get(this.statusRow, { timeout: 60000 })
      .contains(subTab, { timeout: 60000 })
      .scrollIntoView()
      .invoke('show')
      .should('be.visible', { timeout: 10000 })
      .should('not.be.disabled')
      .click();
    cy.url().should('include', url);
    cy.contains('Something went wrong. Please contact support.').should('not.exist');
  }

  accessLocationShipments(mainTab, countryCode, minitab, url) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.sidebarMenu).trigger('mouseover');
    if (mainTab) {
      cy.get(this.sidebarMenu).contains(mainTab).scrollIntoView().invoke('show').click();
    }
    cy.get(`a[href="/admin/office-orders/${countryCode}/${minitab}"]`).eq(0).click();
    cy.url({ timeout: 10000 }).should('include', url);
    cy.contains('Something went wrong. Please contact support.').should('not.exist');
  }
}

export default AccessAdminTabs;
