/// <reference types="cypress" />

class Returns {
  constructor() {
    // Locators
    this.orderInputField = '[data-automation-id="input_box_purchaseOrderNumber"]';
    this.companyInputField = '[data-automation-id="input_box_companyName"]';
    this.certInputField = '[data-automation-id="input_box_certificateNumber"]';
    this.actionBtnReturns = '[data-automation-id="action--btn"]';
    this.returnsTableRow = '[data-automation-id="table-row"]';
    this.selectReasonField = '.return__reason > .Select > .Select-control > .Select-arrow-zone';
    this.optionsList = '.custom_list__item';
    this.reasonList = '.return__reason';
    this.initiateBtn = '[class^="sc-"] > [class^="sc-"] .btn.btn-wdc';
    this.uiModal = '[data-automation-id="ui_modal"]';
    this.closeBtn = '[data-automation-id="close-btn"]';
    this.headers = '[data-automation-id="finance-tab-header"]';
    this.inputBox = '[data-automation-id="input_box_"]';
    this.qcWrapperBtn = '.qc-action-wrapper';
    this.actionOptionsList = '.content_wrapper__box';
    this.statusHeaders = '.icp__filter-container__filter-area';
    this.receievBtn = ':nth-child(12) > .initiated_customer_action > [class^="sc-"]';
    this.refreshBtn = '[data-automation-id="refresh-btn"]';
    this.spinner = '.fade-in';
    this.reasonInputField = '.return__reason-other__textbox';
    this.initiateReturnTab = '#Initiatereturn';
    this.initiatedReturnTab = '#InitiatedReturns';
    this.initiatedInternalTab = '#Initiatedinternal';
    this.searchButton = '[data-automation-id="action--btn"]';
    this.refreshButton = '[data-automation-id="refresh-btn"]';
    this.toAcceptTab = '[data-action-id="TO_ACCEPT"]';
    this.toReceiveTab = '[data-action-id="TO_RECEIVE"]';
    this.toQcTab = '[data-action-id="TO_QC"]';
    this.toTagTab = '[data-action-id="TO_TAG"]';
    this.ExpressToStoreTab = '[data-action-id="EXPRESS_TO_STORE"]';
    this.certSearch = '#search_by_cert_no';
    this.denialReasonSelector = 'select';
    this.denyReturnButton = 'button:contains("Deny Return")';
    this.denyAndSendBackButton = 'button:contains("Deny & Send Back")';
    this.denyingButton = 'button:contains("Denying Return...")';
  }

  initiateReturn(fixtureFileName, fixtureFileName1, fixtureFileName2) {
    const defaultTimeout = 60000;
    cy.readFixtureFile(fixtureFileName).then((orderData) => {
      const orderNumber = orderData.orderNumber;
      cy.log(`orderNumber: ${orderNumber}`);

      cy.readFixtureFile(fixtureFileName1).then((compData) => {
        const compName = compData[0].name;
        cy.log(`compName: ${compName}`);

        cy.readFixtureFile(fixtureFileName2).then((certData) => {
          const certNo = certData[0].certNumber;
          cy.log(`certNo: ${certNo}`);

          cy.get(this.orderInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(orderNumber, { force: true });
          cy.get(this.companyInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(compName, { force: true });
          cy.get(this.optionsList, { timeout: defaultTimeout }).contains(compName).click({ force: true });
          cy.get(this.certInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(certNo, { force: true });
          cy.get(this.actionBtnReturns, { timeout: defaultTimeout }).should('be.visible').click();
          cy.get(this.returnsTableRow, { timeout: defaultTimeout }).contains('Initiate Return').click();
          cy.get(this.selectReasonField, { timeout: defaultTimeout }).click();
          cy.get(this.reasonList, { timeout: defaultTimeout }).contains('Other').click();
          cy.get(this.reasonInputField).type('Return');
          cy.get(this.initiateBtn, { timeout: defaultTimeout }).eq(3).should('be.visible').click();
          cy.get(this.uiModal, { timeout: defaultTimeout }).should('be.visible');
          cy.get(this.closeBtn, { timeout: defaultTimeout }).click();
          cy.get(this.headers, { timeout: defaultTimeout }).contains('Initiated Returns').click();
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.enabled');

          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.visible').type(certNo, { delay: 1000 });
          cy.get(this.actionBtnReturns).click();
          cy.get(this.qcWrapperBtn, { timeout: defaultTimeout }).should('be.visible').click();
          cy.get(this.actionOptionsList).contains('Accept return').click();
          cy.get(this.statusHeaders, { timeout: defaultTimeout }).should('be.visible').contains('To receive').click();
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.enabled');
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.visible').clear().type(certNo, { delay: 1000 });
          cy.get(this.actionBtnReturns).click();
          cy.get(this.receievBtn, { timeout: defaultTimeout })
            .should('be.visible')
            .contains('Receive', { timeout: defaultTimeout })
            .click();
          cy.get(this.statusHeaders, { timeout: defaultTimeout }).should('be.visible').contains('To qc').click();
          cy.reload();
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.enabled');
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.visible').clear().type(certNo, { delay: 1000 });
          cy.get(this.actionBtnReturns).click();
          cy.get(this.qcWrapperBtn, { timeout: defaultTimeout }).should('be.visible').click();
          cy.get(this.actionOptionsList).contains('QC Pass').click();
          cy.wait(3000);
        });
      });
    });
  }

  denyReturnPopup(fixtureFileName, fixtureFileName1, fixtureFileName2) {
    const defaultTimeout = 60000;
    cy.readFixtureFile(fixtureFileName).then((orderData) => {
      const orderNumber = orderData.orderNumber;
      cy.log(`orderNumber: ${orderNumber}`);

      cy.readFixtureFile(fixtureFileName1).then((compData) => {
        const compName = compData[0].name;
        cy.log(`compName: ${compName}`);

        cy.readFixtureFile(fixtureFileName2).then((certData) => {
          const certNo = certData[0].certNumber;
          cy.log(`certNo: ${certNo}`);

          cy.get(this.orderInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(orderNumber, { force: true });
          cy.get(this.companyInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(compName, { force: true });
          cy.get(this.optionsList, { timeout: defaultTimeout }).contains(compName).click({ force: true });
          cy.get(this.certInputField, { timeout: defaultTimeout })
            .should('not.be.disabled')
            .type(certNo, { force: true });
          cy.get(this.actionBtnReturns, { timeout: defaultTimeout }).should('be.visible').click();
          cy.get(this.returnsTableRow, { timeout: defaultTimeout }).contains('Initiate Return').click();
          cy.get(this.selectReasonField, { timeout: defaultTimeout }).click();
          cy.get(this.reasonList, { timeout: defaultTimeout }).contains('Other').click();
          cy.get(this.reasonInputField).type('Return');
          cy.get(this.initiateBtn, { timeout: defaultTimeout }).eq(3).should('be.visible').click();
          cy.get(this.uiModal, { timeout: defaultTimeout }).should('be.visible');
          cy.get(this.closeBtn, { timeout: defaultTimeout }).click();
          cy.get(this.headers, { timeout: defaultTimeout }).contains('Initiated Returns').click();
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.enabled');

          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.visible').type(certNo, { delay: 1000 });
          cy.get(this.actionBtnReturns).click();
          cy.get(this.qcWrapperBtn, { timeout: defaultTimeout }).should('be.visible').click();
          cy.get(this.actionOptionsList).contains('Accept return').click();
          cy.get(this.statusHeaders, { timeout: defaultTimeout }).should('be.visible').contains('To receive').click();
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.enabled');
          cy.get(this.inputBox, { timeout: defaultTimeout }).should('be.visible').clear().type(certNo, { delay: 1000 });
          cy.get(this.actionBtnReturns).click();
          cy.get(this.receievBtn, { timeout: defaultTimeout })
            .should('be.visible')
            .contains('Deny', { timeout: defaultTimeout })
            .click();

          cy.get(this.denialReasonSelector).should('be.visible').select('Return received after Deadline');
        });
      });
    });
  }

  denyReturn(fixtureFileName, fixtureFileName1, fixtureFileName2) {
    this.denyReturnPopup(fixtureFileName, fixtureFileName1, fixtureFileName2);

    cy.get(this.denyReturnButton).click();
    cy.get(this.denyingButton).should('be.disabled');
  }

  denyAndSendBackReturn(fixtureFileName, fixtureFileName1, fixtureFileName2) {
    const defaultTimeout = 60000;
    this.denyReturnPopup(fixtureFileName, fixtureFileName1, fixtureFileName2);

    cy.window().then((win) => {
      cy.stub(win, 'open').as('windowOpen');
    });

    cy.get(this.denyAndSendBackButton).click();
    cy.get(this.denyingButton).should('be.disabled');
    cy.get('@windowOpen', { timeout: defaultTimeout }).should('have.been.called');
  }
}

export default Returns;
