/// <reference types="cypress" />

class AdminFeed {
  constructor() {
    // Locators
    this.expandNaturalFeedButton = 'span[title="Expand natural feeds"]';
    this.expandLabgrownFeedButton = 'span[title="Expand labgrown feeds"]';
    this.expandUnknownFeedButton = 'span[title="Expand unknown feeds"]';
    this.searchCustomerNameInputButton = '[data-testid="input_box_"]';
    this.searchActionButton = '[data-automation-id="action--btn"]';
    this.expandFeedButton = '.sc-iyvyFf';
    this.searchNaturalFeed = 'input[placeholder="Search in natural feeds"]';
    this.searchLabgrownFeed = 'input[placeholder="Search in labgrown feeds"]';
    this.searchUnkownFeed = 'input[placeholder="Search in unknown feeds"]';
    this.editFeedButton = 'button.right__action_btn';
    this.activeButton = '.sc-gwVKww';
    this.saveButton = '.section__action__btn';
    this.closeDialogue = '.close';
  }
  clickActionButton() {
    cy.get(this.searchActionButton, { timeout: 5000 }).click();
  }

  changeFeedStatusAndCloseDialogue() {
    this.storeCheckboxStatus(this.activeButton, 'wasCheckboxChecked');
    cy.get(this.activeButton, { timeout: 5000 }).click();
    cy.get(this.saveButton, { timeout: 5000 }).click();
    cy.wait(3000);
    cy.get(this.closeDialogue, { timeout: 5000 }).click();
  }
  waitForSupplierNametoVisible(supplierName) {
    cy.wait(3000);
    cy.contains('a', supplierName, { timeout: 20000 }).should('be.visible');
  }
  clickEditFeedButton() {
    cy.get(this.editFeedButton, { timeout: 5000 }).click({ force: true });
  }
  expandNaturalFeed() {
    this.expandCustomerFeeds();
    cy.get(this.expandNaturalFeedButton, { timeout: 40000 }).should('be.visible').should('not.be.disabled').click();
  }
  expandCustomerFeeds() {
    cy.get(this.expandFeedButton, { timeout: 20000 }).should('be.visible').and('not.be.disabled').click();
  }
  expandLabgrownFeed() {
    this.expandCustomerFeeds();
    cy.get(this.expandLabgrownFeedButton, { timeout: 40000 }).click();
  }
  expandUnknownFeed() {
    this.expandCustomerFeeds();
    cy.get(this.expandUnknownFeedButton, { timeout: 40000 }).click();
  }
  searchCustomerName(name) {
    cy.get(this.searchCustomerNameInputButton, { timeout: 5000 }).type(name);
    cy.get(this.searchActionButton, { timeout: 20000 })
      .should('exist')
      .should('be.visible')
      .should('not.be.disabled')
      .click();
  }
  searchSupplierNameInNaturalFeed(name) {
    cy.get(this.searchNaturalFeed, { timeout: 20000 }).click({ force: true }).type(name, { delay: 100 });

    this.waitForSupplierNametoVisible(name);
  }
  searchSupplierNameInLabGrownFeed(name) {
    cy.get(this.searchLabgrownFeed, { timeout: 5000 }).type(name);
    this.waitForSupplierNametoVisible(name);
  }
  searchSupplierNameInUknownFeed(name) {
    cy.get(this.searchUnkownFeed, { timeout: 5000 }).type(name);
    this.waitForSupplierNametoVisible(name);
  }

  getFeedCount(feedTitle, label) {
    return cy
      .get(`span[title="${feedTitle}"]`)
      .closest('.admin_feeds_sub_group__label')
      .then(($section) => {
        if (label === 'Total Stones') {
          return cy
            .wrap($section)
            .find('.admin_feeds_sub_group__stones--count')
            .invoke('text')
            .then((text) => parseInt(text.replace(/,/g, '').trim(), 10));
        } else {
          return cy
            .wrap($section)
            .find(`div[title="${label}"]`)
            .invoke('text')
            .then((text) => {
              const countText = text.split(':')[1];
              return parseInt(countText.trim(), 10);
            });
        }
      });
  }
  storeFeedCount(feedType, feedCategory, aliasName) {
    this.getFeedCount(feedType, feedCategory).then((count) => {
      cy.wrap(count).as(aliasName);
    });
  }

  storeCheckboxStatus(selector, aliasName = 'wasCheckboxChecked') {
    return cy
      .get(selector)
      .invoke('attr', 'aria-checked')
      .then((checked) => {
        const isChecked = checked === 'true';
        cy.wrap(isChecked).as(aliasName);
      });
  }

  assertCountChangedBasedOnCheckbox(beforeAlias, afterAlias, checkboxAlias) {
    cy.get(`@${beforeAlias}`).then((beforeCount) => {
      cy.get(`@${afterAlias}`).then((afterCount) => {
        cy.get(`@${checkboxAlias}`).then((wasChecked) => {
          if (wasChecked) {
            expect(beforeCount).to.be.greaterThan(afterCount);
          } else {
            expect(afterCount).to.be.greaterThan(beforeCount);
          }
        });
      });
    });
  }

  assertActiveCountChangedBasedOnCheckbox(activeCountBeforeAlias, activeCountAfterAlias, checkboxAlias) {
    cy.get(`@${activeCountBeforeAlias}`).then((beforeActiveCount) => {
      cy.get(`@${activeCountAfterAlias}`).then((afterActiveCount) => {
        cy.get(`@${checkboxAlias}`).then((wasChecked) => {
          if (wasChecked) {
            expect(afterActiveCount).to.equal(beforeActiveCount - 1);
          } else {
            expect(afterActiveCount).to.equal(beforeActiveCount + 1);
          }
        });
      });
    });
  }
  assertRevokedCountChangedBasedOnCheckbox(revokedCountBeforeAlias, revokedCountAfterAlias, checkboxAlias) {
    cy.get(`@${revokedCountBeforeAlias}`).then((beforeRevokedCount) => {
      cy.get(`@${revokedCountAfterAlias}`).then((afterRevokedCount) => {
        cy.get(`@${checkboxAlias}`).then((wasChecked) => {
          if (wasChecked) {
            expect(afterRevokedCount).to.equal(beforeRevokedCount + 1);
          } else {
            expect(afterRevokedCount).to.equal(beforeRevokedCount - 1);
          }
        });
      });
    });
  }
  setCustomerFromFixture(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((fixtureData) => {
      const customerId = fixtureData[0].id;
      const customerName = fixtureData[0].name;

      Cypress.env('customerId', customerId);
      Cypress.env('customerName', customerName);

      cy.log(`Customer loaded: ${customerName} (${customerId})`);
    });
  }

  searchSupplierFeed(password, customerId, diamondType) {
    cy.authenticateUser('loginasadmin.json', password, null).then(() => {
      cy.getSupplierWithInventory(customerId, diamondType).then((supplierName) => {
        cy.log('Supplier:', supplierName);
        cy.log('Feed Type:', diamondType);

        const feedSearchMap = {
          Natural: (name) => this.searchSupplierNameInNaturalFeed(name),
          Labgrown: (name) => this.searchSupplierNameInLabGrownFeed(name),
          Gemstone: (name) => this.searchSupplierNameInUknownFeed(name)
        };

        const searchMethod = feedSearchMap[diamondType];
        if (!searchMethod) {
          throw new Error(`Invalid feed type: ${diamondType}`);
        }

        searchMethod(supplierName);
      });
    });
  }
}

export default AdminFeed;
