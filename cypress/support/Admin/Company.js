/// <reference types="cypress" />

class Company {
  constructor() {
    // Locators
    this.adminSearchBar = '.global_search_bar';
    this.searchItem = '[data-automation-id="search-item"]';
    this.compResults = '[data-automation-id="name"]';
    this.titleField = '.order-overview__title';
    this.viewCreditNotesBtn = ':nth-child(4) > .section__action-button';
    this.creditNoteItem = ':nth-child(1) > .invoice_number > a';
    this.voidCreditNotebtn = 'span > .sc-bdVaJa';
    this.resizableBoxField = '.resizable__box';
    this.actionBox = '._action-box';
    this.setting = '.settings_title';
    this.financialSettingTab = '[data-automation-id="advanced-financial-settings"]';
    this.kycAssessmentPageLink = '.link_to_kyc_assessment_page';
    this.creditKYCHeader = '.order-overview__title';
    this.creditLimitInput = '#credit_limit_input';
    this.accountLimitInput = '#account_limit_input';
    this.paymentMethodDropdown = ':nth-child(4) > .form-group > .settings__input';
    this.checkoutTypeDropdown = ':nth-child(5) > .form-group > .settings__input';
    this.approveButton = '.kyc_assessment_action_btn';
    this.confirmButton = '.yes-button';
    this.successMessage = '.go3958317564 > div';
    this.commentsField = '[data-automation-id="Enter Credit Comments"]';
    this.saveButton = '[data-automation-id="save-btn"]';
    this.progressInfoModal = '.upload__progress-info';
    this.currencyDropdown = '.section__settings > :nth-child(2) > :nth-child(1) > .form-group > .settings__input';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.kriyaFinanceLimitField = '[data-automation-id="Days Allowed After Due Date"]';
    this.checkboxCheckedClass = 'checkbox_checked';
    this.insuranceLimitInput = '[data-automation-id="Insurance Limit"]';
    this.insuranceCurrencyDropdown = 'div.insurance_currency > .form-group > .settings__input';
    this.memoPenaltyField = '[data-automation-id="Memo return penalty"]';
    this.emailOption = '.unit_search_row_item > :nth-child(3)';
    this.compNameRow = ':nth-child(6) > a > .underline_on_hover';
  }

  visitCompanyPage(fixtureFileName) {
    const defaultTimeout = 15000;

    cy.readFixtureFile(fixtureFileName).then((compData) => {
      const compName = compData[0].name;
      const email = compData[0].email;

      cy.get(this.adminSearchBar, { timeout: defaultTimeout })
        .eq(0)
        .should('be.visible')
        .click()
        .clear()
        .type(email, { delay: 100 });

      cy.get(this.emailOption, { timeout: defaultTimeout }).click();
      cy.get(this.compNameRow, { timeout: defaultTimeout }).should('be.visible').contains(compName).click();
      cy.get(this.titleField, { timeout: ********* }).should('contain.text', compName);
    });
  }

  voidCreditNote(fixtureFileName) {
    const defaultTimeout = 15000;

    this.visitCompanyPage(fixtureFileName);

    cy.get(this.viewCreditNotesBtn, { timeout: defaultTimeout }).should('be.visible').click();
    cy.get(this.creditNoteItem, { timeout: defaultTimeout }).should('be.visible').click();
    cy.get(this.voidCreditNotebtn, { timeout: defaultTimeout }).should('be.visible').click();
    cy.get(this.resizableBoxField, { timeout: defaultTimeout }).should('be.visible').type('No Longer Needed');
    cy.get(this.actionBox, { timeout: defaultTimeout }).should('be.visible').contains('Submit').click();
  }
  accessCompanySetting() {
    cy.get(this.setting).contains('Company Settings').click().scrollIntoView();
  }

  visitTab(tabName) {
    const defaultTimeout = 15000;

    cy.get(this.financialSettingTab, { timeout: defaultTimeout })
      .eq(0)
      .should('contain.text', 'Financial Settings')
      .scrollIntoView()
      .click();
    cy.get(this.setting, { timeout: defaultTimeout }).contains(tabName).should('be.visible').click();
  }

  toggleCheckbox(index, enable) {
    const defaultTimeout = 15000;

    cy.get(this.checkBox, { timeout: defaultTimeout })
      .eq(index)
      .scrollIntoView()
      .then(($checkbox) => {
        const isChecked = $checkbox.hasClass(this.checkboxCheckedClass);
        if (enable && !isChecked) {
          cy.log(`Enabling checkbox`);
          cy.wrap($checkbox).click();
        } else if (!enable && isChecked) {
          cy.log(`Disabling checkbox`);
          cy.wrap($checkbox).click();
        } else {
          cy.log(`Checkbox is already in the desired state.`);
        }
      });
  }

  updateField(selector, value) {
    const defaultTimeout = 15000;

    cy.get(selector, { timeout: defaultTimeout })
      .scrollIntoView({
        offset: { top: -200, left: 0 },
        easing: 'linear',
        duration: 500
      })
      .clear()
      .type(value);
  }

  saveAndVerify() {
    const defaultTimeout = 15000;
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.get(this.saveButton, { timeout: defaultTimeout }).should('contain.text', 'Save').scrollIntoView().click();
    cy.intercept('POST', adminApiUrl, (req) => {
      if (req.body.operationName === 'getAdminSingleCompanyDetails') {
        req.alias = 'saveApiCall';
      }
    });
    cy.wait('@saveApiCall').its('response.statusCode').should('eq', 200);
    cy.get(this.progressInfoModal, { timeout: defaultTimeout }).should(
      'contain.text',
      'Settings have been updated successfully'
    );
  }

  visitFinanceSettingsPage() {
    const defaultTimeout = 60000;

    cy.get(this.financialSettingTab, { timeout: defaultTimeout })
      .eq(0)
      .should('contain.text', 'Financial Settings')
      .scrollIntoView()
      .click();
    cy.get(this.kycAssessmentPageLink, { timeout: defaultTimeout }).eq(1).click();
    cy.get(this.creditKYCHeader, { timeout: defaultTimeout }).should('be.visible').contains('Credit KYC Assessment');
  }

  visitInvoiceSettings() {
    this.visitTab('Invoice Settings');
  }

  visitKriyaSettings() {
    this.visitTab('Kriya Credit Settings');
  }

  visitPricingSettings() {
    this.visitTab('Pricing');
  }

  visitNivodaCreditSettings() {
    this.visitTab('Nivoda Credit Settings');
  }

  enableDisableKriya(limit) {
    this.toggleCheckbox(0, true);
    this.toggleCheckbox(0, true);
    this.updateField(this.kriyaFinanceLimitField, limit);
    this.saveAndVerify();
    this.toggleCheckbox(0, false);
    this.toggleCheckbox(0, false);
    this.updateField(this.kriyaFinanceLimitField, limit);
    this.saveAndVerify();
  }

  enableDisableAIP() {
    this.toggleCheckbox(0, true);
    this.saveAndVerify();
    this.toggleCheckbox(0, false);
    this.saveAndVerify();
  }

  enableDisableCheckout() {
    this.toggleCheckbox(3, true);
    this.saveAndVerify();
    this.toggleCheckbox(3, false);
    this.saveAndVerify();
  }

  enableDisableCreditNoteAutoApplication() {
    this.toggleCheckbox(2, true);
    this.saveAndVerify();
    this.toggleCheckbox(2, false);
    this.saveAndVerify();
  }

  updateCurrency(currency) {
    cy.get(this.currencyDropdown).select(currency);

    this.saveAndVerify();
  }

  updateInsuranceLimitAndCurrency(limit, currency) {
    this.updateField(this.insuranceLimitInput, limit);

    cy.get(this.insuranceCurrencyDropdown).select(currency);

    this.saveAndVerify();
  }

  updateCreditLimit() {
    const randomCreditLimit = Math.floor(Math.random() * (50000 - 10000 + 1)) + 10000;

    this.updateField(this.creditLimitInput, randomCreditLimit.toString());
  }

  updateAccountLimit() {
    const randomAccountLimit = Math.floor(Math.random() * (50000 - 10000 + 1)) + 10000;

    this.updateField(this.accountLimitInput, randomAccountLimit.toString());
  }

  changePaymentMethod(paymentMethod) {
    cy.get(this.paymentMethodDropdown).eq(1).select(paymentMethod);
  }

  changeCheckoutType(checkoutType) {
    cy.get(this.checkoutTypeDropdown).eq(1).select(checkoutType);
  }

  approveAndVerifyChanges() {
    const defaultTimeout = 15000;

    cy.get(this.approveButton, { timeout: defaultTimeout })
      .eq(0)
      .should('contain.text', 'approve')
      .scrollIntoView()
      .click();
    cy.get(this.confirmButton, { timeout: defaultTimeout }).click();
    cy.get(this.successMessage).should('contain.text', 'Credit Verification Details have been successfully saved.');
  }

  rejectAssessment() {
    this.updateField(this.accountLimitInput, '0');
    this.updateField(this.creditLimitInput, '0');
    this.updateField(this.commentsField, 'Rejecting Advance Payment Request');

    cy.get(this.approveButton).eq(1).should('contain.text', 'reject').scrollIntoView().click();
    cy.get(this.confirmButton).click();
    cy.get(this.successMessage).should('contain.text', 'Credit Verification Details have been successfully saved.');
  }

  updateMemoReturnPenalty(amount) {
    this.updateField(this.memoPenaltyField, amount);
    this.saveAndVerify();
  }
}

export default Company;
