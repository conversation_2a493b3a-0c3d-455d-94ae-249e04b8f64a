/// <reference types="cypress" />

class Shipment {
  constructor() {
    // Locators
    this.shipmentFromField = '[data-automation-id="shipment-from"]';
    this.shipmentToField = '[data-automation-id="shipment-to"]';
    this.courierField = '[data-automation-id="courier-service"]';
    this.serviceTypeField = '[data-automation-id="service-type"]';
    this.refreshButton = '[data-automation-id="refresh-btn"]';
    this.shipmentProceedButton = '[data-automation-id="shipment-proceed"]';
    this.scannedOrderInput = '[data-automation-id="scanned-order-input"]';
    this.scanOrderInputSbs = '.sc-hBAMER hJKaoV';
    this.shipmentProceedToDetailsPageButton = '[data-automation-id="back-btn"]';
    this.proceedToShipmentPageButton = '[data-automation-id="shipment-proceed-btn"]';
    this.popUpModal = '[data-automation-id="pop-msg"]';
    this.closePopUpModal = '[data-automation-id="close-btn"]';
    this.shipmentDetailsTable1 = '.table__row > :nth-child(3) > :nth-child(1) > :nth-child(2)';
    this.finalizeShipmentButton = '[data-automation-id="shipment-finalizing"]';
    this.trackingCheckbox = '[data-automation-id="checkbox"]';
    this.trackingCodeField = '[data-automation-id="tracking-code"]';
    this.submitTrackingCodeBtn = '[data-automation-id="shipment-btn"]';
    this.trackingSuccess = '[data-automation-id="ui_modal"]';
    this.trackingSuccessClose = '[data-automation-id="close-btn"]';
    this.trackingCode = 'span a[href*="trackTrace"]';
    this.incomingTrackingCode = '[data-automation-id="tracking-shipment-code"]';
    this.sbsButton = '[data-automation-id="tab-header"]';
    this.sbsDataRow = '[data-automation-id="table-row"]';
    this.finalizeSbsLM = 'button.sc-bocRTG.cLsQHQ';
    this.addWeightBtn = '.dotted_link';
    this.weightInputField = '.form-control';
    this.saveWeightBtn = '[class^="sc-"]  .btn.btn-wdc.action_save';
    this.dashboardContentModal = '#DashboardContent';
    this.shipmentDetailsTable = '.shipment_details__dnt';
    this.prepareEnvelopeBtn = '[data-automation-id="prepare-envelop"]';
    this.shipmentScanningBtn = '[data-automation-id="shipment-scanning"]';
    this.scannedInputField = '[data-automation-id="scanned-input"]';
    this.markEnvelopeDoneBtn = '[data-automation-id="mark-envelop-done"]';
    this.backBtn = '[data-automation-id="back-btn"]';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.customerNameField = '[class*="sc-"][class*="form-control"]';
    this.companyNameField = '.form-control.select_override';
    this.addToShipmentBtn = '.btn.btn-wdc.btn-sm';
    this.viewShipmentBtn = '.card';
    this.shipmentHeader = '.shipment_header';
    this.generateExpressInvoicesBtn = '.kHYcQM';
    this.expressFinalizeShipmentBtn = '.sc-bdVaJa.fJylg.btn.btn-wdc';
    this.reloadBtn = '.message_container > [class^="sc-"]';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.clipboardSpan = '.copy_clipboard span';
    this.sbsEnvelopeTitle = '#sbs-envelope-title';
    this.detailsButton = '.cs_details-btn';
    this.mainCheckBox = '[data-automation-id="ui_checkbox"]';
    this.viewShipmentDetailsButton = '[data-layer="Content"]';
  }

  createShipmentAndVerifyAddress(
    shipmentFrom,
    shipmentTo,
    fixtureFileName1,
    fixtureFileName2,
    fixtureFileName3 = null
  ) {
    const defaultTimeout = 60000;

    cy.readFixtureFile(fixtureFileName1).then((locationData) => {
      const address1 = locationData[0].location_1;
      const address2 = locationData[0].location_2;
      cy.log(`Address1: ${address1}`);
      cy.log(`Address2: ${address2}`);

      cy.readFixtureFile(fixtureFileName2).then((orderData) => {
        const orderNumber1 = orderData.orderNumber;
        cy.log(`Order Number 1: ${orderNumber1}`);

        this.fillShipmentDetails(shipmentFrom, shipmentTo, defaultTimeout);

        if (fixtureFileName3) {
          cy.readFixtureFile(fixtureFileName3).then((orderData2) => {
            const orderNumber2 = orderData2.orderNumber;
            cy.log(`Order Number 2: ${orderNumber2}`);
            this.scanOrders([orderNumber1, orderNumber2]);
            this.proceedToNextSteps(orderNumber1, orderNumber2, address1, address2);
          });
        } else {
          this.scanOrders([orderNumber1]);
          this.proceedToNextSteps(orderNumber1, null, address1, address2);
        }
      });
    });
  }

  createShipmentAndVerifyAddressSbs(
    shipmentFrom,
    shipmentTo,
    fixtureFileName1,
    fixtureFileName2,
    fixtureFileName3 = null
  ) {
    const defaultTimeout = 60000;

    cy.readFixtureFile(fixtureFileName1).then((locationData) => {
      const address1 = locationData[0].location_1;
      const address2 = locationData[0].location_2;
      cy.log(`Address1: ${address1}`);
      cy.log(`Address2: ${address2}`);

      cy.readFixtureFile(fixtureFileName2).then((orderData) => {
        const orderNumber1 = orderData.orderNumber;
        cy.log(`Order Number 1: ${orderNumber1}`);

        this.fillShipmentDetails(shipmentFrom, shipmentTo, defaultTimeout);

        if (fixtureFileName3) {
          cy.readFixtureFile(fixtureFileName3).then((orderData2) => {
            const orderNumber2 = orderData2.orderNumber;
            cy.log(`Order Number 2: ${orderNumber2}`);
            this.scanOrders([orderNumber1, orderNumber2]);
            this.proceedToNextSteps(orderNumber1, orderNumber2, address1, address2);
            this.submitTrackingCode();
            cy.get(this.trackingSuccess).should('be.visible');
          });
        } else {
          this.scanOrders([orderNumber1]);
          this.proceedToNextSteps(orderNumber1, null, address1, address2);
          this.submitTrackingCode();
        }
      });
    });
  }

  proceedToNextSteps(orderNumber1, orderNumber2, address1, address2) {
    cy.get(this.proceedToShipmentPageButton, { timeout: 15000 }).click();
    cy.get(this.popUpModal, { timeout: 60000 })
      .should('be.visible')
      .contains('Scanned items are added to the shipment successfully.');
    cy.get(this.closePopUpModal, { timeout: 60000 }).click();
    cy.get(this.shipmentProceedToDetailsPageButton, { timeout: 15000 }).click();

    this.verifyAddress(address1, address2);
    this.addShipmentWeightAndCreateEnvelope(orderNumber1, orderNumber2);

    cy.get(this.finalizeShipmentButton, { timeout: 60000 }).click();
    cy.get(this.popUpModal, { timeout: 60000 }).should('be.visible').contains('Shipment finalized successfully');
    cy.get(this.closePopUpModal, { timeout: 60000 }).click();
  }

  submitTrackingCode() {
    const uniqueTrackingCode = `tracking-${Math.random().toString(36).substring(2, 8)}`;
    cy.get(this.trackingCheckbox).click();
    cy.get(this.trackingCodeField).type(uniqueTrackingCode);
    cy.get(this.submitTrackingCodeBtn).click();
    cy.get(this.trackingSuccess).should('be.visible').contains('Shipment tracking code added successfully');
    cy.get(this.trackingSuccessClose).click();
    const data = {
      trackingCodeValue: uniqueTrackingCode
    };

    cy.task('writeToFile', {
      filename: 'cypress/fixtures/trackingCode.json',
      data: data
    });
    cy.readFixtureFile('cypress/fixtures/trackingCode.json').then((fileContent) => {
      expect(fileContent).to.have.property('trackingCodeValue', uniqueTrackingCode);
    });
  }

  receiveShipmentByTrackingCode() {
    cy.readFixtureFile('cypress/fixtures/trackingCode.json').then(({ trackingCodeValue }) => {
      cy.get(this.incomingTrackingCode).each(($el) => {
        const text = $el.text().trim();
        if (text === trackingCodeValue) {
          cy.wrap($el)
            .closest('.group_header')
            .within(() => {
              cy.get('[data-automation-id="Received-shipment-office"]').click();
            });
          return false;
        }
      });
    });
  }

  fillShipmentDetails(shipmentFrom, shipmentTo, timeout) {
    cy.get(this.shipmentFromField, { timeout }).select(shipmentFrom);
    cy.get(this.shipmentToField, { timeout }).select(shipmentTo);
    cy.get(this.courierField, { timeout }).select('Brinks');
    cy.get(this.serviceTypeField, { timeout }).select('Brinks Door to Door');
    cy.get(this.shipmentProceedButton, { timeout }).click();
  }

  scanOrders(orderNumbers) {
    cy.wait(2000);
    orderNumbers.forEach((orderNo) => {
      cy.get(this.scannedOrderInput, { timeout: 60000 }).eq(0).click().type(orderNo).type('{enter}');
    });
  }

  verifyAddress(address1, address2) {
    const defaultTimeout = 60000;
    const address1Part = address1.split(',')[0].toLowerCase();
    const address2Part = address2.split(',')[0].toLowerCase();

    cy.get(this.shipmentDetailsTable1, { timeout: defaultTimeout })
      .invoke('text')
      .then((text) => {
        const uiText = text.trim().toLowerCase();
        const isAddress1Included = uiText.includes(address1Part);
        const isAddress2Included = uiText.includes(address2Part);
        expect(isAddress1Included || isAddress2Included).to.be.true;
      });
  }

  addShipmentWeightAndCreateEnvelope(orderNo1, orderNo2 = null) {
    this.addWeight();
    this.navigateToShipmentPage();
    this.generateRequiredDocuments();
    this.prepareAndScanEnvelope(orderNo1, orderNo2);
    if (orderNo2) {
      this.prepareAndScanEnvelope(orderNo1, orderNo2);
    }
  }

  addWeight() {
    cy.get(this.addWeightBtn, { timeout: 60000 }).eq(3).click();
    cy.get(this.weightInputField, { timeout: 60000 }).type(2);
    cy.get(this.saveWeightBtn, { timeout: 60000 }).click();
  }

  navigateToShipmentPage() {
    cy.get(this.dashboardContentModal, { timeout: 15000 }).contains('Go to Shipment Page').click();
  }

  generateRequiredDocuments() {
    cy.get(this.shipmentDetailsTable, { timeout: 90000 }).contains('Generate Commercial Invoice').click();
    cy.get(this.shipmentDetailsTable, { timeout: 90000 }).contains('Generate PPH Insurance form').click();
    cy.get(this.reloadBtn, { timeout: 15000 }).contains('Reload').click();
  }

  prepareAndScanEnvelope(orderNo1, orderNo2 = null) {
    cy.get(this.prepareEnvelopeBtn, { timeout: 90000 })
      .eq(0)
      .contains('Prepare envelope')
      .scrollIntoView()
      .should('be.visible')
      .should('not.be.disabled')
      .click();

    cy.get(this.shipmentScanningBtn, { timeout: 90000 }).should('be.visible').should('not.be.disabled').click();

    const scanAndHandleWarning = (orderToScan, fallbackOrder = null) => {
      cy.get(this.scannedInputField, { timeout: 90000 }).clear().type(orderToScan).type('{enter}');

      cy.wait(2000); // allow DOM to update

      cy.get('body').then(($body) => {
        if ($body.find('.cs_row.card.warn').length && fallbackOrder) {
          cy.log(`Warning shown for ${orderToScan}. Retrying with ${fallbackOrder}`);
          scanAndHandleWarning(fallbackOrder); // Retry with second order
        } else {
          cy.get(this.markEnvelopeDoneBtn, { timeout: 90000 }).should('be.visible').should('not.be.disabled').click();
          cy.wait(5000);
          cy.get(this.backBtn, { timeout: 16000 }).should('be.visible').click();
        }
      });
    };

    scanAndHandleWarning(orderNo1, orderNo2);
  }

  createLastMileShipment(shipmentFrom, fixtureFileName) {
    const defaultTimeout = 60000;

    cy.intercept('POST', '**/graphql-admin', (req) => {
      if (req.body.operationName === 'searchCustomerCompany') {
        req.alias = 'searchCustomerCompany';
      }
    }).as('searchCustomerCompanyRequest');

    cy.readFixtureFile(fixtureFileName).then((userData) => {
      const compName = userData[0].name;
      cy.log(`Company Name: ${compName}`);

      cy.get(this.mainCheckBox, { timeout: defaultTimeout }).each(($checkbox) => {
        const labelText = $checkbox.text().trim();
        if (labelText === 'Direct customer Shipment?') {
          cy.wrap($checkbox).find(this.checkBox).click();
        }
      });
      cy.get(this.shipmentFromField, { timeout: defaultTimeout }).select(shipmentFrom);
      cy.get(this.customerNameField, { timeout: defaultTimeout }).should('be.visible').type(compName);
      cy.wait('@searchCustomerCompanyRequest', {
        timeout: defaultTimeout
      }).then((interception) => {
        if (interception && interception.response) {
          expect(interception.response.statusCode).to.eq(200);
          const items = interception.response.body.data?.search_by_company?.items;
        }
      });
      cy.get(this.companyNameField, { timeout: defaultTimeout }).eq(1).should('be.enabled');
      cy.get(this.companyNameField)
        .eq(1)
        .then(($select) => {
          cy.wrap($select).select(compName);
        });

      cy.wait(4000);
      cy.get(this.courierField, { timeout: defaultTimeout }).select('Brinks');
      cy.get(this.serviceTypeField, { timeout: defaultTimeout }).select('Brinks Door to Door');
      cy.get(this.shipmentProceedButton, { timeout: defaultTimeout }).click();
      cy.url({ timeout: defaultTimeout }).should('include', '/add_items');
      cy.wait(5000);
      cy.get(this.checkBox, { timeout: defaultTimeout }).eq(0).should('be.visible').click();
      cy.get(this.addToShipmentBtn, { timeout: defaultTimeout }).eq(0).contains('Add to Shipment').click();
      cy.get(this.spinner, { timeout: defaultTimeout }).should('not.be.visible');
      cy.get('button', { timeout: defaultTimeout }).contains('View shipment details').click();
      cy.get(this.shipmentHeader, { timeout: defaultTimeout })
        .should('be.visible')
        .contains('Add Shipment Weight')
        .click();
      cy.get(this.spinner).should('not.be.visible');
      cy.get(this.weightInputField, { timeout: defaultTimeout }).type(2);
      cy.get(this.saveWeightBtn, { timeout: defaultTimeout }).click();
      cy.get(this.dashboardContentModal, { timeout: defaultTimeout }).contains('Go to Shipment Page').click();
      cy.get(this.generateExpressInvoicesBtn, { timeout: defaultTimeout }).contains('Generate Commercial').click();
      cy.get(this.expressFinalizeShipmentBtn, { timeout: defaultTimeout }).contains('Finalize Shipment').click();
    });
  }

  createSbsShipment(shipmentFrom, shipmentTo, fixtureFileName) {
    const defaultTimeout = 60000;
    this.fillShipmentDetails(shipmentFrom, shipmentTo, defaultTimeout);

    cy.readFixtureFile(fixtureFileName).then((orderData) => {
      const orderNumber = orderData.orderNumber;
      cy.log(`Order Number: ${orderNumber}`);

      this.scanOrders([orderNumber]);

      cy.get(this.clipboardSpan, { timeout: defaultTimeout })
        .invoke('text')
        .then((clipboardText) => {
          cy.get(this.sbsEnvelopeTitle, { timeout: defaultTimeout }).type(clipboardText).type('{enter}');
          cy.get(this.detailsButton, { timeout: defaultTimeout }).eq(0).click();
        });

      this.addWeight();
      this.navigateToShipmentPage();
      this.generateRequiredDocuments();

      cy.get(this.finalizeShipmentButton, { timeout: defaultTimeout }).click();
      cy.get(this.popUpModal, { timeout: defaultTimeout })
        .should('be.visible')
        .contains('Shipment finalized successfully');
      cy.get(this.closePopUpModal, { timeout: defaultTimeout }).click();
    });
  }

  scanSbsOrdersLM(orderNumbers) {
    cy.wait(2000);
    orderNumbers.forEach((orderNo) => {
      cy.get(this.scanOrderInputSbs, { timeout: 60000 }).eq(0).click().type(orderNo).type('{enter}');
    });
  }

  createSbsLMShipment(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((orderData) => {
      const orderNumber = orderData.orderNumber;
      cy.log(`Order Number: ${orderNumber}`);

      this.scanSbsOrdersLM([orderNumber]);
      cy.get(this.sbsDataRow).each(($row) => {
        const rowText = $row.text();

        orderNumbers.forEach((order) => {
          if (rowText.includes(order)) {
            cy.wrap($row)
              .find('button:contains("Add")')
              .should('not.be.disabled')
              .click()
              .then(() => {
                cy.wrap($row)
                  .find('span')
                  .contains('✔', { timeout: 15000 })
                  .should('have.css', 'color', 'rgb(0, 128, 0)');
              });
          }
        });
        cy.get(this.finalizeSbsLM).click();
      });
    });
  }

  createSbsLMShipment(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((orderData) => {
      const orderNumber = orderData.orderNumber;
      cy.log(`Order Number: ${orderNumber}`);

      this.scanSbsOrdersLM([orderNumber]);

      cy.get(this.sbsDataRow)
        .each(($row) => {
          const rowText = $row.text();

          if (rowText.includes(orderNumber)) {
            cy.wrap($row)
              .find('button:contains("Add")')
              .should('not.be.disabled')
              .click()
              .then(() => {
                cy.wrap($row)
                  .find('span')
                  .contains('✔', { timeout: 15000 })
                  .should('have.css', 'color', 'rgb(0, 128, 0)');
              });
          }
        })
        .then(() => {
          cy.get(this.finalizeSbsLM, { timeout: 15000 }).click();
        });
    });
  }
}
export default Shipment;
