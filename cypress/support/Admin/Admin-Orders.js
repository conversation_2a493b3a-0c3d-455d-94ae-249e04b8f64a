/// <reference types="cypress" />

class AdminOrders {
  constructor() {
    // Locators
    this.productTypeDropdown = '[data-testid="create-product-type-select-box"]';
    this.companyNameInput = '[data-testid="create-search-company-name-input"]';
    this.companyField = '[data-automation-id="select-company"]';
    this.certField = '[data-automation-id="Enter cert number"]';
    this.searchResults = '[data-testid="create-search-result-options"]';
    this.requestTypeDropdown = '[data-automation-id="select-request"]';
    this.createOrderBtn = '[data-automation-id="create-order-btn"]';
    this.successBanner = '[data-testid="info_card__green"] > span > div';
  }

  createCustomOrder(stoneType, fixtureFileName1, fixtureFileName2) {
    cy.readFixtureFile(fixtureFileName1).then((compData) => {
      const compName = compData[0].name;

      cy.readFixtureFile(fixtureFileName2).then((certificateData) => {
        const certificateNo = certificateData[0].certNumber;

        cy.get(this.productTypeDropdown, { timeout: 60000 }).should('be.visible').select(stoneType);
        cy.get(this.companyNameInput, { timeout: 60000 }).should('be.visible').click().type(compName);

        cy.intercept('POST', Cypress.env('adminApiUrl'), (req) => {
          if (
            req.body.query &&
            req.body.query.includes('search_certificates') &&
            req.body.variables &&
            req.body.variables.q === certificateNo
          ) {
            req.alias = 'searchCertificates';
          }
        }).as('searchCertificatesRequest');

        cy.get(this.certField, { timeout: 30000 }).should('be.visible').clear().type(certificateNo);
        cy.wait('@searchCertificatesRequest', { timeout: 30000 }).then((interception) => {
          expect(interception.response.statusCode).to.eq(200);

          cy.get(this.searchResults, { timeout: 30000 }).should('be.visible').contains(certificateNo).click();
        });

        cy.get(this.companyField, { timeout: 60000 }).should('be.visible').select(compName);
        cy.get(this.requestTypeDropdown, { timeout: 60000 }).should('be.visible').select('SLACK_CX');
        cy.get(this.createOrderBtn, { timeout: 60000 }).should('contain.text', 'Create order').click();
        cy.get(this.successBanner, { timeout: 60000 }).should('be.visible').contains('Order created!');
      });
    });
  }
}

export default AdminOrders;
