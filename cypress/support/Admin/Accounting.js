/// <reference types="cypress" />

class Accounting {
  constructor() {
    // Locators
    this.searchCompanyField = '[data-automation-id="search-input"]';
    this.companySelectField = '[data-automation-id="acc-filter-customer"]';
    this.companyOptionField = '.Select-menu';
    this.companyRow = '.debit-note-main-row.group_header.group_header__row--parent';
    this.tableBody = '#result_area';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.createCreditNoteButton = '.debit-note-create-debit-note-button';
    this.creditNoteModal = '.debit-note-modal-wrapper';
    this.sendCreditNoteHeader = '.supplier-return-switches';
    this.creditNoteCompanyList = '.debit-note-invoice-list-wrapper';
    this.sendCreditNoteButton = '[class^="sc-"] .btn.btn-wdc.ml-3.btn-wdc';
    this.invoiceList = '.debit-note-invoice-list-section';
    this.closeIcon = '[data-automation-id="close-btn"]';
    this.paymentMethod = '.form-control.select_override';
    this.creditNoteTab = '.supplier-return-switches';
    this.creditNoteType = '.select__type';
    this.refferCompany = '[data-automation-id="search-input"]';
    this.createReferralCreditNoteButton = '.sc-bdVaJa.fJylg.btn.btn-wdc.button-submit';
    this.radioButton = '[data-target-name="RADIO"]';
    this.popupMessage = '.go3958317564';
    this.selectCompanyField = '.Select-placeholder';
    this.companyNameSelection = '.Select-option';
    this.discountAmount = '[data-automation-id="Enter amount"';
    this.fetchingInvoice = '.sc-fAjcbJ';
  }

  createCreditNote(fixtureFileName, fixtureFileName1) {
    const defaultTimeout = 90000;
    cy.reload();
    cy.readFixtureFile(fixtureFileName).then((compData) => {
      const compName = compData[0].name;
      cy.log(`compName: ${compName}`);

      cy.readFixtureFile(fixtureFileName1).then((orderData) => {
        const orderNumber = orderData.orderNumber;
        cy.log(`orderNumber: ${orderNumber}`);

        cy.get(this.searchCompanyField, { timeout: defaultTimeout })
          .should('be.visible')
          .should('not.be.disabled')
          .type(compName, { force: true });
        cy.get(this.companySelectField, { timeout: defaultTimeout })
          .should('be.visible')
          .should('not.be.disabled')
          .click();
        cy.get(this.companyOptionField, { timeout: defaultTimeout }).should('be.visible').contains(compName).click();
        cy.get(this.companyRow, { timeout: defaultTimeout }).contains(compName).click();
        cy.intercept('https://px.ads.linkedin.com/wa/', { statusCode: 403 }).as('blockLinkedInAd');

        cy.get(this.tableBody)
          .should('be.visible')
          .find('tr', { timeout: defaultTimeout })
          .find('td', { timeout: defaultTimeout })
          .each(($td) => {
            if ($td.text().includes(orderNumber)) {
              expect($td.text()).to.contain(orderNumber);
              cy.wrap($td)
                .parents('tr')
                .within(() => {
                  cy.get(this.checkBox).click({ force: true });
                });
            }
          });
        cy.get(this.createCreditNoteButton, { timeout: defaultTimeout }).should('be.visible').click();
        cy.get(this.creditNoteModal, { timeout: defaultTimeout })
          .should('be.visible')
          .contains('Credit note created successfully');
        cy.get(this.closeIcon, { timeout: defaultTimeout }).should('be.visible').click();
        // cy.wait(3000);
        // cy.get(this.sendCreditNoteHeader, { timeout: defaultTimeout }).eq(0).should('be.visible').click();
        // cy.get(this.creditNoteCompanyList, { timeout: defaultTimeout })
        //   .should('be.visible')
        //   .contains(compName)
        //   .then(() => {
        //     cy.get(this.invoiceList).eq(1).should('be.visible').click();
        //   });
        // cy.get(this.sendCreditNoteButton, { timeout: defaultTimeout })
        //   .should('be.visible')
        //   .contains('Send Credit Note')
        //   .click();
      });
    });
  }
  allocateCreditNote(fixtureFile) {
    cy.readFixtureFile(fixtureFile).then((data) => {
      const invoiceNumber = data[0].invoice;

      cy.get(this.paymentMethod).eq(3).select('Credit Note');
      cy.get('h6', { timeout: 60000 }).eq(1).should('be.visible').and('have.text', 'List of Open Credit Notes');

      cy.get('td').each(($td) => {
        if ($td.text().trim() === invoiceNumber) {
          cy.wrap($td)
            .should('have.text', invoiceNumber)
            .closest('tr')
            .then(($row) => {
              // Click the checkbox
              cy.wrap($row).find('[role="checkbox"]').click();

              // Ensure checkbox is checked (aria-checked="true")
              cy.wrap($row).find('[role="checkbox"]').should('have.attr', 'aria-checked', 'true');

              // Extract amount from 3rd <td>
              cy.wrap($row)
                .find('td')
                .eq(2)
                .invoke('text')
                .then((amountText) => {
                  const amount = amountText.trim();

                  // Type into the input outside
                  cy.get('[data-automation-id="Enter valid amount"]').eq(0).should('be.visible').clear().type(amount);
                });
            });
        }
      });
    });
  }
  AccessOtherCreditNoteTab() {
    cy.get(this.creditNoteTab).contains('Other Credit Notes').click();
  }
  createReferalCreditNote(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const company = data[0].name;

      this.AccessOtherCreditNoteTab();
      cy.wait(1000);

      cy.get(this.creditNoteType).select('Referral Credit Note');
      cy.wait(1000);
      cy.get('h3', { timeout: 60000 }).eq(0).should('have.text', 'Create Referral Credit Note');
      cy.get(this.refferCompany).eq(0).click().type(company);
      cy.wait(2000);

      cy.get(this.selectCompanyField).click();
      cy.wait(3000);

      cy.get(this.selectCompanyField).eq(0).click();
      cy.get(this.refferCompany).eq(1).click().type('Nivoda');
      cy.wait(2000);
      cy.get(this.selectCompanyField).click();
      cy.wait(3000);

      cy.get(this.selectCompanyField).eq(0).click();
      cy.get(this.createReferralCreditNoteButton).click();
      cy.get(this.popupMessage).contains('Credit Note Created successfully');
    });
  }
  createECFCreditNote(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const company = data[0].name;

      this.AccessOtherCreditNoteTab();
      cy.wait(1000);

      cy.get(this.creditNoteType).select('Waive ECF');
      cy.wait(1000);
      cy.get('h3', { timeout: 60000 }).eq(0).should('have.text', 'Create ECF reversal Credit Note');
      cy.get(this.refferCompany).eq(0).click().type(company);
      cy.wait(2000);
      cy.get(this.selectCompanyField).click();
      cy.wait(3000);
      cy.get(this.companyNameSelection).click();
      cy.get(this.radioButton).eq(1).click();
      cy.get(this.createReferralCreditNoteButton).click();
      cy.get(this.popupMessage).contains('Credit Note Created successfully');
    });
  }
  createSelectedCreditNote(fixtureFileName, type, header) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const company = data[0].name;

      this.AccessOtherCreditNoteTab();
      cy.wait(1000);

      cy.get(this.creditNoteType).select(type);
      cy.wait(1000);
      cy.get('h3', { timeout: 60000 }).eq(0).should('have.text', header);
      cy.get(this.refferCompany).eq(0).click().type(company);
      cy.wait(2000);
      cy.get(this.selectCompanyField).click({ force: true });
      cy.wait(2000);
      cy.get(this.companyNameSelection).click({ force: true });
      cy.get(this.fetchingInvoice, { timeout: 60000 }).should('not.exist');
      if (type !== 'FP_PROMOTION') {
        cy.get(this.radioButton).eq(1).click();
      }

      cy.get(this.discountAmount).clear().type(10);
      cy.get(this.createReferralCreditNoteButton).click();
      cy.get(this.popupMessage, { timeout: 20000 }).contains('Credit Note Created successfully');
    });
  }
}

export default Accounting;
