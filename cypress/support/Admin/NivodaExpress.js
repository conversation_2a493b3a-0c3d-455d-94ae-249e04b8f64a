/// <reference types="cypress" />

class NivodaExpress {
  constructor() {
    // Locators
    this.spinner = '.fade-in';
    this.londonDestination = '[data-flag-id="GB"]';
    this.NewYorkDestination = '[data-flag-id="US"]';
    this.searchByCertificateNoToolTip = '[class^="sc-"]';
    this.certificateNumber = '.fbn_textarea';
    this.clearSearchButton = '.fbn_actions > [class^="sc-"][class*="btn-wdc"]';
    this.sourceType = '[class^="sc-"] .btn.btn-wdc.fbn_source_types__button';
    this.certificateNo = '.col_text__value.col_cert';
    this.toolTip = '.fbn_heading__text [class^="sc-"]';
    this.toolTipText = '.fbn_heading__text [class^="sc-"] [class^="sc-"]';
    this.startOverButton = '[class^="sc-"] .btn.btn-wdc.btn_default.btn-ghost';
    this.checkbox = '[data-automation-id="checkbox"]';
    this.selecAllCheckBox = '[data-automation-id="select-all-checkbox"]';
    this.sourceButton = '.section_right';
    this.stoneNotAvailableText = '.empty_cart_text_wrapper';
    this.deleteButton = '[data-automation-id="table-column"] > [class^="sc-"][class*="btn-wdc"][class*="btn-ghost"]';
    this.notFoundButton = '[class^="sc-"] .btn.btn-wdc.notFoundButtonConf';
    this.stoneBeingSourcedText = '.warn-br.warn_card__block';
    this.backButton = '[class^="sc-"] .btn.btn-wdc.btn_default.btn-ghost';
    this.expressRequestButton = '[class^="sc-"]  .btn.btn-wdc';
    this.successStoneBeingSourcesText = '.success-br.success_card__block';
    this.consignmentNo = '.consignmentNo';
    this.reviewAndConfirmRequestButton = '[class^="sc-"]  .btn.btn-wdc.ex-request-action-btn';
    this.processingStonesTab = '[data-status="PROCESSING_STONES"]';
    this.searchField = '[data-automation-id="input_box_multi-search-input"]';
    this.includeAllConsignmentPageButton =
      '[class^="sc-"]  .btn.btn-wdc.btn_default.btn-ghost.consignment-action.include-btn';
    this.nextButtonReviewConsignmentPage = '[class^="sc-"]  .btn.btn-wdc.rc__footer_action-btn';
    this.browseFileButton = '.uf_no-files';
    this.submitButton = '[class^="sc-"] .btn.btn-wdc.rc__footer_action-btn';
    this.row = '[data-automation-id="table-row"]';
    this.totalPrice = '.totalPrice';
    this.status = '[data-automation-id="table-column"]';
    this.generateExpressRequest = '.fbn_source_action__submit-box';
  }

  selectDestinationOffice(fixtureFileName, destinationOffice, inventoryIdentifier, source) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const certno = data[0].certNumber;
      const certno2 = data[1].certNumber;
      const certno3 = data[2].certNumber;
      const destination = destinationOffice === 'London' ? this.londonDestination : this.NewYorkDestination;
      cy.get(destination, { timeout: 29000 }).contains(destinationOffice).click({ force: true });
      cy.get(destination).eq(0).should('have.class', 'selectedOpt');
      cy.get(destination).contains(inventoryIdentifier).click({ force: true });
      cy.get(destination).should('have.class', 'selectedOpt');
      cy.get(this.sourceType).contains(source).click();
      cy.get(this.certificateNumber).type(`${certno},${certno2},${certno3}`);
    });
  }

  verifyClearButton(fixtureFileName, destinationOffice, inventoryIdentifier, source) {
    const destination = destinationOffice === 'London' ? this.londonDestination : this.NewYorkDestination;
    this.selectDestinationOffice(fixtureFileName, destinationOffice, inventoryIdentifier, source);
    cy.get(this.clearSearchButton).contains('Clear').should('be.enabled');
    cy.get(this.clearSearchButton).contains('Clear').click({ force: true });
    cy.get(destination).should('not.have.class', 'selectedOpt');
    cy.get(this.sourceType).should('not.have.class', 'selectedOpt');
    cy.get(this.clearSearchButton).contains('Clear').should('be.disabled');
  }

  verifSearchButton(fixtureFileName, destinationOffice, inventoryIdentifier, source) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const certno = data[0].certNumber;
      const certno2 = data[1].certNumber;
      const adminApiUrl = Cypress.env('adminApiUrl');

      this.selectDestinationOffice(fixtureFileName, destinationOffice, inventoryIdentifier, source);
      cy.intercept('POST', adminApiUrl).as('graphqlQuery');
      cy.get(this.clearSearchButton).contains('Search items').click();
      cy.wait('@graphqlQuery').then((interception) => {
        const requestBody = interception.request.body;
        expect(requestBody.query).to.include('search_fbn_items');
        expect(interception.response.statusCode).to.eq(200);
      });
      cy.contains('Source stones');
      cy.get(this.certificateNo).should('contain.text', certno2);
    });
  }

  verifyToolTip() {
    cy.get(this.toolTip).eq(2).trigger('mouseover');
    cy.get(this.toolTipText).should(
      'contain.text',
      'This tool enables you to mark stones within the platform as Nivoda Express using their certificate numbers. It will search the platform to check if the stones exist and you will be able to mark them as Nivoda Express in bulk.Use the form below to upload a certificate number list as .xlsx or .csv files, or input them manually using the text area below. Manual lists must be comma-separated.'
    );
  }

  verifyTextAreaIsExpandable() {
    cy.get(this.londonDestination).realHover({ force: true });
    cy.get(this.NewYorkDestination).realHover({ force: true });
    cy.get(this.certificateNumber).should('be.visible').and('have.css', 'height', '168px');
    cy.get(this.certificateNumber).then(($el) => {
      $el[0].style.height = '83px';
    });
    cy.wait(1000);
    cy.get(this.certificateNumber).should('have.css', 'height', '83px');
  }

  verifyStartOverButton() {
    cy.get(this.startOverButton).eq(0).click();
    cy.get(this.certificateNumber).should('be.visible');
  }
  verifyCheckBox() {
    cy.get(this.selecAllCheckBox).eq(0).click();
    cy.get(this.checkbox).should('have.class', 'checkbox_media').and('have.class', 'checkbox_checked');
  }
  verifySourceSelectedButton() {
    cy.get(this.checkbox)
      .should('have.class', 'checkbox_media')
      .and('have.class', 'checkbox_checked')
      .its('length')
      .then((count) => {
        cy.log('Number of checkboxes:', count);
        const totalcheckbox = count - 1;
        cy.get(this.sourceButton).children().eq(0).should('have.text', `  Source selected (${totalcheckbox})`);
      });
  }
  verifySourceAllButton() {
    return cy
      .get(this.checkbox)
      .should('have.class', 'checkbox_media')
      .and('have.class', 'checkbox_checked')
      .its('length')
      .then((count) => {
        cy.log('Number of checkboxes:', count);
        const totalcheckbox = count - 1;
        return cy
          .get(this.sourceButton)
          .children()
          .eq(1)
          .should('have.text', `  Source all (${totalcheckbox})`)
          .then(() => totalcheckbox);
      });
  }
  verifyDestinationOfficeButton(destinationofficetext) {
    cy.get(this.NewYorkDestination).should('contain.text', destinationofficetext);
  }
  verifyClearSearchButtonToBeEnabled() {
    cy.get(this.clearSearchButton).contains('Clear').should('be.enabled');
    cy.get(this.clearSearchButton).contains('Search items').should('be.enabled');
  }

  verifyStoneNotAvailableText(fixtureFileName, destinationOffice, inventoryIdentifier, source) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      this.selectDestinationOffice(fixtureFileName, destinationOffice, inventoryIdentifier, source);
      cy.get(this.clearSearchButton).contains('Search items').click();
      cy.contains('Source stones');
      cy.get(this.stoneNotAvailableText).should(
        'have.text',
        'No stones foundYour search yielded no results. Please check your certificate numbers and try again.'
      );
    });
  }
  validateDeleteButtonAndSourceAllButton() {
    this.verifySourceAllButton().then((totalcheckbox) => {
      cy.get(this.deleteButton)
        .eq(0)
        .click()
        .then(() => {
          const count = totalcheckbox - 1;
          cy.get(this.sourceButton).children().eq(0).should('have.text', `  Source selected (${count})`);
          cy.log('Updated count after deletion:', count);
        });
    });
  }
  invalidStoneTextVerify() {
    cy.get(this.certificateNumber).type(',12345678');
    cy.get(this.clearSearchButton).eq(0).click();
    cy.get(this.notFoundButton).should('have.text', 'Stone not found (1)  ');
  }
  beforeSourcingStoneTextVerify(office) {
    this.verifySourceAllButton().then((totalcheckbox) => {
      const count = totalcheckbox;
      cy.get(this.sourceButton).children().eq(0).should('have.text', `  Source selected (${count})`);
      cy.log('Updated count after deletion:', count);
      cy.get(this.sourceButton).children().eq(1).click();
      cy.get(this.stoneBeingSourcedText).should(
        'have.text',
        `The following ${count} items will be sourced as Nivoda Express to the ${office} office. Please confirm your selection below.`
      );
    });
  }
  backButtonOnSourcingPage() {
    cy.get(this.backButton).click();
    cy.get(this.NewYorkDestination).should('be.visible');
  }
  verifyBackgroundColorOfBeforeSourcingStoneText() {
    cy.get(this.stoneBeingSourcedText).should('be.visible');
    cy.get(this.stoneBeingSourcedText).should(($el) => {
      expect($el).to.have.css('background-color', 'rgb(255, 243, 205)');
    });
  }

  afterSourcingStoneTextVerify(office) {
    this.verifySourceAllButton().then((totalcheckbox) => {
      const count = totalcheckbox;
      cy.get(this.sourceButton).children().eq(0).should('have.text', `  Source selected (${count})`);
      cy.log('Updated count after deletion:', count);
      cy.get(this.sourceButton).children().eq(1).click();
      cy.get(this.stoneBeingSourcedText).should(
        'have.text',
        `TSuccess! The following ${count} were sourced as Nivoda Express to the ${office}. You may now finish.`
      );
    });
  }

  verifyBackgroundColorOfAfterSourcingStoneText() {
    cy.get(this.successStoneBeingSourcesText).should('have.css', 'background-color', 'rgb(209, 231, 221)');
  }
  sourceStone() {
    cy.get(this.generateExpressRequest).children().eq(0).click();
    this.verifyBackgroundColorOfAfterSourcingStoneText();
    cy.get(this.expressRequestButton).eq(0).click();
  }
  copyConsignmentNo() {
    cy.get(this.consignmentNo)
      .eq(0)
      .invoke('text')
      .then((consignmentText) => {
        const data = [
          {
            consignmentNo: consignmentText.trim()
          }
        ];
        cy.writeFile('cypress/fixtures/consignmentData.json', data);
      });
  }
  reviewandConfirmRequest(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const consignmentNumber = data[0].consignmentNo;
      console.log(`Consignment Number: ${consignmentNumber}`);
      let ConsignmentFound = false;
      cy.get(this.totalPrice, { timeout: 60000 }).eq(0).realHover({ position: 'bottomLeft' });
      cy.get(this.row).each(($row) => {
        if (ConsignmentFound) {
          return false;
        }
        const ConsignmentNumber = Cypress.$($row).find(this.consignmentNo).text();
        if (ConsignmentNumber.includes(consignmentNumber)) {
          ConsignmentFound = true;
          cy.log(`Certificate number ${consignmentNumber} found`);
          cy.wrap($row).find(this.reviewAndConfirmRequestButton).scrollIntoView().should('be.visible').click();

          cy.get(this.includeAllConsignmentPageButton).eq(0).click();
          cy.get(this.nextButtonReviewConsignmentPage).eq(1).click();

          cy.fixture('profileimage.png', 'base64').then((fileContent) => {
            const fileName = 'profileimage.png';
            const mimeType = 'image/png';
            const encoding = 'base64';

            cy.get('input[type="file"]').attachFile({
              fileContent,
              fileName,
              mimeType,
              encoding
            });
          });

          cy.get(this.submitButton).eq(2).should('be.enabled');
          cy.get(this.submitButton).eq(2).click();
          cy.url().should('include', 'nivoda-express/express-requests/processing-stones');
          cy.get(this.searchField).should('be.visible').should('be.enabled').click();
          console.log(`Typing consignment number: ${consignmentNumber}`);
          cy.get(this.searchField, { timeout: 100000 }).should('be.enabled');
          cy.get(this.searchField).type(consignmentNumber, { force: true });
          cy.contains('Search by consignment no').click();
          cy.get(this.consignmentNo).eq(0).should('include.text', consignmentNumber);
          cy.get(this.row)
            .eq(2)
            .then((row) => {
              cy.wrap(row)
                .find(this.status)
                .eq(7)
                .should(($status) => {
                  expect($status.text().trim()).to.be.oneOf(['Accepted', 'Qc pending']);
                });
            });
        }
      });
    });
  }
  queryExpressRequestItems(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((data) => {
      const certificateIdFromFixture = data[0].CertificateId;

      return cy
        .task('READFROMDB', {
          dbConfig: Cypress.config('DB'),
          sql: `SELECT * FROM "ExpressRequestItems" WHERE "CertificateId" = '${certificateIdFromFixture}' and status = 'ACTIVE'`
        })
        .then((itemsResult) => {
          if (itemsResult.rows.length > 0) {
            const expressRequestId = itemsResult.rows[0].ExpressRequestId;
            return cy
              .task('READFROMDB', {
                dbConfig: Cypress.config('DB'),
                sql: `SELECT consignment_number FROM "ExpressRequests" WHERE id = '${expressRequestId}' and status  <> 'REJECTED'`
              })
              .then((requestsResult) => {
                const consignmentNumber =
                  requestsResult.rows.length > 0 ? requestsResult.rows[0].consignment_number : null;
                cy.writeFile('cypress/fixtures/consignment.json', [{ consignmentNo: consignmentNumber }]);
              });
          }
        });
    });
  }
}

export default NivodaExpress;
