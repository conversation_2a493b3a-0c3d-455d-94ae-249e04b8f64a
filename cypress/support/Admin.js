/// <reference types="cypress" />
class Admin {
  constructor() {
    this.spinner = '.fade-in';
    this.selectStoneType = '#react-select-2--value';
    this.certificateNumber = '[data-automation-id="table-column"]';
    this.row = '[data-automation-id="table-row"]';
    this.confirmRTCMenu = '[data-automation-id="ellipsismenu"]';
    this.collectExpressStone = '[data-automation-id="action-section"]';
    this.stockid = '.order-stock-id';
    this.successmessage = '[class*="go3958317564"]';
    this.rtcTab = '#RTC';
    this.orderNumber = '[data-automation-id="order-overview"]';
    this.sidebarMenu = '#admin_right_sidebar';
    this.invocesSpinner = 'Loading Invoices';
    this.searchCompanyName = '[data-automation-id="search-input"]';
    this.selectCustomer = '[data-automation-id="acc-filter-customer"]';
    this.createInvoiceCheckbox = '[data-automation-id="checkbox-acc-list-item"]';
    this.createInvoiceButton = '[data-automation-id="create-invoice-btn"]';
    this.expandInvoice = '.group_header';
    this.invtosendTab = '#InvoicestoSend';
    this.supplierName = '#supplier';
    this.sendInvoicerow = '.group_header__row--parent';
    this.sendInvoicetoCustomer = '[class*="btn-wdc"][class*="ml-3"]';
    this.invoiceLoadingText = '[class*="empty__cart_text_description"]';
    this.awaitingPaymentTabOrder = '#Awaitingpayment';
    this.awaitingPaymentTabAccounting = '#AwaitingPayment';
    this.paymentamount = '[data-automation-id="Enter valid amount"]';
    this.orderNumberSelector = '[data-automation-id="table-column"]';
    this.statusDropdown = 'select[name="status"]';
    this.saveButton = '[class*="btn-wdc"][class*="action_save__btn"]';
    this.invoiceStatus = '[data-automation-id="invoice-status"]';
    this.invoiceNumber = '[class*="accounting_total__grand"]';
    this.searchInvoice = '[data-automation-id="search-input"]';
    this.markReciept = '[data-automation-id="mark-receipt"]';
    this.payButton = '[data-automation-id="pay-btn"]';
    this.datePicker = 'input[name="date"]';
    this.reactdatepicker = '.react-date-picker';
    this.monthInput = '.react-date-picker__inputGroup__month';
    this.dayInput = '.react-date-picker__inputGroup__day';
    this.yearInput = '.react-date-picker__inputGroup__year';
    this.diamondRequestsSpecialRequests = 'a[href="/admin/special-requests/diamond"]';
    this.tableHeaders = '[data-automation-id="tab-header"]';
    this.searchInput = '[data-automation-id="input_box_"]';
    this.actionBtnQc = '[class*="btn-wdc"][class*="btn-ghost"][class*="single_action"]';
    this.diamondqcPassBtn = '.aqc__pass-stone-btn';
    this.adminSearchBar = '[data-automation-id="admin-search-bar"]';
    this.searchLoading = '[class*="page_header__section"]';
    this.orderInvoiceNo = '[data-automation-id="order-item-invoice"]';
    this.spaceCodeField = '[class*="scw_select-scm"] .Select-arrow-zone';
    this.spaceCodeDropdown = '.Select-menu';
    this.expressSaveButton = '.scw_footer > [class*="btn-wdc"]';
    this.tagInfo = '[class*="tag_info__val"]';
    this.scanButton = '[class*="btn-wdc"][class*="cftl_action-btn"]';
    this.expressCheckBox = '[data-automation-id="checkbox"]';
    this.expressConsolidateStonesBtn = '[class*="btn-wdc"][class*="cft_consolidate_action-btn"]';
    this.expressNextButton = '.cft_footer > [class*="btn-wdc"]';
    this.rttTab = '#RTT';
    this.expressSubTab = '[data-action-id="NIVODA_EXPRESS"]';
    this.tagMenu = '[data-action-type="TAG"]';
    this.board = '.Select-input input';
    this.connectButton = '[data-scm-connection-state="CONNECT"]';
    this.connectedButton = '[data-scm-connection-state="DISCONNECT"]';
    this.manualBarcodeRadio = '[data-target-value="MANUAL"]';
    this.barcode = '#fbnBarcode';
    this.updateStone = '[data-scan-type="PRODUCT"]';
    this.updateCertificate = '[data-scan-type="CERTIFICATE"]';
    this.checkBox = '[data-automation-id="ui_checkbox"]';
    this.donebutton = '.sc-bdVaJa.kHYcQM.btn.btn-wdc';
    this.diamondQcBackbtn = '[class*="aqc__action-btn-icon"]';
    this.gemQcPassBtn = '.gems_aqc__pass-stone-btn';
    this.gemsQcBackBtn = 'gems_aqc__action-btn-icon__box';
    this.confirmGemsPassButtonBtn = '.qcm__footer--action-btn__pass';
    this.qcTab = '#QC';
    this.qcPassButton = '.aqc__footer-container__col > .aqc__btn';
    this.qcFailButton = '[class*="btn-wdc"][class*="gems_aqc__fail-stone-btn"]';
    this.confirmPassButton = '[class*="qcm__footer--action-btn__pass"]';
    this.menuQCButton = '[class*="btn-wdc"][class*="single_action"]';
    this.qcSpinner = '[class*="spinner-wrapper"]';
    this.readyToShipTab = '#RTS';
    this.backButtonQCPage = '[class*="btn-wdc"][class*="btn-ghost"]';
    this.searchBox = '[data-automation-id="input_box_"]';
    this.searchButton = '[data-automation-id="action--btn"]';
    this.closeSearchIcon = '[data-automation-id="input_box__clear_icon"]';
    this.refreshButton = '[data-automation-id="refresh-btn"]';
    this.customerNameSelection = '.Select-option';
  }
  selectStone(stoneType) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.selectStoneType, { timeout: 60000 }).should('be.visible').should('exist');
    cy.get(this.selectStoneType).eq(0).click();
    cy.contains(`${stoneType}`, { timeout: 60000 }).should('be.visible').should('exist').click();
  }
  confirmRTC(stoneType, fixtureFileName, fixtureFileName1, Status, StatusMessage, isExpress = false) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      cy.readFixtureFile(fixtureFileName1).then((data1) => {
        const orderFromFixture = data1.orderNumber;

        const stockidFromFixture = data[0].certNumber;
        console.log(stockidFromFixture);
        this.selectStone(stoneType);

        cy.get(this.certificateNumber, { timeout: 60000 }).should('be.visible');
        console.log('Certificate Number Field is Now Visible');

        let certificateFound = false;
        console.log('Finding Certificate Number');

        // Check if search box is present
        cy.get('body').then(($body) => {
          if ($body.find(this.searchBox).length > 0) {
            cy.log('Search Box is visible. Proceeding with typing the certificate number.');
            const adminApiUrl = Cypress.env('adminApiUrl');

            cy.intercept('POST', adminApiUrl, (req) => {
              if (
                req.body.operationName === 'getOrderItemsForRTC' ||
                req.body.operationName === 'getOrdersForPOAndOthers'
              ) {
                req.alias = 'getOrderItemsForRTC';
              } else {
                req.alias = 'podelayreasons';
              }
            });
            cy.get(this.searchBox, { timeout: 60000 }).should('not.be.disabled');
            cy.get(this.searchBox, { timeout: 60000 })
              .type('{selectall}{backspace}')
              .type(`${stockidFromFixture}{enter}`);

            cy.intercept('https://px.ads.linkedin.com/wa/', { statusCode: 403 }).as('blockLinkedInAd');
            cy.wait('@getOrderItemsForRTC').then(({ response }) => {
              expect(response.statusCode).to.eq(200);
              cy.wait(6000);
            });
            cy.get('body', { timeout: 60000 }).then(($body) => {
              const $rows = $body.find(this.row);

              if ($rows.length > 4 || $rows.length === 0) {
                cy.log('More than 1 or no row found. Searching again.');
                cy.get(this.refreshButton).click();
                cy.wait(4000);
                cy.get(this.searchBox, { timeout: 60000 }).should('not.be.disabled');
                cy.get(this.searchBox, { timeout: 60000 })
                  .clear()
                  .type('{selectall}{backspace}')
                  .type(`${orderFromFixture}{enter}`);
                cy.wait(4000);
              } else {
                cy.log('Row count is within expected range. Proceeding with the next steps.');
              }
            });

            cy.get(this.row, { timeout: 60000 })
              .should('be.visible')
              .then(($row) => {
                certificateFound = true;
                cy.log(`Certificate number ${stockidFromFixture} found`);

                if (isExpress) {
                  // Handle Express RTC logic and extract the tags
                  cy.get(this.certificateNumber, { timeout: 60000 }).eq(2).dblclick({ force: true });
                  cy.wrap($row)
                    .find(this.expressCheckBox)
                    .scrollIntoView()
                    .should('be.visible')
                    .click({ force: true })
                    .then(() => {
                      let tag1, tag2;

                      cy.wrap($row)
                        .find(this.tagInfo)
                        .eq(0)
                        .invoke('text')
                        .then((text1) => {
                          tag1 = text1.trim();
                          cy.wrap($row)
                            .find(this.tagInfo)
                            .eq(1)
                            .invoke('text')
                            .then((text2) => {
                              tag2 = text2.trim();

                              const tagData = {
                                tags: [tag1, tag2]
                              };
                              cy.writeFile('cypress/fixtures/spaceCodeTags.json', tagData);
                              cy.spaceCodeTagging('spaceCodeTags.json');
                              cy.get(this.collectExpressStone).should('not.be.disabled').contains('Collect').click();
                              cy.get(this.spaceCodeField, { timeout: 60000 }).should('be.visible').click();
                              cy.get(this.spaceCodeDropdown, { timeout: 60000 }).contains('Mock').click();
                              cy.get(this.expressSaveButton, { timeout: 60000 }).eq(1).should('be.visible').click();
                              cy.get(this.scanButton, { timeout: 60000 }).should('be.visible').contains('Scan').click();
                              cy.get(this.scanButton, { timeout: 60000 })
                                .should('be.visible')
                                .contains('Turn LEDs off')
                                .click();
                              cy.get(this.expressConsolidateStonesBtn)
                                .should('be.visible')
                                .contains('Scan & consolidate')
                                .click();
                              cy.get(this.expressNextButton, { timeout: 190000 })
                                .should('be.visible')
                                .should('not.be.disabled')
                                .contains('Next')
                                .click();

                              const adminApiUrl = Cypress.env('adminApiUrl');

                              cy.intercept('POST', adminApiUrl, (req) => {
                                if (req.body.operationName === 'updateNivodaExpressItems') {
                                  req.continue((res) => {
                                    expect(res.statusCode).to.eq(200);
                                  });
                                }
                              }).as('updateNivodaExpressItems');
                            });
                          cy.get(this.expressNextButton, { timeout: 190000 })
                            .should('be.visible')
                            .should('not.be.disabled')
                            .contains('Finish')
                            .click();
                          cy.wait('@updateNivodaExpressItems');
                        });
                    });
                } else {
                  // Handle regular RTC logic
                  cy.wrap($row)
                    .find(this.confirmRTCMenu, { timeout: 60000 })
                    .click()
                    .get('.at_menu')
                    .scrollIntoView()
                    .should('be.visible')
                    .contains(`${Status}`)
                    .click();
                  cy.get(this.successmessage, { timeout: 100000 }).should('be.visible').contains(`${StatusMessage}`);
                  cy.get(this.closeSearchIcon, { timeout: 100000 }).click();
                }
              });
          } else {
            // If search box is not present, proceed with finding by row
            cy.get(this.row)
              .each(($row) => {
                if (certificateFound) {
                  return false;
                }
                const certificateNumber = $row.find(this.certificateNumber).text();
                if (certificateNumber.includes(stockidFromFixture)) {
                  certificateFound = true;
                  cy.log(`Certificate number ${stockidFromFixture} found`);

                  if (isExpress) {
                    // Handle Express RTC logic and extract the tags
                    cy.get(this.certificateNumber, { timeout: 60000 }).eq(2).dblclick({ force: true });

                    cy.wrap($row)
                      .find(this.expressCheckBox)
                      .scrollIntoView()
                      .click({ force: true })
                      .then(() => {
                        cy.get(this.collectExpressStone).should('not.be.disabled').contains('Collect').click();
                        let tag1, tag2;

                        cy.wrap($row).as('currentRow');

                        cy.get('@currentRow')
                          .find(this.tagInfo)
                          .eq(0)
                          .should('exist')
                          .invoke('text')
                          .then((text1) => {
                            const tag1 = text1.trim();

                            cy.get('@currentRow')
                              .find(this.tagInfo)
                              .eq(1)
                              .should('exist')
                              .invoke('text')
                              .then((text2) => {
                                const tag2 = text2.trim();
                                const tagData = {
                                  tags: [tag1, tag2]
                                };
                                cy.writeFile('cypress/fixtures/spaceCodeTags.json', tagData);
                                cy.spaceCodeTagging('spaceCodeTags.json');

                                cy.get(this.spaceCodeField, { timeout: 60000 }).should('be.visible').click();
                                cy.get(this.spaceCodeDropdown, { timeout: 60000 }).contains('Mock').click();
                                cy.get(this.expressSaveButton, { timeout: 60000 }).eq(1).should('be.visible').click();
                                cy.get(this.scanButton, { timeout: 60000 })
                                  .should('be.visible')
                                  .contains('Scan')
                                  .click();
                                cy.get(this.scanButton, { timeout: 60000 })
                                  .should('be.visible')
                                  .contains('Turn LEDs off')
                                  .click();
                                cy.get(this.expressConsolidateStonesBtn)
                                  .should('be.visible')
                                  .contains('Scan & consolidate')
                                  .click();
                                cy.get(this.expressNextButton, { timeout: 90000 })
                                  .should('be.visible')
                                  .should('not.be.disabled')
                                  .contains('Next')
                                  .click();

                                const adminApiUrl = Cypress.env('adminApiUrl');

                                cy.intercept('POST', adminApiUrl, (req) => {
                                  if (req.body.operationName === 'updateNivodaExpressItems') {
                                    req.continue((res) => {
                                      expect(res.statusCode).to.eq(200);
                                    });
                                  }
                                }).as('updateNivodaExpressItems');
                                cy.get(this.expressNextButton, { timeout: 90000 })
                                  .should('be.visible')
                                  .should('not.be.disabled')
                                  .contains('Finish')
                                  .click();
                                cy.wait('@updateNivodaExpressItems');
                              });
                          });
                      });
                  } else {
                    // Handle regular RTC logic
                    cy.wrap($row)
                      .find(this.confirmRTCMenu)
                      .scrollIntoView()
                      .click()
                      .get('.at_menu')
                      .scrollIntoView()
                      .should('be.visible')
                      .contains(`${Status}`)
                      .click();
                    cy.wait(5000);
                    cy.get(this.successmessage, { timeout: 100000 }).contains('have.text', `${StatusMessage}`);
                  }
                }
              })
              .then(() => {
                if (!certificateFound) {
                  console.log(`Certificate number ${stockidFromFixture} not found`);
                  assert.isTrue(certificateFound, 'Certificate number not found');
                }
              });
          }
        });
      });
    });
  }
  // confirmRTC(stoneType, fixtureFileName, Status, StatusMessage, isExpress = false) {
  //   cy.readFixtureFile(fixtureFileName).then((data) => {
  //     const stockidFromFixture = data[0].certNumber;
  //     console.log(stockidFromFixture);
  //     this.selectStone(stoneType);

  //     cy.get(this.certificateNumber, { timeout: 60000 }).should('be.visible');
  //     console.log('Certificate Number Field is Now Visible');

  //     let certificateFound = false;
  //     console.log('Finding Certificate Number');

  //     cy.get('body').then(($body) => {
  //       if ($body.find(this.searchBox).length > 0) {
  //         cy.log('Search Box is visible. Proceeding with typing the certificate number.');

  //         const searchAndVerifySingleRow = () => {
  //           cy.get(this.searchBox).clear().type(`${stockidFromFixture}{enter}`);
  //           cy.wait(2000);
  //           cy.intercept('https://px.ads.linkedin.com/wa/', { statusCode: 403 }).as('blockLinkedInAd');
  //           cy.get(this.searchButton).dblclick();
  //           cy.wait(4000);

  //           cy.get(this.row, { timeout: 60000 }).then(($rows) => {
  //             if ($rows.length >= 1) {
  //               cy.log(`Found ${$rows.length} rows.`);
  //               certificateFound = true;
  //               if (isExpress) {
  //                 cy.wrap($rows).each(($row, index) => {
  //                   cy.wrap($row)
  //                     .find(this.certificateColumn) // Replace with the locator for the certificate column
  //                     .invoke('text')
  //                     .then((text) => {
  //                       if (text.trim() === stockidFromFixture) {
  //                         cy.wrap($row).as('foundRow'); // Save the matching row
  //                         return false; // Break out of the `each` loop
  //                       }
  //                     });
  //                 });
  //               } else {
  //                 cy.wrap($rows).as('foundRow'); // Save the first row for non-express
  //               }
  //             } else {
  //               cy.log('No rows found. Reloading the page and retrying...');
  //               cy.reload();
  //               searchAndVerifySingleRow();
  //             }
  //           });
  //         };

  //         searchAndVerifySingleRow();
  //       }
  //     });

  //     // Once the correct row is found, continue with your actions
  //     cy.get('@foundRow').then(($row) => {
  //       if (certificateFound) {
  //         console.log(`Certificate number ${stockidFromFixture} found`);

  //         if (isExpress) {
  //           cy.wrap($row)
  //             .find(this.expressCheckBox)
  //             .should('be.visible')
  //             .click({ force: true })
  //             .then(() => {
  //               let tag1, tag2;
  //               cy.wrap($row)
  //                 .find(this.tagInfo)
  //                 .eq(0)
  //                 .invoke('text')
  //                 .then((text1) => {
  //                   tag1 = text1.trim();
  //                   cy.wrap($row)
  //                     .find(this.tagInfo)
  //                     .eq(1)
  //                     .invoke('text')
  //                     .then((text2) => {
  //                       tag2 = text2.trim();

  //                       const tagData = {
  //                         tags: [tag1, tag2]
  //                       };
  //                       cy.writeFile('cypress/fixtures/spaceCodeTags.json', tagData);
  //                       cy.spaceCodeTagging('spaceCodeTags.json');
  //                       cy.get(this.collectExpressStone).should('not.be.disabled').contains('Collect').click();
  //                       cy.get(this.spaceCodeField, { timeout: 60000 }).should('be.visible').click();
  //                       cy.get(this.spaceCodeDropdown, { timeout: 60000 }).contains('Mock').click();
  //                       cy.get(this.expressSaveButton, { timeout: 60000 }).should('be.visible').click();
  //                       cy.get(this.scanButton, { timeout: 60000 }).should('be.visible').contains('Scan').click();
  //                       cy.get(this.scanButton, { timeout: 60000 })
  //                         .should('be.visible')
  //                         .contains('Turn LEDs off')
  //                         .click();
  //                       cy.get(this.expressConsolidateStonesBtn)
  //                         .should('be.visible')
  //                         .contains('Scan & consolidate')
  //                         .click();
  //                       cy.get(this.expressNextButton, { timeout: 190000 })
  //                         .should('be.visible')
  //                         .should('not.be.disabled')
  //                         .contains('Next')
  //                         .click();
  //                       cy.get(this.expressNextButton, { timeout: 190000 })
  //                         .should('be.visible')
  //                         .should('not.be.disabled')
  //                         .contains('Finish')
  //                         .click();
  //                     });
  //                 });
  //             });
  //         } else {
  //           cy.get(this.confirmRTCMenu, { timeout: 6000 })
  //             .should('be.visible')
  //             .click()
  //             .get('.at_menu')
  //             .should('be.visible')
  //             .contains(`${Status}`)
  //             .click();
  //           cy.get(this.successmessage, { timeout: 100000 }).should('be.visible').contains(`${StatusMessage}`);
  //           cy.get(this.closeSearchIcon, { timeout: 100000 }).click();
  //         }
  //       } else {
  //         console.log(`Certificate number ${stockidFromFixture} not found`);
  //         assert.isTrue(certificateFound, 'Certificate number not found');
  //       }
  //     });
  //   });
  // }

  accessRtcTab() {
    cy.get(this.rtcTab).click();
    cy.url().should('include', 'orders/ready-to-collect');
  }

  accessExpressRtcTab() {
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.get(this.tableHeaders, { timeout: 50000 }).should('be.visible').contains('Express RTC').click();
    cy.reload();
    cy.intercept('POST', adminApiUrl, (req) => {
      if (req.operationName === 'getExpressRTCOrders') {
      }
    }).as('getExpressRTCOrders');
    cy.url().should('include', 'admin/orders/express-ready-to-collect');
    cy.wait('@getExpressRTCOrders').its('response.statusCode').should('eq', 200);
    cy.wait(20000);
  }
  accessPOFromMenu() {
    cy.get(this.sidebarMenu).trigger('mouseover');
    cy.get(this.sidebarMenu).contains('Orders').scrollIntoView().invoke('show').click();
    cy.get(this.sidebarMenu).contains('Purchased').scrollIntoView().invoke('show').click({ force: true });
    cy.url().should('include', 'orders/purchase-order');
  }

  accessAccounting(accountingTab) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.sidebarMenu).trigger('mouseover');
    cy.get(this.sidebarMenu).contains('Accounting').scrollIntoView().invoke('show').click();
    cy.get(this.sidebarMenu).contains(accountingTab).scrollIntoView().invoke('show').click();
  }

  createInvoice(fixtureFileName) {
    cy.get(this.invoiceLoadingText, { timeout: 200000 }).should('not.exist');
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const customerName = data[0].name;
      cy.get(this.invoiceLoadingText, { timeout: 200000 }).should('not.exist');
      cy.get(this.searchCompanyName, { timeout: 200000 }).should('be.enabled');
      cy.get(this.searchCompanyName).click({ force: true }).type(customerName);
      cy.get(this.selectCustomer).click().type(customerName, { delay: 100 });
      cy.get(this.customerNameSelection).contains(customerName).click({ force: true });
      cy.wait(3000);
      cy.get(this.invoiceLoadingText, { timeout: 200000 }).should('not.exist');
      cy.get(this.expandInvoice).click();
      cy.get(this.createInvoiceCheckbox).click({ multiple: true });
      cy.get(this.createInvoiceButton).click();
      cy.wait(4000);
    });
  }
  accessInvoicesToSend() {
    cy.get(this.invtosendTab).click({ force: true });
    cy.wait(4000);
    cy.url().should('include', 'accounting/inv-to-send');
  }

  sendInvoice(fixtureFileName) {
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.intercept('POST', adminApiUrl, (req) => {
      if (req.body.query && req.body.query.includes('all_invoices')) {
        req.alias = 'graphqlInvoices';
      }
    });
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const customerName = data[0].name;
      const invoiceNumber = data[0].invoice;
      let companyFound = false;

      cy.get(this.sendInvoicerow, { timeout: 200000 }).should('be.visible');
      cy.get(this.sendInvoicerow)
        .each(($row) => {
          let supplierNameOrInvoice;

          if (
            fixtureFileName === 'PayInvoice.json' ||
            fixtureFileName === 'PayPartialInvoice.json' ||
            fixtureFileName === 'PayInvoice1.json'
          ) {
            supplierNameOrInvoice = $row.find(this.invoiceNumber).text();
            cy.log(`Invoice number from row: ${supplierNameOrInvoice}`);
          } else {
            supplierNameOrInvoice = $row.find(this.invoiceNumber).text();
            cy.log(`Supplier name from row: ${supplierNameOrInvoice}`);
          }
          if (
            (fixtureFileName === 'PayInvoice.json' || fixtureFileName === 'PayInvoice1.json') &&
            supplierNameOrInvoice.includes(invoiceNumber)
          ) {
            companyFound = true;
            cy.log(`Matching invoice found: ${supplierNameOrInvoice}`);
          } else if (supplierNameOrInvoice.includes(customerName)) {
            companyFound = true;
            cy.log(`Matching supplier name found: ${supplierNameOrInvoice}`);
          }

          if (companyFound) {
            cy.wrap($row)
              .find('.accounting_total')
              .eq(1)
              .click()
              .then(() => {
                cy.wrap($row)
                  .find(this.invoiceNumber)
                  .eq(1)
                  .invoke('text')
                  .then((invoiceNo) => {
                    const data = [
                      {
                        invoice: invoiceNo.trim()
                      }
                    ];
                    cy.task('writeToFile', {
                      filename: 'cypress/fixtures/Invoice.json',
                      data: data
                    });
                  });
                cy.wait(2000);
              });

            // Perform action after matching the row
            cy.get(this.sendInvoicetoCustomer).eq(0).click();

            // Wait for and validate GraphQL request
            cy.wait('@graphqlInvoices').then((interception) => {
              expect(interception.request.body.query).to.include('all_invoices');
              expect(interception.request.body.variables.status).to.eq('INVOICED');
              expect(interception.response.statusCode).to.eq(200);
            });

            return false;
          }
        })
        .then(() => {
          if (!companyFound) {
            throw new Error(
              `No matching entry found for invoice number: ${invoiceNumber} or customer name: ${customerName}`
            );
          }
        });
    });
  }

  accessAwaitingPaymentTab() {
    cy.get(this.awaitingPaymentTabOrder, { timeout: 200000 }).click();
    cy.url().should('include', 'orders/awaiting-payment');
  }
  accessAwaitingPaymentTabAccounting() {
    cy.get(this.awaitingPaymentTabAccounting, { timeout: 200000 }).click({ force: true });
  }
  orderStatuChangeFromAwaitingPayment(fixtureFileName, staus) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const orderNo = data.orderNumber;
      cy.get(this.orderNumberSelector, { timeout: 200000 }).should('be.visible');
      cy.get(this.searchBox).clear().type(`${orderNo}`);
      cy.get(this.searchButton).click();
      cy.get(this.orderNumberSelector, { timeout: 200000 })
        .contains(orderNo)
        .then(($el) => {
          cy.wrap($el).invoke('removeAttr', 'target').invoke('removeAttr', 'rel').click();
          cy.url().should('include', '/item');
        });
      cy.wait(6000);
      cy.get(this.statusDropdown).eq(0).select(`${staus}`);
      cy.get(this.saveButton).scrollIntoView().click();
      cy.get(this.invoiceStatus, { timeout: 200000 }).should('have.text', `${staus}`);
    });
  }

  verifyInvoices(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const invoiceNumber = data[0].invoice;
      cy.get(this.orderNumberSelector, { timeout: 200000 }).contains(invoiceNumber);
    });
  }
  searchInvoiceInAwaitingPaymentTab(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const fullInvoiceNumber = data[0].invoice;
      const invoiceNumber = fullInvoiceNumber.split('CN-')[0];
      cy.get(this.searchInvoice, { timeout: 60000 }).should('be.enabled');
      cy.get(this.markReciept, { timeout: 60000 }).should('be.visible');

      cy.get(this.searchInvoice).click({ force: true }).eq(0).type(invoiceNumber);
      cy.wait(3000);
      cy.get('.empty__cart_text_description-text').should('not.exist');
      cy.get(this.markReciept, { timeout: 60000 }).click();
      cy.contains('Invoice Payment');
    });
  }
  payInvoiceDate() {
    cy.get(this.datePicker)
      .invoke('attr', 'max')
      .then((maxDate) => {
        const maxDateObj = new Date(maxDate);
        maxDateObj.setDate(maxDateObj.getDate() - 1);
        const year = maxDateObj.getFullYear();
        const month = `0${maxDateObj.getMonth() + 1}`.slice(-2);
        const day = `0${maxDateObj.getDate()}`.slice(-2);
        cy.get(this.reactdatepicker).eq(0).click();
        cy.get(this.monthInput).eq(0).clear({ force: true }).type(month, { force: true });
        cy.get(this.dayInput).eq(0).clear({ force: true }).type(day, { force: true });
        cy.get(this.yearInput).eq(0).clear({ force: true }).type(year, { force: true });
      });
  }

  payInvoice(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      this.payInvoiceDate();

      const baseUrl = Cypress.config('baseUrl');
      const awaitingPaymentUrl = `${baseUrl}/admin/accounting/awaiting-payment`;

      cy.intercept('GET', awaitingPaymentUrl).as('awaitingPayment');
      cy.get(this.payButton).click();
      cy.wait('@awaitingPayment').its('response.statusCode').should('eq', 200);
      cy.wait(2000);
      cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    });
  }

  partialPayInvoice(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      cy.get(this.paymentamount).eq(0).clear().type('10');
      this.payInvoiceDate();
    });

    const baseUrl = Cypress.config('baseUrl');
    const awaitingPaymentUrl = `${baseUrl}/admin/accounting/awaiting-payment`;

    cy.intercept('GET', awaitingPaymentUrl).as('awaitingPayment');
    cy.get(this.payButton).click();
    cy.wait('@awaitingPayment').its('response.statusCode').should('eq', 200);
  }

  accessDiamondRequestsInSpecialRequests(subtab) {
    cy.get(this.diamondRequestsSpecialRequests).contains('Diamond Requests').click();
    cy.contains('button', subtab, { timeout: 28000 }).click().should('have.class', 'active');
  }
  searchInvoiceAndVerifyStatus(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const invoiceno = data[0].invoice;
      cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
      cy.get(this.adminSearchBar).should('be.visible').type(invoiceno);
      cy.contains(`Search '${invoiceno}' in Invoice`).click();
      cy.get(this.searchLoading, { timeout: 60000 }).should('not.have.text', 'Loading your data... please wait.');
      cy.get(this.orderInvoiceNo).eq(0).should('be.visible').should('have.text', invoiceno).click();
      cy.contains(`Invoice: ${invoiceno}`, { timeout: 60000 });
      cy.get(this.row, { timeout: 60000 }).contains('PAID');
    });
  }

  markStoneQcPass(fixtureFileName) {
    this.markQcPass(fixtureFileName, this.diamondqcPassBtn, this.diamondQcBackbtn);
  }

  markGemStoneQcPass(fixtureFileName) {
    this.markQcPass(fixtureFileName, this.gemQcPassBtn, this.gemsQcBackBtn);
  }

  // markQcPass(fixtureFileName, qcPassButton, backButton) {
  //   cy.readFixtureFile(fixtureFileName).then((data) => {
  //     const orderNumber = data.orderNumber;

  //     cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
  //     cy.get(this.tableHeaders, { timeout: 50000 }).should('be.visible').contains('QC').click();
  //     cy.get(this.searchInput, { timeout: 50000 }).should('be.visible').should('be.enabled').type(orderNumber);
  //     cy.wait(500);
  //     cy.get(this.actionBtnQc, { timeout: 50000 }).eq(0).should('be.visible').click();
  //     cy.get(qcPassButton, { timeout: 50000 }).should('be.visible').click();
  //     cy.get(this.selectStoneType)
  //       .eq(0)
  //       .then(($el) => {
  //         if ($el.text().includes('Gemstone')) {
  //           cy.get(this.confirmGemsPassButtonBtn, { timeout: 10000 }).should('be.visible').click();
  //         }
  //       });

  //     cy.get(this.successmessage).should('not.have.text', 'Cannot do QC, stone is not collected');
  //     cy.go(-1);
  //     cy.wait(4000);
  //   });
  // }
  markQcPass(fixtureFileName, qcPassButton, backButton) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const orderNumber = data.orderNumber;

      cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
      cy.get(this.tableHeaders, { timeout: 50000 }).should('be.visible').contains('QC').click();
      cy.get(this.searchInput, { timeout: 50000 }).should('be.visible').should('be.enabled').type(orderNumber);
      cy.wait(500);
      cy.get('body', { timeout: 60000 }).then(($body) => {
        const $rows = $body.find(this.row);

        if ($rows.length > 4 || $rows.length === 0) {
          cy.log('More than 1 or no row found. Searching again.');
          cy.get(this.refreshButton).click();
          cy.wait(4000);
          cy.get(this.searchBox, { timeout: 60000 }).should('not.be.disabled');
          cy.get(this.searchBox, { timeout: 60000 })
            .clear()
            .type('{selectall}{backspace}')
            .type(`${orderNumber}{enter}`);
          cy.wait(4000);
        } else {
          cy.log('Row count is within expected range. Proceeding with the next steps.');
        }
      });
      const attemptQcPass = () => {
        cy.get(this.actionBtnQc, { timeout: 50000 }).eq(0).should('be.visible').click();
        cy.get(qcPassButton, { timeout: 50000 }).should('be.visible').click();

        cy.get(this.selectStoneType)
          .eq(0)
          .then(($el) => {
            if ($el.text().includes('Gemstone')) {
              cy.get(this.confirmGemsPassButtonBtn, { timeout: 10000 }).should('be.visible').click();

              cy.get(this.successmessage, { timeout: 10000 }).then(($msg) => {
                if ($msg.text().includes('Cannot do QC, stone is not collected')) {
                  // Click Save button
                  cy.get('.sc-bdVaJa.fQjzdH.btn.btn-wdc.btn-ghost.gems_aqc__save-btn').eq(0).click();

                  // Retry the whole QC flow
                  attemptQcPass();
                }
              });
            }
          });
      };

      attemptQcPass(); // Initial attempt

      cy.go(-1);
      cy.wait(4000);
    });
  }

  accessRttTab() {
    cy.get(this.rttTab).click();
    cy.url().should('include', 'orders/ready-to-tag');
  }
  accessExpressTab() {
    cy.get(this.expressSubTab, { timeout: 180000 }).should('be.enabled');
    cy.get(this.expressSubTab, { timeout: 180000 }).click();
  }

  moveToQc(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const stockidFromFixture = data[0].certNumber;
      console.log(stockidFromFixture);

      cy.get(this.certificateNumber, { timeout: 60000 }).should('be.visible');
      cy.get(this.searchBox).then(($searchBox) => {
        if ($searchBox.is(':visible')) {
          // If search box is visible, perform search operations
          cy.get(this.searchBox).clear().type(`${stockidFromFixture}`);
          cy.wait(2000);
          cy.get(this.searchButton).click();
          cy.get(this.tagMenu).scrollIntoView().click();
        } else {
          // If search box is not visible, use each row to find the certificate
          let certificateFound = false;

          cy.get(this.row)
            .each(($row) => {
              if (certificateFound) {
                return false;
              }

              const certificateNumber = Cypress.$($row).find(this.certificateNumber).text();
              if (certificateNumber.includes(stockidFromFixture)) {
                certificateFound = true;
                cy.log(`Certificate number ${stockidFromFixture} found`);

                cy.wrap($row).find(this.tagMenu).scrollIntoView().click();
              }
            })
            .then(() => {
              if (!certificateFound) {
                console.log(`Certificate number ${stockidFromFixture} not found`);
                assert.isTrue(certificateFound, 'Certificate number not found');
              }
            });
        }
      });

      cy.get(this.board).type('Mock{enter}', { force: true });
      cy.get(this.connectButton).click();
      cy.get(this.connectedButton).should('be.visible');
      cy.wait(2000);
      cy.get(this.manualBarcodeRadio).eq(0).click({ force: true });
      const randomBarcode = Math.floor(1000000000 + Math.random() * 9000000000);
      cy.get(this.barcode).type(randomBarcode.toString());
      cy.get(this.updateStone).click();
      cy.get(this.updateCertificate, { timeout: 18000 }).should('be.enabled').click();
      cy.get(this.checkBox).click();
      cy.get(this.donebutton).eq(2).should('be.enabled').click();
      cy.get(this.donebutton, { timeout: 18000 }).should('not.exist');
      cy.get(this.successmessage, { timeout: 200000 }).should(
        'have.text',
        'Status update successful for 1 item(s) and moved to next stage from this tab'
      );
    });
  }

  accessQCTab() {
    cy.get(this.qcTab).click();
    cy.url().should('include', 'orders/ready-to-qc');
  }
  qcPassandVerifyRTCExpress(fixtureFileName) {
    this.accessExpressTab();
    this.verifyCertificateNoIsAvailable(fixtureFileName, this.menuQCButton);
    cy.get(this.qcSpinner, { timeout: 18000 }).should('not.exist');
    cy.get(this.qcPassButton).click();
    cy.get(this.successmessage).should('contain.text', `Stone Approved`);
    cy.get(this.qcPassButton).should('contain.text', `Passed`);
    cy.get(this.backButtonQCPage).eq(0).click();
    this.accessReadyToShipTab();
    this.verifyCertificateNoIsAvailable(fixtureFileName, this.certificateNumber);
  }

  verifyCertificateNoIsAvailable(fixtureFileName, tagMenuSelector) {
    cy.readFixtureFile(fixtureFileName).then((data) => {
      const stockidFromFixture = data[0].certNumber;
      console.log(stockidFromFixture);
      let certificateFound = false;
      cy.intercept('https://px.ads.linkedin.com/wa/', { statusCode: 403 }).as('blockLinkedInAd');
      cy.get(this.certificateNumber, { timeout: 60000 }).should('be.visible');
      cy.get(this.searchBox, { timeout: 60000 }).should('be.enabled');
      cy.get(this.searchBox).type(`${stockidFromFixture}`);
      cy.wait(2000);
      cy.get(this.searchButton).click();
      cy.wait(2000);
      cy.get(this.searchBox, { timeout: 60000 }).should('be.enabled');
      cy.get(3000);
      cy.get(this.row)
        .each(($row) => {
          if (certificateFound) {
            return false;
          }

          const certificateNumber = Cypress.$($row).find(this.certificateNumber).text();
          if (certificateNumber.includes(stockidFromFixture)) {
            certificateFound = true;
            cy.log(`Certificate number ${stockidFromFixture} found`);
            cy.wrap($row).find(tagMenuSelector).eq(0).scrollIntoView().click();
          }
        })
        .then(() => {
          if (!certificateFound) {
            console.log(`Certificate number ${stockidFromFixture} not found`);
            assert.isTrue(certificateFound, 'Certificate number not found');
          }
        });
    });
  }
  accessReadyToShipTab() {
    const adminApiUrl = Cypress.env('adminApiUrl');

    cy.intercept('POST', adminApiUrl, (req) => {
      if (req.operationName === 'getOrdersForPOAndOthers') {
      }
    }).as('getOrdersForPOAndOthers');
    cy.get(this.readyToShipTab).click();
    cy.url().should('include', 'orders/ready-to-ship');
    cy.wait('@getOrdersForPOAndOthers').its('response.statusCode').should('eq', 200);
  }
}

export default Admin;
