import 'cypress-file-upload';

class CustomJewelleryQuote {
  constructor() {
    this.requestaQuotebtn = '[data-automation-id="jewellery-mto-request-quote-button"]';
    this.addReferenceImagesbtn = '[data-automation-id="jewellery-mto-reference-images-file-upload-button"]';
    this.baseMatel = '[data-automation-id="select-baseMetal-input"]';
    this.yellowGoldOption = '[data-automation-id="menu-item-YELLOW_GOLD"]';
    this.matelPurity = '[data-automation-id="select-metalPurity-input"]';
    this.matelPurityOption = '[data-automation-id="menu-item-KT_12KT"]';
    this.ringWidth = '[data-automation-id="jewellery-mto-ring-width-input"]';
    this.ringThickness = '[data-automation-id="jewellery-mto-ring-thickness-input"]';
    this.ringFingerSize = '[data-automation-id="jewellery-mto-ring-finger-size"]';
    this.ringFingerSizeOption = '[data-option-index="6"]';
    this.maleeType = '[data-automation-id="jewellery-mto-select-melee-type"]';
    this.maleeColor = '[data-automation-id="jewellery-mto-select-melee-color"]';
    this.maleeClarity = '[data-automation-id="jewellery-mto-select-melee-clarity"]';
    this.maleeTypeOption = '#meleeType-option-1';
    this.maleeColorOption = '#meleeColor-option-1';
    this.maleeClarityOption = '#meleeClarity-option-4';
    this.innerProfile = '[data-automation-id="jewellery-mto-inner-profile-D_SHAPE"]';
    this.finish = '[data-automation-id="jewellery-mto-finish-option-MATTE"]';
    this.engraving = '[data-automation-id="jewellery-mto-engraving"]';
    this.hallmarking = '[data-automation-id="jewellery-mto-select-hallmark"]';
    this.addCenterStone = '[data-automation-id="jewellery-mto-center-stone-button"]';
    this.addSideStone = '[data-automation-id="jewellery-mto-side-stone-button"]';
    this.searchStonetxt = '[data-automation-id="jewellery-mto-stone-certificate-number-input"]';
    this.searchStonebtn = '[data-automation-id="jewellery-mto-stone-certificate-number-search-buton"]';
    this.selectStone = '#stones-catalog-list';
    this.confirmbtn = '[data-automation-id="jewellery-mto-stone-modal-confirm-button"]';
    this.budget = '[data-automation-id="jewellery-mto-estimated-budget"]';
    this.deliveryDeadline = '[placeholder="DD/MM/YYYY"]';
    this.internalOrderReference = '[data-automation-id="jewellery-mto-internal-order-reference"]';
    this.additionalComments = '[data-automation-id="jewellery-mto-additional-comments"]';
    this.fileupload = 'input[type="file"]';
  }

  requestQuote() {
    cy.get(this.requestaQuotebtn).click();

    cy.get(this.addReferenceImagesbtn, { timeout: 15000 }).should('be.visible').click();
    cy.get(this.fileupload, { timeout: 10000 })
      .should('exist')
      .selectFile('cypress/fixtures/ring.jpg', { force: true });
    cy.contains('ring.jpg').should('be.visible');

    cy.get(this.baseMatel).click();
    cy.get(this.yellowGoldOption).click();

    cy.get(this.matelPurity).click();
    cy.get(this.matelPurityOption).click();

    cy.get(this.ringWidth).type(10);
    cy.get(this.ringThickness).type(2);

    cy.get(this.ringFingerSize).click();
    cy.get(this.ringFingerSizeOption).click();

    cy.get(this.maleeType).click();
    cy.get(this.maleeTypeOption).should('be.visible').click();

    cy.get(this.maleeColor).click();
    cy.get(this.maleeColorOption).should('be.visible').click();

    cy.get(this.maleeClarity).click();
    cy.get(this.maleeClarityOption).should('be.visible').click();

    cy.get(this.innerProfile).click();
    cy.get(this.finish).click();

    cy.get(this.engraving).type('Automation engraving');
    cy.get(this.hallmarking).click();

    cy.get(this.addCenterStone, { timeout: 15000 }).should('be.visible').click();

    cy.get(this.searchStonetxt).should('be.visible').clear().type('GM-WNT-1682774137');
    cy.get(this.searchStonebtn).click();
    cy.wait(8000);
    cy.get(this.selectStone).should('be.visible').click();
    cy.get(this.confirmbtn).click();

    cy.get(this.addSideStone, { timeout: 15000 }).should('be.visible').click();

    cy.get(this.searchStonetxt).clear().type('7441498668');
    cy.get(this.searchStonebtn).click();
    cy.wait(8000);
    cy.get(this.selectStone).should('be.visible').click({ force: true });
    cy.get(this.selectStone).should('be.visible').click();

    cy.get(this.confirmbtn).click();

    cy.get(this.budget).type(5000);

    cy.get(this.deliveryDeadline).clear().type('09/09/2025');
    cy.get(this.internalOrderReference).clear().type('internal Order by Automation');
    cy.get(this.additionalComments).clear().type('Additional comments by Automation');
  }
}

export default CustomJewelleryQuote;
