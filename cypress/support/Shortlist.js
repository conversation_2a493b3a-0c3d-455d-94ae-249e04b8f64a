/// <reference types="cypress" />
const path = require('path');

class Shortlist {
  constructor() {
    // Locators
    this.productTypeDropdown = '[data-automation-id="select--inp"]';
    this.productOptions = '[data-automation-id="select-option"]';
    this.tableBody = '[data-automation-id="shortlist-table-container"]';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.headerBody = '[data-automation-id="header-body-right-side"]';
    this.addToCartButton = '[data-automation-id="add-to-cart-btn"]';
    this.shortlistTile = '[data-automation-id="header-title"]';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.holdModal = '[data-automation-id="ui_modal"]';
    this.newGroupButton = '[data-automation-id="group-topbar--heading"]';
    this.newGroupTextButton = '[data-automation-id="new-group-input-field"]';
    this.newGroupSubmitButton = '[data-automation-id="shortlist-create--left"]';
    this.groupTitle = '[data-automation-id="group-name"]';
    this.groupDropdown = '[data-automation-id="select-option"]';
    this.checkAllBox = '[data-automation-id="checkbox"]';
    this.copyToClipboard = '[data-automation-id="copy__to__clipboard"]';
    this.labIcon = '.bgmEC_label.lab';
    this.labModal = '.sc-ckVGcZ';
    this.meleeCaratsField = '.body--carats_pieces';
    this.selectQuantityButton = '[data-automation-id="melee_action__selectQuantityBtn"]';
    this.selectQuantityDescription = '.select-quantity-modal__description';
    this.quantityToggle = '.select-quantity-modal__input_toggle';
    this.quantityInputField = '.select-quantity-modal__input_field';
    this.deliveryNotes = '.select-quantity-modal__delivery_notes';
    this.applyChangesButton = '[data-automation-id="apply-changes-btn"]';
    this.editButton = '.edit-btn';
  }

  selectProductType(productTypeName) {
    cy.get(this.shortlistTile, { timeout: 190000 }).should('be.visible').contains('Shortlist');
    cy.get(this.productTypeDropdown).eq(1).should('be.visible').click({ force: true });
    cy.get(this.productOptions, { timeout: 60000 })
      .contains(productTypeName)
      .should('be.visible')
      .click({ force: true });
  }
  selectStock(fixtureFileName, isById) {
    cy.get(this.groupTitle, { timeout: 60000 })
      .contains('Default shortlist', { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible');
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.checkBox).eq(1).should('be.visible').click();
  }

  selectStockByStockId() {
    this.selectStock();
  }

  selectStockByStockDetails(fixtureFileName) {
    this.selectStock(fixtureFileName, false);
  }

  selectStockByStockIdOrStockDetails() {
    cy.get(this.groupTitle, { timeout: 60000 })
      .contains('Default shortlist', { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .then(() => {
        cy.get('body').then(($body) => {
          if ($body.find(this.productTypeDropdown).length > 0) {
            cy.get(this.productTypeDropdown).then(($dropdown) => {
              if ($dropdown.text().includes('Gemstones')) {
                this.selectStockByStockDetails();
              } else {
                this.selectStockByStockId();
              }
            });
          } else {
            this.selectStockByStockId();
          }
        });
      });
  }

  removeFromShortlistButtonVerify(productTypeName, fixtureFileName) {
    this.selectProductType(productTypeName);
    this.selectStockByStockIdOrStockDetails(fixtureFileName);

    cy.get(this.headerBody)
      .contains('Remove', { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .should('be.enabled')
      .click();
  }

  addToCartFromShortlist(productTypeName) {
    this.selectProductType(productTypeName);
    this.selectStockByStockIdOrStockDetails();
    cy.get(this.addToCartButton)
      .contains('Add to cart', { timeout: 160000 })
      .scrollIntoView()
      .should('be.visible')
      .should('be.enabled')
      .click({ force: true });
  }

  verifyAddToCartBlockedForOnHoldItem(productTypeName, stockId) {
    this.addToCartFromShortlist(productTypeName, stockId);
    cy.get(this.holdModal, { timeout: 60000 })
      .should('be.visible')
      .contains('Following stones from your shortlist are on hold and these will not be added in the cart.');
  }

  createShortlistGroup(groupName) {
    cy.get(this.newGroupButton, { timeout: 60000 }).should('be.visible').contains('New group').click({ force: true });
    cy.get(this.newGroupTextButton).should('be.visible').click().type(groupName, { force: true });
    cy.get(this.newGroupSubmitButton).should('be.visible').contains('Submit').click({ force: true });
    cy.get(this.groupTitle, { timeout: 60000 })
      .contains(groupName, { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible');
  }

  addProductToShortlistGroup(fixtureFileName) {
    this.selectStockByStockIdOrStockDetails(fixtureFileName);

    cy.get(this.headerBody)
      .contains('Move to', { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .click({ force: true });
    cy.get(this.groupDropdown).eq(1).should('be.visible').click({ force: true });
  }

  exportShortlist(productTypeName) {
    this.selectProductType(productTypeName);
    cy.get(this.copyToClipboard, { timeout: 60000 }).eq(0).click();
    cy.get(this.checkAllBox, { timeout: 60000 }).eq(0).click();
    cy.get(this.headerBody)
      .contains('Export', { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .click({ force: true });
    cy.wait(2000);
    cy.getDownloadedFileName().then((fileName) => {
      const baseName = Cypress._.trimEnd(fileName, path.extname(fileName));
      cy.log('Downloaded file name:', baseName);
      cy.task('convertXlsxToJson', `cypress/downloads/${baseName}.xlsx`).then(() => {
        cy.readFixtureFile(`cypress/fixtures/${baseName}.json`).then((data) => {
          data.forEach((item) => {
            cy.assertValueCopiedToClipboard(item.StockId);
          });
        });
      });
    });
  }

  certIdNotVisibleAssertion() {
    cy.get(this.labIcon)
      .eq(0)
      .should('be.visible')
      .realHover()
      .then(() => {
        cy.get(this.labModal).contains('Hidden');
      });
  }

  verifyMeleeDetails(productTypeName, fixtureFileName, carats) {
    this.selectProductType(productTypeName);
    this.selectStockByStockIdOrStockDetails(fixtureFileName);

    cy.get(this.meleeCaratsField, { timeout: 5000 }).should('be.visible').contains(carats);
  }

  verifyShortlistedMeleeDetails(productTypeName, carats) {
    this.selectProductType(productTypeName);
    cy.get(this.meleeCaratsField, { timeout: 5000 }).should('be.visible').contains(carats);
  }

  verifyUpdateCartModalForMelee() {
    cy.get(this.editButton, { timeout: 5000 }).should('be.visible').click();
    cy.contains('Update Melee');
    cy.contains('Change the quantity of melee you have in your cart');
    cy.get(this.selectQuantityDescription, { timeout: 60000 }).should('be.visible');
    cy.get(this.quantityToggle, { timeout: 5000 }).should('be.visible');
    cy.get(this.quantityInputField, { timeout: 5000 }).should('be.visible');
    cy.get(this.deliveryNotes, { timeout: 5000 }).should('be.visible');
    cy.get(this.applyChangesButton, { timeout: 60000 }).should('be.enabled');
  }

  verifyAddToCartDisabled() {
    cy.get(this.addToCartButton)
      .contains('Add to cart', { timeout: 160000 })
      .scrollIntoView()
      .should('be.visible')
      .should('be.disabled');
  }
}

export default Shortlist;
