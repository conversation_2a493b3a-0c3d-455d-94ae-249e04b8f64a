/// <reference types="cypress" />

class SharingOptions {
  constructor() {
    // Locators
    this.ShareButton = '[data-automation-id="share-button"]';
    this.whatsAppButton = '[data-target-id="WHATSAPP"]';
    this.emailButton = '[data-target-id="EMAIL"]';
    this.copyInfoButton = '[data-target-id="COPY_INFO"]';
    this.copyImageLink = '[data-target-id="COPY_IMAGE"]';
    this.copyVideoLink = '[data-target-id="COPY_VIDEO"]';
    this.downloadImage = '[data-target-id="DOWNLOAD_IMAGE"]';
    this.downloadVideo = '[data-target-id="DOWNLOAD_VIDEO"]';
    this.notificationWrapper = '.upload__progress-info ';
    this.spinner = '.fade-in';
    this.container = '[data-automation-id="product-container"]';
    this.productShareButton = '[data-target-id="SHARE_VIA"]';
    this.shareListViewButton = '.share__btn';
    this.listViewButton = '[data-testid="search-melee-list-view-btn"]';
    this.arrowlistButton = '.list_button';
    this.title = '[data-automation-id="diamond-title"]';
    this.notificationBanner = '.upload__progress-info ';
    this.videodownloadDialogBox = '.content-area';
    this.downloadVideoButton = '[data-target-id="DOWNLOAD_VIDEO"]';
    this.closeButton = '[data-automation-id="close-btn"]';
    this.diamondDetailBody = '.diamond-detail-body';
    this.emailCust = 'span.diamond_download__action--label[data-target-id="EMAIL"]';
    this.emailCustCta = 'span.customize-details-btn-label';
    this.whatsappCust = 'span.diamond_download__action--label[data-target-id="WHATSAPP"]';
  }
  verifyShareButton() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.ShareButton).eq(0).should('be.visible');
  }
  verifyShareButtonInListView() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.listViewButton).click();
    cy.get(this.arrowlistButton).eq(0).click();
    cy.get(this.shareListViewButton).eq(0).should('be.visible');
  }
  verifyShareButtonInListViewNotVisibleForGemstone() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.listViewButton).click();
    cy.get(this.shareListViewButton).should('not.exist');
  }
  verifyShareButtonInListViewNotExistForMelee() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.listViewButton).should('be.visible');
    cy.get(this.listViewButton).click();
    cy.get(this.shareListViewButton).should('not.exist');
  }
  verifyShareButtonInProduct() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.title, { timeout: 60000 }).eq(0).click();
    cy.get(this.diamondDetailBody, { timeout: 10000 }).invoke('text').should('not.be.empty');
    cy.get(this.productShareButton).should('be.visible');
  }

  verifyShareButtonNotVisibleInProduct() {
    cy.get(this.spinner, { timeout: 5000 }).eq(0).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.title, { timeout: 60000 }).eq(0).click();
    cy.get(this.diamondDetailBody, { timeout: 10000 }).invoke('text').should('not.be.empty');
    cy.get(this.productShareButton).should('not.exist');
  }
  verifyShareButtonNotVisibleInProductForGemStoneandMelee() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.title, { timeout: 60000 }).eq(0).click();
    cy.get(this.diamondDetailBody, { timeout: 10000 }).invoke('text').should('not.be.empty');
    cy.get(this.productShareButton).should('not.exist');
  }

  verifyShareButtonNotVisibleForMelee() {
    cy.get(this.spinner).should('not.be.visible');
    cy.get(this.container, { timeout: 60000 }).eq(0).should('be.visible');
    cy.get(this.ShareButton).should('not.exist');
  }
  verifyWhatsappSharingOption() {
    this.verifyShareButton();
    cy.get(this.ShareButton, { timeout: 60000 }).eq(0).click();
    cy.get(this.whatsAppButton, { timeout: 60000 }).should('be.visible');
  }
  verifyWhatsappCust() {
    cy.get(this.whatsappCust).should('be.visible').click();
    cy.contains(this.emailCustCta, 'Customise details to share').should('be.visible').click();
  }
  verifyWhatsappSharingOptionNotVisibleForGemstone() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.whatsAppButton).should('not.exist');
  }
  verifyemailSharingOption() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.emailButton).should('be.visible');
  }
  verifyemailCust() {
    cy.get(this.emailCust).should('be.visible').click();
    cy.contains(this.emailCustCta, 'Customise details to share').should('be.visible').click();
  }
  verifyemailSharingOptionNotAvailableForGemstone() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.emailButton).should('not.exist');
  }
  verifycopyinfoButton() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.copyInfoButton).should('be.visible');
    cy.get(this.copyInfoButton).eq(0).click();
    cy.get(this.notificationBanner).should('contain.text', 'Info Copied');
  }
  verifycopyinfoButtonNotVisibleForGemstone() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.copyInfoButton).should('not.exist');
  }
  verifyCopyImageLinkButton() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.copyImageLink).eq(1).should('be.visible');
    cy.window().then((win) => {
      cy.stub(win.navigator.clipboard, 'writeText').as('clipboardWrite');
    });
    cy.get(this.copyImageLink).eq(1).click({ force: true });

    cy.get(this.notificationBanner, { timeout: 60000 }).should('contain.text', 'Image link copied.');
    cy.get('@clipboardWrite').should('have.been.calledWith', Cypress.sinon.match.string);
  }

  verifyCopyVideoLinkButton() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.get(this.copyVideoLink).eq(2).should('be.visible');
    cy.window().then((win) => {
      cy.stub(win.navigator.clipboard, 'writeText').as('clipboardWrite');
    });
    cy.get(this.copyVideoLink).eq(2).click();
    cy.get(this.notificationBanner, { timeout: 60000 }).should('contain.text', 'Video link copied.');
    cy.get('@clipboardWrite').should('have.been.calledWith', Cypress.sinon.match.string);
  }
  verifyDownloadImageButton() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();

    cy.get(this.downloadImage).eq(0).should('be.visible').click();
    cy.get(this.notificationWrapper, { timeout: 60000 }).should('contain.text', 'Image downloaded');
  }

  verifyDownloadVideoButton() {
    this.verifyShareButton();
    cy.get(this.ShareButton).eq(0).click();
    cy.intercept('POST', '**/graphql', (req) => {
      if (req.body.operationName === 'GetVideoLink' || 'GetGemsVideoLink') {
        req.alias = 'getVideoLink';
      }
    });
    cy.get(this.downloadVideo).eq(0).should('be.visible').click();
    cy.wait('@getVideoLink').its('response.statusCode').should('eq', 200);

    cy.get(this.videodownloadDialogBox, { timeout: 60000 }).should(($el) => {
      const text = $el.text();
      expect(
        text.includes('Generating link') || text.includes('Video link generated, click on download to save the video.')
      ).to.be.true;
    });
    cy.get(this.downloadVideoButton).eq(3).click();
    cy.get(this.closeButton).click();
    cy.get(this.notificationWrapper, { timeout: 60000 }).should('contain.text', 'Video downloaded successfully');
  }
}

export default SharingOptions;
