/// <reference types="cypress" />
import { faker } from '@faker-js/faker';
const fs = require('fs');

class SignUp {
  constructor() {
    // Locators
    this.signUpButton = '[data-automation-id="signup-btn"]';
    this.signUpInputField = '[data-automation-id="signup-input-field"]';
    this.continueButton = '[data-automation-id="continue-btn"]';
    this.phoneField = '[data-automation-id="phone-input"]';
    this.termsCheckBox = '[data-automation-id="checkbox"]';
    this.errorPopUpModal = '#alert-dialog-description';
    this.captchaBox = '.recaptcha-checkbox-checkmark';
    this.businessTypeField = '#business-type';
    this.businessRoleField = '#business-role-field';
    this.companyCountryField = '#company-country';
    this.dropdownMenu = 'div[id^="react-select"]';
    this.dropdownMenuOption = '[id$="option-0"]';
    this.stateField = '#registered-state';
    this.setupCompleteModal = '.MuiContainer-root';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.otpField = '.MuiInputBase-input.MuiOutlinedInput-input.css-2haxq0';
    this.addressHeading = '.MuiAlert-message';
    this.supplierSelectionBtn = ':nth-child(2) > .MuiPaper-root > .MuiCardContent-root';
    this.stockCountInput = '#stock-count';
    this.firstSwitchInput =
      '.css-1vogve2 > .MuiGrid2-root > :nth-child(1) > .MuiButtonBase-root > .PrivateSwitchBase-input';
    this.secondSwitchInput =
      '.css-1mhzic1 > .MuiGrid2-root > :nth-child(1) > .MuiButtonBase-root > .PrivateSwitchBase-input';
    this.firstCardContent = ':nth-child(1) > .MuiCardContent-root';
    this.termsCheckbox = '.PrivateSwitchBase-input.css-j8yymo';
    this.completeRegistrationButton = '.MuiContainer-root > .css-8atqhb > .MuiButtonBase-root';
  }

  selectDropdownOption(selector) {
    cy.get(selector)
      .click()
      .then(() => {
        cy.get(this.dropdownMenuOption, { timeout: 5000 }).should('be.visible').first().click();
      });
  }

  fillOTP() {
    cy.getOTP(new Date()).then((otpCode) => {
      const otpDigits = otpCode.split('');

      cy.get(this.otpField, { timeout: 50000 })
        .should('have.length', otpDigits.length)
        .each(($el, index) => {
          cy.wrap($el).should('not.be.disabled').type(otpDigits[index], { delay: 1000 });
        })
        .then(() => {
          cy.get(this.otpField).each(($el, index) => {
            cy.wrap($el).should('have.value', otpDigits[index]);
          });
          cy.log('OTP filled correctly');
        });
    });

    cy.wait(4000);
  }

  solveCaptcha() {
    cy.get('iframe[title="reCAPTCHA"]')
      .should('exist')
      .then(($iframe) => {
        const iframe = $iframe.get(0);
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        cy.wrap(iframeDoc).find(this.captchaBox).should('be.visible').click({ force: true });
      });
    cy.wait(5000);
  }

  clickSignUpButton() {
    cy.get(this.signUpButton).should('be.visible').contains('Sign up for free').click();
    cy.url().should('include', '/register');
  }

  fillSignUpForm(email) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const password = 'Nivoda123';
    const phoneNumber = faker.phone.number('92344#######');

    cy.get(this.continueButton).should('be.visible').click();
    cy.get(this.signUpInputField).eq(0).should('be.visible').type(firstName);
    cy.get(this.signUpInputField).eq(1).should('be.visible').type(lastName);
    cy.get(this.signUpInputField).eq(2).should('be.visible').type(email);
    cy.get(this.signUpInputField).eq(3).should('be.visible').type(email);
    cy.get(this.signUpInputField).eq(4).should('be.visible').type(password);
    cy.get(this.signUpInputField).eq(5).should('be.visible').type(password);
    cy.get(this.phoneField).should('be.visible').clear().type(phoneNumber);
  }

  fillCompanyForm() {
    const companyName = faker.company.name();

    cy.contains("Let's talk about your business.", { timeout: 3000 });
    cy.get(this.signUpInputField).eq(0).should('be.visible').click().type(companyName);
    this.selectDropdownOption(this.businessTypeField);
    this.selectDropdownOption(this.businessRoleField);
    this.selectDropdownOption(this.companyCountryField);
  }

  fillAddressForm() {
    const zipCode = faker.address.zipCode();
    const address = faker.address.streetAddress();
    const city = faker.address.city();

    cy.contains("We need your company's registered address.", { timeout: 30000 });
    cy.get(this.addressHeading, { timeout: 5000 }).should('contain.text', 'New address will require verification');
    cy.get(this.signUpInputField).eq(2).should('be.visible').type(zipCode);
    cy.get(this.signUpInputField).eq(1).should('be.visible').type(address);
    cy.get(this.signUpInputField).eq(4).should('be.visible').type(city);
    this.selectDropdownOption(this.stateField);
  }

  agreeToTerms() {
    cy.get(this.termsCheckBox).should('be.visible').click();
  }

  continueSignUp() {
    cy.contains('Continue').click();
    cy.wait(4000);
  }

  signUpBuyerWithExistingEmail(emailFilePath) {
    cy.fixture(emailFilePath).then((emailData) => {
      const email = emailData[0].email;

      this.clickSignUpButton();
      this.fillSignUpForm(email);
      this.agreeToTerms();
      this.continueSignUp();
      cy.get(this.errorPopUpModal, { timeout: 50000 })
        .should('be.visible')
        .contains('This email address is already linked to an account, please log in or change your password');
    });
  }

  signUpBuyer() {
    const email = faker.internet.exampleEmail();
    this.clickSignUpButton();
    this.fillSignUpForm(email);
    this.agreeToTerms();
    this.continueSignUp();
    this.fillOTP();
    this.solveCaptcha();
    this.continueSignUp();
    this.fillOTP();
    this.continueSignUp();
    this.fillCompanyForm();
    this.continueSignUp();
    this.fillAddressForm();
    this.continueSignUp();
    cy.get(this.setupCompleteModal, { timeout: 50000 }).should('be.visible').contains('You are now all set to go!');
  }

  signUpBuyerWithoutaddress() {
    const email = faker.internet.exampleEmail();
    console.log(email);
    cy.writeFile('cypress/fixtures/emailData.json', [{ email: email }]);
    this.clickSignUpButton();
    this.fillSignUpForm(email);
    this.agreeToTerms();
    this.continueSignUp();
    this.fillOTP();
    this.solveCaptcha();
    this.continueSignUp();
    this.fillOTP();
    this.continueSignUp();
    this.fillCompanyForm();
    this.continueSignUp();
  }
  verifyAddressUrl() {
    cy.url().should('include', 'register/set-address');
  }

  signUpSupplier() {
    const email = faker.internet.exampleEmail();

    this.clickSignUpButton();

    cy.get(this.supplierSelectionBtn, { timeout: 10000 }).should('be.visible').click();

    this.fillSignUpForm(email);
    this.agreeToTerms();
    this.continueSignUp();
    this.fillOTP();
    this.solveCaptcha();
    this.continueSignUp();
    this.fillOTP();
    this.continueSignUp();
    this.fillCompanyForm();
    this.continueSignUp();
    this.fillSupplierStockForm();
    this.continueSignUp();
    this.acceptSupplierConditions();
  }

  fillSupplierStockForm() {
    cy.contains('Thank you! Now about your stock, and how to upload it.', { timeout: 10000 }).should('be.visible');
    cy.get(this.stockCountInput, { timeout: 10000 }).should('be.visible').type('500-1000');
    cy.contains('500-1000', { timeout: 5000 }).click();
    cy.get(this.firstSwitchInput, { timeout: 5000 }).click();
    cy.get(this.secondSwitchInput, { timeout: 5000 }).click();
    cy.get(this.firstCardContent, { timeout: 5000 }).click();

    this.continueSignUp();
  }

  acceptSupplierConditions() {
    cy.contains('Please read and accept the supplier terms & conditions.', { timeout: 10000 }).should('be.visible');
    cy.get(this.termsCheckbox, { timeout: 5000 }).click();
    cy.intercept('POST', Cypress.env('supplierApiUrl'), (req) => {
      if (req.body.query && req.body.query.includes('accept_terms')) {
        req.alias = 'acceptTerms';
      }
    }).as('acceptTerms');
    cy.get(this.completeRegistrationButton, { timeout: 10000 }).should('have.text', 'Complete registration').click();
    cy.wait('@acceptTerms', { timeout: 130000 }).then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
    });
    cy.contains('Done! Support will contact you shortly.', { timeout: 10000 }).should('be.visible');
  }
}

export default SignUp;
